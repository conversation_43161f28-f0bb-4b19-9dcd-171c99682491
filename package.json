{"name": "PSPBACKEND", "version": "1.0.2", "description": "PSP backend", "main": "server.js", "scripts": {"local-maildev": "maildev -o", "local-admin": "node --watch ./ADMIN -r dotenv/config ./ADMIN/server.js dotenv_config_path=./ADMIN/.env.local", "dev-admin": "node --watch-r dotenv/config ./ADMIN/server.js dotenv_config_path=./ADMIN/.env.dev", "staging-admin": "node --watch -r dotenv/config ./ADMIN/server.js dotenv_config_path=./ADMIN/.env.staging", "uat-admin": "node --watch -r dotenv/config ./ADMIN/server.js dotenv_config_path=./ADMIN/.env.uat", "live-admin": "node --watch -r dotenv/config ./ADMIN/server.js dotenv_config_path=./ADMIN/.env.live", "local-terminal": "node --watch ./TERMINAL -r dotenv/config ./TERMINAL/server.js dotenv_config_path=./TERMINAL/.env.local", "dev-terminal": "node --watch -r dotenv/config ./TERMINAL/server.js dotenv_config_path=./TERMINAL/.env.dev", "staging-terminal": "node --watch -r dotenv/config ./TERMINAL/server.js dotenv_config_path=./TERMINAL/.env.staging", "uat-terminal": "node --watch -r dotenv/config ./TERMINAL/server.js dotenv_config_path=./TERMINAL/.env.uat", "live-terminal": "node --watch -r dotenv/config ./TERMINAL/server.js dotenv_config_path=./TERMINAL/.env.live", "local-business": "node --watch ./BUSINESS -r dotenv/config ./BUSINESS/server.js dotenv_config_path=./BUSINESS/.env.local", "dev-business": "node --watch -r dotenv/config ./BUSINESS/server.js dotenv_config_path=./BUSINESS/.env.dev", "staging-business": "node --watch -r dotenv/config ./BUSINESS/server.js dotenv_config_path=./BUSINESS/.env.staging", "uat-business": "node --watch -r dotenv/config ./BUSINESS/server.js dotenv_config_path=./BUSINESS/.env.uat", "live-business": "node --watch -r dotenv/config ./BUSINESS/server.js dotenv_config_path=./BUSINESS/.env.live", "local-users": "node --watch ./USERS -r dotenv/config ./USERS/server.js dotenv_config_path=./USERS/.env.local", "dev-users": "node --watch -r dotenv/config ./USERS/server.js dotenv_config_path=./USERS/.env.dev", "staging-users": "node --watch -r dotenv/config ./USERS/server.js dotenv_config_path=./USERS/.env.staging", "uat-users": "node --watch -r dotenv/config ./USERS/server.js dotenv_config_path=./USERS/.env.uat", "live-users": "node --watch -r dotenv/config ./USERS/server.js dotenv_config_path=./USERS/.env.live", "local-config": "node --watch ./CONFIG -r dotenv/config ./CONFIG/server.js dotenv_config_path=./CONFIG/.env.local", "dev-config": "node --watch -r dotenv/config ./CONFIG/server.js dotenv_config_path=./CONFIG/.env.dev", "staging-config": "node --watch -r dotenv/config ./CONFIG/server.js dotenv_config_path=./CONFIG/.env.staging", "uat-config": "node --watch -r dotenv/config ./CONFIG/server.js dotenv_config_path=./CONFIG/.env.uat", "live-config": "node --watch -r dotenv/config ./CONFIG/server.js dotenv_config_path=./CONFIG/.env.live", "local-store": "node --watch ./STORE -r dotenv/config ./STORE/server.js dotenv_config_path=./STORE/.env.local", "dev-store": "node --watch -r dotenv/config ./STORE/server.js dotenv_config_path=./STORE/.env.dev", "staging-store": "node --watch -r dotenv/config ./STORE/server.js dotenv_config_path=./STORE/.env.staging", "uat-store": "node --watch -r dotenv/config ./STORE/server.js dotenv_config_path=./STORE/.env.uat", "live-store": "node --watch -r dotenv/config ./STORE/server.js dotenv_config_path=./STORE/.env.live", "local-common": "node --watch ./COMMON -r dotenv/config ./COMMON/server.js dotenv_config_path=./COMMON/.env.local", "dev-common": "node --watch -r dotenv/config ./COMMON/server.js dotenv_config_path=./COMMON/.env.dev", "staging-common": "node --watch -r dotenv/config ./COMMON/server.js dotenv_config_path=./COMMON/.env.staging", "uat-common": "node --watch -r dotenv/config ./COMMON/server.js dotenv_config_path=./COMMON/.env.uat", "live-common": "node --watch -r dotenv/config ./COMMON/server.js dotenv_config_path=./COMMON/.env.live", "kill-ports": "cmd /c \"kill_ports.bat\"", "generate-docs": "node scripts/generate-docs.js", "generate-openapi": "node scripts/generate-openapi-specs.js", "add-jsdoc": "node scripts/add-jsdoc-to-all-routes.js", "validate-docs": "node scripts/validate-docs.js"}, "license": "ISC", "dependencies": {"@sendgrid/mail": "^7.7.0", "aws-sdk": "^2.1552.0", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.40.0", "cors": "^2.8.5", "crypto-js": "^4.1.1", "csv-writer": "^1.6.0", "date-fns": "^2.30.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-group-routes": "^1.1.0", "express-useragent": "^1.0.15", "helmet": "^6.0.1", "jsonwebtoken": "^8.5.1", "jwt-decode": "^3.1.2", "moment-timezone": "^0.5.43", "mongodb": "^4.12.1", "mongoose": "^6.8.0", "mongoose-unique-validator": "^3.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "net": "^1.0.2", "node-cron": "^3.0.2", "node-emv": "^1.0.22", "nodemailer": "^6.8.0", "redis": "^4.6.11", "twilio": "^3.71.1"}}