NODE_ENV=local
ECHO=l
WINDOW=30
MAX_LIMIT=10
PORT=4008
SECRET="secret"
# MONGO_URI="mongodb://127.0.0.1:27017/amex"
MONGO_URI="mongodb://root:rootadmin1!2!@*************:27017/amex?authSource=admin"
KEY=PSiUpload-048j54323k53qpmz5vmfdv
SSL_CERT_PATH="./certificate/amex-dev.pspservicesco.com_fullchain.cer"
SSL_KEY_PATH="./certificate/amex-dev.pspservicesco.com_private_key.key"
AMEX_URL="https://apigateway2sma-qa.americanexpress.com"
AMEX_CLIENT_ID="ywP8XmTg5l4w6wWRWOingFLqQSEGd9Pv"
AMEX_CLIENT_SECRET="7PwNKkvHoz4PL9kl6rxueMfZS01r9UsK"
AMEX_HOST="https://apigateway2sma-qa.americanexpress.com"
AMEX_END_POINT="https://apigateway2sma-qa.americanexpress.com"
MONDO_DB_NAME=amex
MONGO_DB_COLLECTION=merchantDetails
PARTICIPANT_SE=SC8/aPGgFZan0obN8mnvG4ycT7MhDh3iZLCiBMJj+48= 