const express = require('express');
require('express-group-routes');
const router = express.Router();

const { amexController } = require('./controllers');


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */

router.group('/v1', router => {
  /**
   * @swagger
   * /amex/v1/merchant:
   *   get:
   *     tags: [Merchant Management]
   *     summary: Get all merchants
   *     description: Retrieve a list of all AMEX merchants
   *     responses:
   *       200:
   *         description: Merchants retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
  router.get('/merchant', amexController.getAllMerchants);

  /**
   * @swagger
   * /amex/v1/merchant/{record_number}:
   *   get:
   *     tags: [Merchant Management]
   *     summary: Get merchant by record number
   *     description: Retrieve a specific AMEX merchant by record number
   *     parameters:
   *       - name: record_number
   *         in: path
   *         required: true
   *         description: Merchant record number
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: Merchant retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  router.get('/merchant/:record_number', amexController.getMerchantById);

  /**
   * @swagger
   * /amex/v1/merchant:
   *   post:
   *     tags: [Merchant Management]
   *     summary: Create merchant
   *     description: Create a new AMEX merchant
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateMerchantRequest'
   *     responses:
   *       201:
   *         description: Merchant created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/merchant', amexController.createMerchant);

  /**
   * @swagger
   * /amex/v1/merchant:
   *   delete:
   *     tags: [Merchant Management]
   *     summary: Delete merchant
   *     description: Delete an AMEX merchant
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               record_number:
   *                 type: string
   *                 description: Merchant record number to delete
   *             required: [record_number]
   *     responses:
   *       200:
   *         description: Merchant deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */
  router.delete('/merchant', amexController.deleteMerchant);
});

module.exports = router;
