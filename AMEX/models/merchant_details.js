const mongoose = require('mongoose');

const Schema = mongoose.Schema;

const addressSchema = new Schema({
  address_line_1: { type: String },
  address_line_2: { type: String },
  address_line_3: { type: String },
  address_line_4: { type: String },
  address_line_5: { type: String },
  city_name: { type: String },
  region_code: { type: String },
  postal_code: { type: String },
  country_code: { type: String },
});
const ownerSchema = new Schema({
  first_name: { type: String },
  last_name: { type: String },
  identification_number: { type: String },
  date_of_birth: { type: String },
  street_address: { type: addressSchema },
});
const sellerSchema = new Schema({
  seller_id: { type: String },
  seller_url: { type: String },
  seller_status: { type: String },
  seller_mcc: { type: String },
  seller_legal_name: { type: String },
  seller_dba_name: { type: String },
  seller_business_registration_number: { type: String },
  seller_business_phone_number: { type: String },
  seller_email_address: { type: String },
  seller_currency_code: { type: String },
  seller_start_date: { type: String },
  seller_term_date: { type: String },
  seller_charge_volume: { type: String },
  seller_transaction_count: { type: String },
  seller_chargeback_count: { type: String },
  seller_chargeback_amount: { type: String },
  seller_street_address: { type: addressSchema },
});
const setupErrorSeSchema = new Schema({
  submission_se: { type: String },
  seller_id: { type: String },
});
const setupErrorSchema = new Schema({
  message_id: { type: String },
  number_of_setups_processed: { type: Number },
  number_of_setups_with_warnings: { type: Number },
  number_of_setups_with_errors: { type: Number },
  se_setup_error_details: { type: [setupErrorSeSchema] },
});
const SaleSchema = new Schema({
  channel_indicator_code: { type: String },
  channel_name: { type: String },
  represent_id: { type: String },
  iso_register_number: { type: String },
});
const merchantDetailsSchema = new Schema(
  {
    record_number: { type: String },
    participant_se: { type: String },
    submitter_id: { type: String },
    se_detail_status_code: { type: String },
    se_status_code_change_date: { type: String },
    language_preference_code: { type: String },
    japan_credit_bureau_indicator: { type: String },
    marketing_indicator: { type: String },
    ownership_type_indicator: { type: String },
    seller_transacting_indicator: { type: String },
    client_defined_code: { type: String },
    seller: { type: sellerSchema },
    significant_owners: {
      first_owner: { type: ownerSchema },
      second_owner: { type: ownerSchema },
      third_owner: { type: ownerSchema },
      fourth_owner: { type: ownerSchema },
    },
    authorized_signer: { type: ownerSchema },
    sale: { type: SaleSchema },
    setup_error_details: {
      type: [setupErrorSchema],
    },
    owner_count: { type: Number },
    signer_count: { type: Number },
  },
  { timestamps: true },
);

module.exports = mongoose.model('merchant_details', merchantDetailsSchema);
