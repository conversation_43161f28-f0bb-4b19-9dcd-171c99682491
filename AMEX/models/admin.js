const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const adminSchema = new Schema(
  {
    email: {
      type: String,
      unique: true,
      required: [true, 'email is required'],
    },
    password: {
      type: String,
      required: [true, 'password is ready'],
    },
    permissions: { type: Array, default: [] },
    roles: [{ type: Schema?.Types?.ObjectId, ref: 'role' }],
    token: { type: String, default: '' },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

adminSchema.plugin(uniqueValidator);

const ADMIN = model('admin', adminSchema);
module.exports = ADMIN;
