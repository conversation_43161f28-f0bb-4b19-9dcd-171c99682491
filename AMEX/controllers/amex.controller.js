const { getMerchantPayload, createMerchantWithAmex, changeMerchantFormatArr } = require('../utils/helper');
const { MERCHANTDETAILS } = require('../models');
exports.getAllMerchants = async (req, res) => {
  try {
    let { page, itemsPerPage } = req.query;
    page = page ? Number(page) : 1;
    itemsPerPage = itemsPerPage ? Number(itemsPerPage) : 10;
    const skip = (page - 1) * itemsPerPage;
    const filter = {};
    const totalDocuments = await MERCHANTDETAILS.countDocuments(filter);
    const projection = {
      _id: 0,
      record_number: 1,
      'seller.seller_id': 1,
      'seller.seller_legal_name': 1,
      'seller.seller_business_phone_number': 1,
      'seller.seller_email_address': 1,
      'seller.seller_start_date': 1,
      'seller.seller_status': 1,
      owner_count: 1,
      signer_count: 1,
    };
    const merchantDetails = await MERCHANTDETAILS.find(filter, projection).skip(skip).limit(itemsPerPage);
    return res.status(200).json({ totalCount: totalDocuments, merchantDetails });
  } catch (ex) {
    return res.status(500).json({ message: ex.message });
  }
};
exports.getMerchantById = async (req, res) => {
  try {
    const { record_number } = req.params;
    const merchant = await MERCHANTDETAILS.find({});
    return res.status(200).json(merchant);
  } catch (ex) {
    return res.status(500).json({ message: ex.message });
  }
};
exports.createMerchant = async (req, res) => {
  try {
    const merchant = req.body;
    if (!merchant) {
      return res.status(400).json({ message: 'Merchant details are required' });
    }
    const payload = await getMerchantPayload(merchant);
    const query = { record_number: payload.record_number };
    // const resp = await createMerchantWithAmex(payload);
    // const error = {
    //   message_id: resp.message_id,
    //   number_of_setups_processed: resp.number_of_setups_processed,
    //   number_of_setups_with_warnings: resp.number_of_setups_with_warnings,
    //   number_of_setups_with_errors: resp.number_of_setups_with_errors,
    //   se_setup_error_details: resp.se_setup_error_details,
    // };
    payload.owner_count = Object.keys(payload.significant_owners).length;
    payload.signer_count = 1;
    // payload.setup_error_details = [error];
    let resp = await MERCHANTDETAILS.findOneAndUpdate(
      query,
      {
        $set: payload,
      },
      { upsert: true, new: true },
    );
    return res.status(200).json(resp);
  } catch (ex) {
    console.log('error', ex);
    return res.status(500).json({ message: ex.message });
  }
};
exports.deleteMerchant = async (req, res) => {
  try {
    const { record_number } = req.query;
    await MERCHANTDETAILS.findOneAndDelete({
      record_number,
    });
    return res.status(200).json({ message: 'Merchant deleted successfully' });
  } catch (ex) {
    return res.status(500).json({ message: ex.message });
  }
};
