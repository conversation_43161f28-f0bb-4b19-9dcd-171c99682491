const dotenv = require('dotenv');
dotenv.config();

module.exports = {
  env: process.env.NODE_ENV,
  window: process.env.WINDOW,
  max_limit: process.env.MAX_LIMIT,
  port: process.env.PORT,
  secret: process.env.SECRET,
  mongo_string: process.env.MONGO_URI,
  participant_se: process.env.PARTICIPANT_SE,

  amex_url: process.env.AMEX_URL,
  amex_client_id: process.env.AMEX_CLIENT_ID,
  amex_client_secret: process.env.AMEX_CLIENT_SECRET,
  amex_host: process.env.AMEX_HOST,
  amex_end_point: process.env.AMEX_END_POINT,

  ssl_cert_path: process.env.SSL_CERT_PATH,
  ssl_key_path: process.env.SSL_KEY_PATH,
};
