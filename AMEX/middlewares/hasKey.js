const { access_key } = require('../configs');

module.exports = (req, res, next) => {
  //check if he is admin
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).send({
      error: true,
      message: 'Authorization Header Missing!',
    });
  }

  const token = authHeader.split(' ')[1];
  if (token === access_key) {
    next();
  } else {
    res.status(401).json({
      message: 'API KEY Required',
      success: false,
    });
  }
};
