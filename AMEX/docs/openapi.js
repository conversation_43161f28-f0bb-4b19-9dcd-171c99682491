/**
 * @fileoverview OpenAPI configuration for AMEX service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const amexSchemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for AMEX service
 * @param {Object} app - Express app instance
 */
function setupAmexDocs(app) {
  const config = {
    serviceName: 'AMEX',
    serviceDescription: `
      American Express integration service

      Key features:
      - Entity management
      - Authentication and authorization
      - Data validation and processing
    `,
    version: '1.0.0',
    port: 4008,
    basePath: '/amex',
    additionalSchemas: amexSchemas,
    docsPath: '/amex/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  setupAmexDocs
};