/**
 * @fileoverview OpenAPI schemas specific to AMEX service
 */

module.exports = {
  Merchant: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      record_number: {
        type: 'string',
        example: 'MRC001234',
        description: 'Merchant record number'
      },
      merchant_name: {
        type: 'string',
        example: 'ABC Store Inc.',
        description: 'Merchant business name'
      },
      merchant_id: {
        type: 'string',
        example: 'AMEX123456',
        description: 'AMEX merchant ID'
      },
      dba_name: {
        type: 'string',
        example: 'ABC Store',
        description: 'Doing business as name'
      },
      address: {
        type: 'object',
        properties: {
          street: { type: 'string', example: '123 Main St' },
          city: { type: 'string', example: 'New York' },
          state: { type: 'string', example: 'NY' },
          zip: { type: 'string', example: '10001' },
          country: { type: 'string', example: 'USA' }
        }
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['record_number', 'merchant_name', 'merchant_id']
  },

  CreateMerchantRequest: {
    type: 'object',
    properties: {
      record_number: {
        type: 'string',
        minLength: 3,
        example: 'MRC001234',
        description: 'Merchant record number'
      },
      merchant_name: {
        type: 'string',
        minLength: 2,
        example: 'ABC Store Inc.',
        description: 'Merchant business name'
      },
      merchant_id: {
        type: 'string',
        minLength: 3,
        example: 'AMEX123456',
        description: 'AMEX merchant ID'
      },
      dba_name: {
        type: 'string',
        example: 'ABC Store',
        description: 'Doing business as name'
      },
      address: {
        type: 'object',
        properties: {
          street: { type: 'string', example: '123 Main St' },
          city: { type: 'string', example: 'New York' },
          state: { type: 'string', example: 'NY' },
          zip: { type: 'string', example: '10001' },
          country: { type: 'string', example: 'USA' }
        },
        required: ['street', 'city', 'state', 'zip']
      }
    },
    required: ['record_number', 'merchant_name', 'merchant_id', 'address']
  },

  UpdateAmexRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Updated Amex',
        description: 'Amex name'
      },
      description: {
        type: 'string',
        example: 'Updated description',
        description: 'Amex description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  AmexListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Amex'
            }
          }
        }
      }
    ]
  }
};