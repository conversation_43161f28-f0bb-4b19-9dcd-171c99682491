const {
  participant_se,
  amex_client_id,
  amex_client_secret,
  amex_end_point,
  amex_host,
  ssl_cert_path,
  ssl_key_path,
  amex_url,
} = require('../configs');
const { default: axios } = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const { MERCHANTDETAILS } = require('../models');
const path = require('path');

exports.pagination = (items = [], page = 1, totalItems = 0, itemsPerPage = 5) => {
  return {
    currentPage: page,
    hasNextPage: itemsPerPage * page < totalItems,
    hasPreviousPage: page > 1,
    nextPage: page + 1,
    previousPage: page - 1,
    lastPage: Math.ceil(totalItems / itemsPerPage),
    totalItems: totalItems,
    items: items,
  };
};
exports.filterQuery = req => ({
  ...req.query,
  page: req.query.page ? Number(req.query.page) : 1,
  itemsPerPage: req.query.itemsPerPage
    ? Number(req.query.itemsPerPage)
    : req.query.perPage
      ? Number(req.query.perPage)
      : 10,
  searchText:
    req.query.searchText !== 'null' && req.query.searchText !== 'undefined' && req.query.searchText
      ? req.query.searchText
      : '',
  startDate:
    req.query.startDate !== 'null' && req.query.startDate !== 'undefined' && req.query.startDate
      ? req.query.startDate
      : '',
  endDate:
    req.query.endDate !== 'null' && req.query.endDate !== 'undefined' && req.query.endDate ? req.query.endDate : '',
  storeId:
    req.query.storeId !== 'null' && req.query.storeId !== 'undefined' && req.query.storeId ? req.query.storeId : '',
});
exports.getMerchantPayload = async _ => {
  const record_number = _.record_number ?? (await this.completelyUniqueRecordNumber());
  const merchant = {
    ..._,
    record_number: record_number ?? '',
    participant_se: participant_se ?? '',
    submitter_id: _?.submitter_id ?? '',
    se_detail_status_code: _?.se_detail_status_code?.value ?? '',
    language_preference_code: _?.language_preference_code ?? '',
    japan_credit_bureau_indicator: _?.japan_credit_bureau_indicator?.value ?? '',
    marketing_indicator: _?.marketing_indicator?.value ?? '',
    ownership_type_indicator: _?.ownership_type_indicator?.value ?? '',
    seller_transacting_indicator: _.seller_transacting_indicator ?? '',
    client_defined_code: _.client_defined_code ?? '',
    seller: {
      ..._.seller,
      seller_id: _?.seller?.seller_id ?? '',
      seller_url: _?.seller?.seller_url ?? '',
      seller_status: _?.seller?.seller_status?.value ?? '',
      seller_mcc: _?.seller?.seller_mcc ?? '',
      seller_legal_name: _?.seller?.seller_legal_name ?? '',
      seller_dba_name: _?.seller?.seller_dba_name ?? '',
      seller_business_registration_number: _?.seller?.seller_business_registration_number ?? '',
      seller_business_phone_number: _?.seller?.seller_business_phone_number ?? '',
      seller_email_address: _?.seller?.seller_email_address ?? '',
      seller_currency_code: _?.seller?.seller_currency_code ?? '',
      seller_charge_volume: _?.seller?.seller_charge_volume ?? '',
      seller_transaction_count: _?.seller?.seller_transaction_count ?? '',
      seller_chargeback_count: _?.seller?.seller_chargeback_count ?? '',
      seller_chargeback_amount: _?.seller?.seller_chargeback_amount ?? '',
      seller_street_address: {
        ..._?.seller?.seller_street_address,
        region_code: _?.seller?.seller_street_address?.region_code?.value ?? '',
      },
    },
    authorized_signer: {
      ..._?.authorized_signer,
      street_address: {
        ..._?.authorized_signer?.street_address,
        region_code: _?.authorized_signer?.street_address?.region_code?.value ?? '',
      },
    },
    sale: _?.sale,
  };
  if (merchant.significant_owners.first_owner) {
    merchant.significant_owners.first_owner.street_address.region_code =
      merchant.significant_owners.first_owner.street_address.region_code?.value ?? '';
  }
  if (merchant.significant_owners.second_owner) {
    merchant.significant_owners.second_owner.street_address.region_code =
      merchant.significant_owners.second_owner.street_address.region_code?.value ?? '';
  }
  if (merchant.significant_owners.third_owner) {
    merchant.significant_owners.third_owner.street_address.region_code =
      merchant.significant_owners.third_owner.street_address.region_code?.value ?? '';
  }
  if (merchant.significant_owners.fourth_owner) {
    merchant.significant_owners.fourth_owner.street_address.region_code =
      merchant.significant_owners.fourth_owner.street_address.region_code?.value ?? '';
  }
  return merchant;
};
exports.createMerchantWithAmex = async merchant => {
  const referenceNumber = this.generateRandomString(15);
  const payload = {
    se_setup_request_count: 1,
    message_id: referenceNumber,
    se_setup_requests: [merchant],
  };
  const macHeader = this.generateMacHeader(
    amex_client_id,
    amex_client_secret,
    amex_end_point,
    amex_host,
    443,
    'POST',
    JSON.stringify(payload),
  );
  console.log({ macHeader });
  const https = require('https');
  const httpsAgent = new https.Agent({
    cert: fs.readFileSync(path.resolve(__dirname, ssl_cert_path)),
    key: fs.readFileSync(path.resolve(__dirname, ssl_key_path)),
    rejectUnauthorized: false,
  });

  const url = amex_url + amex_end_point;
  const headers = {
    'Content-Type': 'application/json',
    Authorization: macHeader,
    'x-amex-api-key': amex_client_secret,
  };
  const response = await axios.post(url, payload, { headers, httpsAgent });
  console.log({ response });

  resolve(response.data);
  return response.data;
};

exports.generateRandomString = (length = 15) => {
  return Math.random()
    .toString(36)
    .substring(2, 2 + length);
};

exports.changeMerchantFormatArr = merchant => {
  if (merchant.authorized_signer.date_of_birth != '') {
    merchant.authorized_signer.date_of_birth = new Date(merchant.authorized_signer.date_of_birth).toISOString();
  }
  merchant.authorized_signer = [merchant.authorized_signer];

  if (merchant.se_status_code_change_date !== '') {
    merchant.se_status_code_change_date = new Date(merchant.se_status_code_change_date).toISOString();
  }
  if (merchant.seller.seller_start_date !== '') {
    merchant.seller.seller_start_date = new Date(merchant.seller.seller_start_date).toISOString();
  }
  if (merchant.seller.seller_term_date !== '') {
    merchant.seller.seller_term_date = new Date(merchant.seller.seller_term_date).toISOString();
  }
  let owners = [];
  if (merchant.significant_owners.first_owner) {
    if (merchant.significant_owners.first_owner.date_of_birth != '') {
      merchant.significant_owners.first_owner.date_of_birth = new Date(
        merchant.significant_owners.first_owner.date_of_birth,
      ).toISOString();
    }
    owners.push(merchant.significant_owners.first_owner);
  }
  if (merchant.significant_owners.second_owner) {
    if (merchant.significant_owners.second_owner.date_of_birth != '') {
      merchant.significant_owners.second_owner.date_of_birth = new Date(
        merchant.significant_owners.second_owner.date_of_birth,
      ).toISOString();
    }
    owners.push(merchant.significant_owners.second_owner);
  }
  if (merchant.significant_owners.third_owner) {
    if (merchant.significant_owners.third_owner.date_of_birth != '') {
      merchant.significant_owners.third_owner.date_of_birth = new Date(
        merchant.significant_owners.third_owner.date_of_birth,
      ).toISOString();
    }
    owners.push(merchant.significant_owners.third_owner);
  }
  if (merchant.significant_owners.fourth_owner) {
    if (merchant.significant_owners.fourth_owner.date_of_birth != '') {
      merchant.significant_owners.fourth_owner.date_of_birth = new Date(
        merchant.significant_owners.fourth_owner.date_of_birth,
      ).toISOString();
    }
    owners.push(merchant.significant_owners.fourth_owner);
  }
  merchant.significant_owners = owners;
  return merchant;
};
exports.generateNumericRandomStr = (length = 15) => {
  return Math.random()
    .toString()
    .substring(2, 2 + length);
};
exports.completelyUniqueRecordNumber = async () => {
  let record_number = this.generateNumericRandomStr(11);
  let merchant = await MERCHANTDETAILS.findOne({ record_number }).lean();
  while (merchant) {
    record_number = this.generateNumericRandomStr(11);
    merchant = await MERCHANTDETAILS.findOne({ record_number });
  }
  return record_number;
};

/**
 * @param client_id : Your application's client id
 * @param client_secret : Your application's client secret
 * @param resourcePath : Resource path of the API
 * @param host : Host name of the API
 * @param port : Port number. Example - for https use port 443
 * @param httpMethod : HTTP Verb. Example - GET, POST, PUT, DELETE
 * @param payload : payload
 * @return mac_authorization_header : MAC Authorization header
 */
exports.generateMacHeader = (client_id, client_secret, resourcePath, host, port, httpMethod, payload) => {
  const HMAC_SHA256_ALGORITHM = 'sha256';
  const utf8_encoding = 'utf8';
  const newline = '\n';
  const mac_id_label = 'MAC id="';
  const ts_label = 'ts="';
  const nonce_label = 'nonce="';
  const body_hash_label = 'bodyhash="';
  const mac_label = 'mac="';
  const terminator = '"';
  const ts = String(Date.now());
  const nonce = crypto.randomBytes(16).toString('hex');
  const signingKey = crypto.createHmac(HMAC_SHA256_ALGORITHM, client_secret);
  const rawBodyHash = signingKey.update(payload, utf8_encoding).digest('base64');
  const bodyHash = rawBodyHash;
  let baseString = '';
  baseString +=
    ts +
    newline +
    nonce +
    newline +
    httpMethod +
    newline +
    resourcePath +
    newline +
    host +
    newline +
    port +
    newline +
    bodyHash +
    newline;
  const signatureBytes = crypto
    .createHmac(HMAC_SHA256_ALGORITHM, client_secret)
    .update(baseString, utf8_encoding)
    .digest('base64');
  const signatureStr = signatureBytes;
  let macString = '';
  const mac_authorization_header =
    macString +
    mac_id_label +
    client_id +
    ts_label +
    ts +
    nonce_label +
    nonce +
    body_hash_label +
    bodyHash +
    mac_label +
    signatureStr +
    terminator;
  return mac_authorization_header;
};
