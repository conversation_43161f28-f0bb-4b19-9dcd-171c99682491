image: psppos/pipeline-node-js:v2

pipelines:
  branches:
    'staging':
      - step:
          name: Prepare Build Environment
          script:
            - export REGION_IMAGE=ca-central-1
            - COMMIT_ID=$(git rev-parse --short HEAD)
            - PREVIOUS_COMMIT_ID=$(git rev-parse --short HEAD~1)
            - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
            - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
            - aws configure set region $REGION_IMAGE
            - CHANGED_FILES=$(git diff --name-only $PREVIOUS_COMMIT_ID $COMMIT_ID)
            - |
              for project in ADMIN BUSINESS COMMON CONFIG STORE TERMINAL USERS AMEX; do
                if echo "$CHANGED_FILES" | grep -q "$project/"; then
                  echo "${project}_CHANGED=true" >> build.env
                fi
              done
            - echo "COMMIT_ID=$COMMIT_ID" >> build.env
            - echo "REGION_IMAGE=$REGION_IMAGE" >> build.env
            - echo "AWS_ACCOUNT_ID=$AWS_ACCOUNT_ID" >> build.env
          artifacts:
            - build.env

      - step:
          name: Build and Deploy Changed Services
          services:
            - docker
          script:
            - source build.env
            - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
            - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
            - aws configure set region $REGION_IMAGE
            - aws eks --region $REGION_IMAGE update-kubeconfig --name $EKS_CLUSTER_NAME
            - |
              # Build and deploy Admin if changed
              if [ "$ADMIN_CHANGED" = true ]; then
                echo "Building and deploying Admin..."
                docker build -t backend-admin:$COMMIT_ID ./ADMIN
                aws ecr get-login-password --region $REGION_IMAGE | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com
                docker tag backend-admin:$COMMIT_ID ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-admin:$COMMIT_ID
                docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-admin:$COMMIT_ID
                sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/dev/admin-backend.yaml > kubernetes/dev/admin-backend-updated.yaml
                mv kubernetes/dev/admin-backend-updated.yaml kubernetes/dev/admin-backend.yaml
                kubectl apply -f kubernetes/dev/admin-backend.yaml
              fi

              # Build and deploy Business if changed
              if [ "$BUSINESS_CHANGED" = true ]; then
                echo "Building and deploying Business..."
                docker build -t backend-business:$COMMIT_ID ./BUSINESS
                aws ecr get-login-password --region $REGION_IMAGE | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com
                docker tag backend-business:$COMMIT_ID ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-psp-business:$COMMIT_ID
                docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-psp-business:$COMMIT_ID
                sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/dev/business-backend.yaml > kubernetes/dev/business-backend-updated.yaml
                mv kubernetes/dev/business-backend-updated.yaml kubernetes/dev/business-backend.yaml
                kubectl apply -f kubernetes/dev/business-backend.yaml
              fi

              # Build and deploy Common if changed
              if [ "$COMMON_CHANGED" = true ]; then
                echo "Building and deploying Common..."
                docker build -t backend-common:$COMMIT_ID ./COMMON
                aws ecr get-login-password --region $REGION_IMAGE | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com
                docker tag backend-common:$COMMIT_ID ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-psp-common:$COMMIT_ID
                docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-psp-common:$COMMIT_ID
                sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/dev/common-backend.yaml > kubernetes/dev/common-backend-updated.yaml
                mv kubernetes/dev/common-backend-updated.yaml kubernetes/dev/common-backend.yaml
                kubectl apply -f kubernetes/dev/common-backend.yaml
              fi

              # Build and deploy Config if changed
              if [ "$CONFIG_CHANGED" = true ]; then
                echo "Building and deploying Config..."
                docker build -t backend-config:$COMMIT_ID ./CONFIG
                aws ecr get-login-password --region $REGION_IMAGE | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com
                docker tag backend-config:$COMMIT_ID ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-psp-config:$COMMIT_ID
                docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-psp-config:$COMMIT_ID
                sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/dev/config-backend.yaml > kubernetes/dev/config-backend-updated.yaml
                mv kubernetes/dev/config-backend-updated.yaml kubernetes/dev/config-backend.yaml
                kubectl apply -f kubernetes/dev/config-backend.yaml
              fi

              # Build and deploy Store if changed
              if [ "$STORE_CHANGED" = true ]; then
                echo "Building and deploying Store..."
                docker build -t backend-store:$COMMIT_ID ./STORE
                aws ecr get-login-password --region $REGION_IMAGE | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com
                docker tag backend-store:$COMMIT_ID ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-store:$COMMIT_ID
                docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-store:$COMMIT_ID
                sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/dev/store-backend.yaml > kubernetes/dev/store-backend-updated.yaml
                mv kubernetes/dev/store-backend-updated.yaml kubernetes/dev/store-backend.yaml
                kubectl apply -f kubernetes/dev/store-backend.yaml
              fi

              # Build and deploy Terminal if changed
              if [ "$TERMINAL_CHANGED" = true ]; then
                echo "Building and deploying Terminal..."
                docker build -t backend-terminal:$COMMIT_ID ./TERMINAL
                aws ecr get-login-password --region $REGION_IMAGE | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com
                docker tag backend-terminal:$COMMIT_ID ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-terminal:$COMMIT_ID
                docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-terminal:$COMMIT_ID
                sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/dev/terminal-backend.yaml > kubernetes/dev/terminal-backend-updated.yaml
                mv kubernetes/dev/terminal-backend-updated.yaml kubernetes/dev/terminal-backend.yaml
                kubectl apply -f kubernetes/dev/terminal-backend.yaml
              fi

              # Build and deploy Users if changed
              if [ "$USERS_CHANGED" = true ]; then
                echo "Building and deploying Users..."
                docker build -t backend-users:$COMMIT_ID ./USERS
                aws ecr get-login-password --region $REGION_IMAGE | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com
                docker tag backend-users:$COMMIT_ID ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-users:$COMMIT_ID
                docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-users:$COMMIT_ID
                sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/dev/users-backend.yaml > kubernetes/dev/users-backend-updated.yaml
                mv kubernetes/dev/users-backend-updated.yaml kubernetes/dev/users-backend.yaml
                kubectl apply -f kubernetes/dev/users-backend.yaml
              fi

              # Build and deploy Amex if changed
              if [ "$AMEX_CHANGED" = true ]; then
                echo "Building and deploying Amex..."
                docker build -t backend-amex:$COMMIT_ID ./AMEX
                aws ecr get-login-password --region $REGION_IMAGE | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com
                docker tag backend-amex:$COMMIT_ID ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-amex:$COMMIT_ID
                docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION_IMAGE}.amazonaws.com/psp-pos-amex:$COMMIT_ID
                sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/dev/amex-backend.yaml > kubernetes/dev/amex-backend-updated.yaml
                mv kubernetes/dev/amex-backend-updated.yaml kubernetes/dev/amex-backend.yaml
                kubectl apply -f kubernetes/dev/amex-backend.yaml
              fi

    'master':
      - step:
          name: Prepare Build Environment
          script:
            - |
              export REGION_IMAGE_PROD=ca-central-1
              COMMIT_ID=$(git rev-parse --short HEAD)
              PREVIOUS_COMMIT_ID=$(git rev-parse --short HEAD~1)
              CHANGED_FILES=$(git diff --name-only $PREVIOUS_COMMIT_ID $COMMIT_ID)
              
              for project in ADMIN BUSINESS COMMON CONFIG STORE TERMINAL USERS AMEX; do
                if echo "$CHANGED_FILES" | grep -q "^$project/"; then
                  echo "${project}_CHANGED=true" >> build.env
                fi
              done
              
              echo "COMMIT_ID=$COMMIT_ID" >> build.env
              echo "REGION_IMAGE_PROD=$REGION_IMAGE_PROD" >> build.env
              echo "AWS_ACCOUNT_ID_PROD=$AWS_ACCOUNT_ID_PROD" >> build.env
          artifacts:
            - build.env

      - parallel:
          - step:
              name: Build and Deploy Admin
              services:
                - docker
              script:
                - source build.env
                - |
                  if [ "$ADMIN_CHANGED" = true ]; then
                    # Configure AWS CLI with production credentials
                    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_PROD
                    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_PROD
                    export AWS_DEFAULT_REGION=$REGION_IMAGE_PROD
                    
                    # Verify AWS identity and account
                    echo "Verifying AWS identity..."
                    aws sts get-caller-identity
                    echo "Expected Account ID: $AWS_ACCOUNT_ID_PROD"
                    
                    # Update kubeconfig
                    aws eks --region $REGION_IMAGE_PROD update-kubeconfig --name $EKS_CLUSTER_NAME_PROD
                    
                    docker build -t backend-admin:$COMMIT_ID ./ADMIN
                    aws ecr get-login-password --region $REGION_IMAGE_PROD | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com
                    docker tag backend-admin:$COMMIT_ID ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_admin_backend:$COMMIT_ID
                    docker push ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_admin_backend:$COMMIT_ID
                    sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/prod/admin-backend.yaml > kubernetes/prod/admin-backend-updated.yaml
                    mv kubernetes/prod/admin-backend-updated.yaml kubernetes/prod/admin-backend.yaml
                    kubectl apply -f kubernetes/prod/admin-backend.yaml
                  fi

          - step:
              name: Build and Deploy Business
              services:
                - docker
              script:
                - source build.env
                - |
                  if [ "$BUSINESS_CHANGED" = true ]; then
                    # Configure AWS CLI with production credentials
                    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_PROD
                    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_PROD
                    export AWS_DEFAULT_REGION=$REGION_IMAGE_PROD
                    
                    # Verify AWS identity and account
                    echo "Verifying AWS identity..."
                    aws sts get-caller-identity
                    echo "Expected Account ID: $AWS_ACCOUNT_ID_PROD"
                    
                    # Update kubeconfig
                    aws eks --region $REGION_IMAGE_PROD update-kubeconfig --name $EKS_CLUSTER_NAME_PROD
                    
                    docker build -t backend-business:$COMMIT_ID ./BUSINESS
                    aws ecr get-login-password --region $REGION_IMAGE_PROD | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com
                    docker tag backend-business:$COMMIT_ID ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_business_backend:$COMMIT_ID
                    docker push ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_business_backend:$COMMIT_ID
                    sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/prod/business-backend.yaml > kubernetes/prod/business-backend-updated.yaml
                    mv kubernetes/prod/business-backend-updated.yaml kubernetes/prod/business-backend.yaml
                    kubectl apply -f kubernetes/prod/business-backend.yaml
                  fi

          - step:
              name: Build and Deploy Common
              services:
                - docker
              script:
                - source build.env
                - |
                  if [ "$COMMON_CHANGED" = true ]; then
                    # Configure AWS CLI with production credentials
                    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_PROD
                    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_PROD
                    export AWS_DEFAULT_REGION=$REGION_IMAGE_PROD
                    
                    # Verify AWS identity and account
                    echo "Verifying AWS identity..."
                    aws sts get-caller-identity
                    echo "Expected Account ID: $AWS_ACCOUNT_ID_PROD"
                    
                    # Update kubeconfig
                    aws eks --region $REGION_IMAGE_PROD update-kubeconfig --name $EKS_CLUSTER_NAME_PROD
                    
                    docker build -t backend-common:$COMMIT_ID ./COMMON
                    aws ecr get-login-password --region $REGION_IMAGE_PROD | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com
                    docker tag backend-common:$COMMIT_ID ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_common_backend:$COMMIT_ID
                    docker push ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_common_backend:$COMMIT_ID
                    sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/prod/common-backend.yaml > kubernetes/prod/common-backend-updated.yaml
                    mv kubernetes/prod/common-backend-updated.yaml kubernetes/prod/common-backend.yaml
                    kubectl apply -f kubernetes/prod/common-backend.yaml
                  fi

          - step:
              name: Build and Deploy Config
              services:
                - docker
              script:
                - source build.env
                - |
                  if [ "$CONFIG_CHANGED" = true ]; then
                    # Configure AWS CLI with production credentials
                    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_PROD
                    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_PROD
                    export AWS_DEFAULT_REGION=$REGION_IMAGE_PROD
                    
                    # Verify AWS identity and account
                    echo "Verifying AWS identity..."
                    aws sts get-caller-identity
                    echo "Expected Account ID: $AWS_ACCOUNT_ID_PROD"
                    
                    # Update kubeconfig
                    aws eks --region $REGION_IMAGE_PROD update-kubeconfig --name $EKS_CLUSTER_NAME_PROD
                    
                    docker build -t backend-config:$COMMIT_ID ./CONFIG
                    aws ecr get-login-password --region $REGION_IMAGE_PROD | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com
                    docker tag backend-config:$COMMIT_ID ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_config_backend:$COMMIT_ID
                    docker push ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_config_backend:$COMMIT_ID
                    sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/prod/config-backend.yaml > kubernetes/prod/config-backend-updated.yaml
                    mv kubernetes/prod/config-backend-updated.yaml kubernetes/prod/config-backend.yaml
                    kubectl apply -f kubernetes/prod/config-backend.yaml
                  fi

          - step:
              name: Build and Deploy Store
              services:
                - docker
              script:
                - source build.env
                - |
                  if [ "$STORE_CHANGED" = true ]; then
                    # Configure AWS CLI with production credentials
                    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_PROD
                    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_PROD
                    export AWS_DEFAULT_REGION=$REGION_IMAGE_PROD
                    
                    # Verify AWS identity and account
                    echo "Verifying AWS identity..."
                    aws sts get-caller-identity
                    echo "Expected Account ID: $AWS_ACCOUNT_ID_PROD"
                    
                    # Update kubeconfig
                    aws eks --region $REGION_IMAGE_PROD update-kubeconfig --name $EKS_CLUSTER_NAME_PROD
                    
                    docker build -t backend-store:$COMMIT_ID ./STORE
                    aws ecr get-login-password --region $REGION_IMAGE_PROD | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com
                    docker tag backend-store:$COMMIT_ID ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_store_backend:$COMMIT_ID
                    docker push ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_store_backend:$COMMIT_ID
                    sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/prod/store-backend.yaml > kubernetes/prod/store-backend-updated.yaml
                    mv kubernetes/prod/store-backend-updated.yaml kubernetes/prod/store-backend.yaml
                    kubectl apply -f kubernetes/prod/store-backend.yaml
                  fi

          - step:
              name: Build and Deploy Terminal
              services:
                - docker
              script:
                - source build.env
                - |
                  if [ "$TERMINAL_CHANGED" = true ]; then
                    # Configure AWS CLI with production credentials
                    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_PROD
                    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_PROD
                    export AWS_DEFAULT_REGION=$REGION_IMAGE_PROD
                    
                    # Verify AWS identity and account
                    echo "Verifying AWS identity..."
                    aws sts get-caller-identity
                    echo "Expected Account ID: $AWS_ACCOUNT_ID_PROD"
                    
                    # Update kubeconfig
                    aws eks --region $REGION_IMAGE_PROD update-kubeconfig --name $EKS_CLUSTER_NAME_PROD
                    
                    docker build -t backend-terminal:$COMMIT_ID ./TERMINAL
                    aws ecr get-login-password --region $REGION_IMAGE_PROD | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com
                    docker tag backend-terminal:$COMMIT_ID ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_terminal_backend:$COMMIT_ID
                    docker push ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_terminal_backend:$COMMIT_ID
                    sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/prod/terminal-backend.yaml > kubernetes/prod/terminal-backend-updated.yaml
                    mv kubernetes/prod/terminal-backend-updated.yaml kubernetes/prod/terminal-backend.yaml
                    kubectl apply -f kubernetes/prod/terminal-backend.yaml
                  fi

          - step:
              name: Build and Deploy Users
              services:
                - docker
              script:
                - source build.env
                - |
                  if [ "$USERS_CHANGED" = true ]; then
                    # Configure AWS CLI with production credentials
                    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_PROD
                    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_PROD
                    export AWS_DEFAULT_REGION=$REGION_IMAGE_PROD
                    
                    # Verify AWS identity and account
                    echo "Verifying AWS identity..."
                    aws sts get-caller-identity
                    echo "Expected Account ID: $AWS_ACCOUNT_ID_PROD"
                    
                    # Update kubeconfig
                    aws eks --region $REGION_IMAGE_PROD update-kubeconfig --name $EKS_CLUSTER_NAME_PROD
                    
                    docker build -t backend-users:$COMMIT_ID ./USERS
                    aws ecr get-login-password --region $REGION_IMAGE_PROD | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com
                    docker tag backend-users:$COMMIT_ID ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_user_backend:$COMMIT_ID
                    docker push ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_user_backend:$COMMIT_ID
                    sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/prod/users-backend.yaml > kubernetes/prod/users-backend-updated.yaml
                    mv kubernetes/prod/users-backend-updated.yaml kubernetes/prod/users-backend.yaml
                    kubectl apply -f kubernetes/prod/users-backend.yaml
                  fi

          - step:
              name: Build and Deploy Amex
              services:
                - docker
              script:
                - source build.env
                - |
                  if [ "$AMEX_CHANGED" = true ]; then
                    # Configure AWS CLI with production credentials
                    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_PROD
                    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_PROD
                    export AWS_DEFAULT_REGION=$REGION_IMAGE_PROD
                    
                    # Verify AWS identity and account
                    echo "Verifying AWS identity..."
                    aws sts get-caller-identity
                    echo "Expected Account ID: $AWS_ACCOUNT_ID_PROD"
                    
                    # Update kubeconfig
                    aws eks --region $REGION_IMAGE_PROD update-kubeconfig --name $EKS_CLUSTER_NAME_PROD
                    
                    docker build -t backend-amex:$COMMIT_ID ./AMEX
                    aws ecr get-login-password --region $REGION_IMAGE_PROD | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com
                    docker tag backend-amex:$COMMIT_ID ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_amex_backend:$COMMIT_ID
                    docker push ${AWS_ACCOUNT_ID_PROD}.dkr.ecr.${REGION_IMAGE_PROD}.amazonaws.com/tms_amex_backend:$COMMIT_ID
                    sed 's/COMMIT_ID/'"$COMMIT_ID"'/g' kubernetes/prod/amex-backend.yaml > kubernetes/prod/amex-backend-updated.yaml
                    mv kubernetes/prod/amex-backend-updated.yaml kubernetes/prod/amex-backend.yaml
                    kubectl apply -f kubernetes/prod/amex-backend.yaml
                  fi
