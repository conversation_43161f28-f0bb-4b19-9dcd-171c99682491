const cron = require('node-cron');
const { sendDeviceData, getAllConnectedTerminals } = require('../mqtt-connection');
const { MESSAGE_QUEUE, TERMINAL_TRANSACTION } = require('../../models');

exports.messageQueue = () => {
  console.log('Message Queue Initalized');
  cron.schedule('* * * * *', async () => {
    try {
      let terminals = await getAllConnectedTerminals();
      const configsInQueue = await MESSAGE_QUEUE.find({
        serial_number: { $in: terminals },
      });
      configsInQueue.forEach(config => {
        sendDeviceData({ serial_number: config.serial_number, event: 'server_config_update' });
      });
    } catch (error) {
      console.log(error);
    }
  });

  cron.schedule('*/5 * * * *', async () => {
    try {
      const DUPLICATES = await TERMINAL_TRANSACTION.aggregate([
        {
          $match: {
            status: 'Sale',
          },
        },
        {
          $group: {
            _id: '$reference_id',
            ids: { $push: '$_id' },
            count: { $sum: 1 },
          },
        },
        {
          $match: {
            count: { $gt: 1 },
          },
        },
      ]);
      // remove all duplicates found in duplicates
      if (DUPLICATES.length > 0) {
        const idsToRemove = DUPLICATES.flatMap(duplicate => duplicate.ids.slice(1)); // Keep the first one, remove the rest
        await TERMINAL_TRANSACTION.deleteMany({ _id: { $in: idsToRemove } });
      }
    } catch (error) {
      console.log(error);
    }
  });
};
