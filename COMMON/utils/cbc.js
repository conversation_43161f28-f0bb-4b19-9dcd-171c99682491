const crypto = require('crypto');
const algorithm = 'aes-256-cbc';
const { encryption_key, enc_iv } = require('../configs');

const ENCRYPTION_KEY = Buffer.from(encryption_key, 'hex');
const IV = Buffer.from(enc_iv, 'hex');

const encrypt = text => {
  let cipher = crypto.createCipheriv(algorithm, ENCRYPTION_KEY, IV);
  let encrypted = cipher.update(text);
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  return encrypted.toString('hex');
};

const decrypt = text => {
  let encryptedText = Buffer.from(text, 'hex');
  let decipher = crypto.createDecipheriv(algorithm, ENCRYPTION_KEY, IV);
  let decrypted = decipher.update(encryptedText);
  decrypted = Buffer.concat([decrypted, decipher.final()]);
  return decrypted.toString();
};

module.exports = { encryptCBC: encrypt, decryptCBC: decrypt };
