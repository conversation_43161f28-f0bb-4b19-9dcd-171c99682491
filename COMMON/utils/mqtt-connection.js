const mqtt = require('mqtt');
const fs = require('fs');
const { mqtt_url, mqtt_port, mqtt_ca, mqtt_cert, mqtt_key, redis_url } = require('../configs');
const path = require('path');
const { createClient } = require('redis');
const { TERMINAL, TERMINAL_TRANSACTION_MQTT, CERTIFICATES, CONFIGURATION, BUSINESS } = require('../models');

const {
  setTerminalTimeZone,
  updatePrintedReceipt,
  updatePushedVersion,
  removeMessageFromQueue,
  getDevicesForSocketNotifications,
  findStoreOfDevice,
  findRestrictedPinsForStore,
  fetchNewCertificate,
  setTerminalLocationCoordinates,
} = require('./helper');

const mapSerialNumberToDetails = new Map();
const redisClient = createClient({
  url: redis_url,
});

(async () => {
  try {
    try {
      let terminals = await TERMINAL.find({ status: 'Active', is_deleted: { $ne: true } })
        .populate({
          path: 'business_id',
          select: 'mid',
          model: BUSINESS,
        })
        .lean();
      for (let terminal of terminals) {
        let key = `terminal_${terminal.store_id}:${terminal.serial_number}:${terminal?.business_id?.mid}`;
        mapSerialNumberToDetails.set(terminal.serial_number, { key });
      }
    } catch (ex) {
      console.log('Error while fetching terminals', ex);
    }
    if (!redisClient.isOpen) {
      await redisClient.connect();
    }

    await redisClient.unsubscribe();
    await redisClient.pUnsubscribe();
    // INFO: This should only be available in development and local only
    // await redisClient.configSet('notify-keyspace-events', 'Ex');
    const redisSubscriber = redisClient.duplicate();
    await redisSubscriber.connect();
    await redisSubscriber.subscribe('__keyevent@0__:expired', async (channel, message) => {
      console.log(`Key "${message}" expired`, { channel });
      let key = channel;
      if (key.startsWith('terminal_')) {
        let serial_number = key.split(':')[1];
        let mid = key.split(':')[2];
        await redisClient.del(key);
        let data = await redisClient.get(`_${key}_`);
        if (data) {
          data = JSON.parse(data);
        } else {
          data = { status: 'Active', in_use: null, is_pos: null };
        }
        this.publishToMqtt(
          `status_${serial_number}`,
          {
            serial_number: serial_number,
            store_id: key.split(':')[0].split('_')[1],
            mid: mid,
            status: data?.status || 'Active',
            online: false,
            in_use: data?.in_use,
            is_pos: data?.is_pos,
            fromRedis: 'Key Expired',
          },
          2,
          true,
        );
      }
    });
  } catch (ex) {
    console.log(ex);
    this.publishToMqtt('error_on_mqtt', { error: ex.message, details: JSON.stringify(ex) }, 2, true);
  }
})();

const options = {
  host: mqtt_url,
  port: mqtt_port,
  protocol: 'mqtts',
  ca: fs.readFileSync(path.join(__dirname, mqtt_ca).replace('/utils/utils/', '/utils/')),
  cert: fs.readFileSync(path.join(__dirname, mqtt_cert).replace('/utils/utils/', '/utils/')),
  key: fs.readFileSync(path.join(__dirname, mqtt_key).replace('/utils/utils/', '/utils/')),
  secureProtocol: 'TLSv1_2_method',
  rejectUnauthorized: false,
};

const queues = [
  'first_connection',
  'pong',
  'printed_receipt',
  'version_pushed',
  'close_queue_id',
  'update_location',
  'fetch_certificates',
  'terminal_transaction',
  'in_use_update',
];
exports.client = mqtt.connect(options);

this.client.on('connect', async () => {
  queues.forEach(async queue => {
    await this.subscribeToMqtt(queue);
  });
});

this.client.on('error', error => console.log('MQTT Error : ', error.message));

this.client.on('message', async (topic, message) => {
  try {
    if (message) {
      message = JSON.parse(message.toString());
      switch (topic) {
        case 'first_connection':
          await saveConnectedToRedis(message, false);
          break;
        case 'in_use_update':
          await updateInuse(message);
          break;
        case 'pong':
          if (!message.RedisK) {
            await __deprecated__saveConnectedToRedis(message);
          } else {
            await saveConnectedToRedis(
              {
                RedisK: message.RedisK,
              },
              true,
            );
          }
          break;
        case 'printed_receipt':
          await updatePrintedReceipt(message);
          break;
        case 'version_pushed':
          await updatePushedVersion(message);
          break;
        case 'close_queue_id':
          await removeMessageFromQueue(message);
          break;
        case 'update_location':
          await setTerminalLocationCoordinates(message.serial_number, message.coordinates);
          break;
        case 'fetch_certificates':
          {
            let data = await fetchNewCertificate();
            await this.publishToMqtt(message.serial_number, { type: 'update_certificates', ...data });
          }
          break;
        case 'ack_certificates':
          await TERMINAL.findOneAndUpdate(
            { serial_number: message.serial_number, is_deleted: false },
            { cert_version: message?.version },
          );
          break;
        case 'terminal_transaction': {
          let transaction = message.transaction;
          function first6andLast4RestHidden(str) {
            return str.slice(0, 6) + '*'.repeat(str.length - 10) + str.slice(-4);
          }
          transaction.card_number = first6andLast4RestHidden(transaction.card_number);
          let count = await TERMINAL_TRANSACTION_MQTT.countDocuments({
            terminal_id: transaction.terminal_id,
          });
          let invoice_no = 0;
          count = count + 1;
          if (transaction.is_auto_invoice) {
            let countA = await TERMINAL_TRANSACTION_MQTT.countDocuments({
              terminal_id: transaction.terminal_id,
              is_auto_invoice: true,
            });
            invoice_no = countA + 1;
          } else {
            invoice_no = transaction.invoice_no;
          }

          let trans = await TERMINAL_TRANSACTION_MQTT.create({
            ...transaction,
            transaction_no: count,
            invoice_no: invoice_no,
          });
          // trans.merchant_name = (await BUSINESS.findOne({ mid: trans.merchant_id }).select('title')).title;
          // await sendToMerchantTools(trans);
          await this.publishToMqtt(
            message.serial_number,
            { type: 'transaction_response', transaction: trans, invoice_no: invoice_no, transaction_count: count },
            2,
            false,
          );
          break;
        }
        default:
          console.log('Unknown topic: ', topic);
          break;
      }
    }
  } catch (ex) {
    console.log(ex);
    this.publishToMqtt('error_on_mqtt', { error: ex.message, details: JSON.stringify(ex) }, 2, true);
  }
});

exports.subscribeToMqtt = topic => {
  return new Promise((resolve, reject) => {
    this.client.subscribe(topic, (err, granted) => {
      if (err) {
        console.log('Error while subscribing to topic: ', topic);
        reject(err);
      } else {
        console.log('Subscribed to topic: ', topic);
        resolve(granted);
      }
    });
  });
};

exports.publishToMqtt = async (topic, message, qos = 0, retain = false) => {
  if (topic.includes('status_')) {
    let key = `terminal_${message.store_id}:${message.serial_number}:${message.mid}`;
    let dataFromRedis = await redisClient.get(`_${key}_`);
    let pos_message_fallback = {};
    if (dataFromRedis) {
      pos_message_fallback = JSON.parse(dataFromRedis);
    }
    let pos_message = { ...pos_message_fallback, ...message, mobile: false, timestamp: new Date().toISOString() };
    await new Promise(async (resolve, reject) => {
      this.client.publish(
        `status_${pos_message.serial_number}`,
        Buffer.from(JSON.stringify(pos_message)),
        {
          qos,
          retain: true,
        },
        err => {
          if (err) {
            console.log('Error while publishing to topic: ', 'terminalQueue');
            reject(err);
          } else {
            resolve();
          }
        },
      );
    });

    await new Promise(async (resolve, reject) => {
      this.client.publish(
        `terminals_${pos_message.mid}`,
        Buffer.from(
          JSON.stringify({
            store_id: pos_message.store_id,
            status: pos_message.status,
            serial_number: pos_message.serial_number,
            mid: pos_message.mid,
            is_pos: pos_message.is_pos,
            shouldRefetchTerminals: false,
            timestamp: new Date().toISOString(),
          }),
        ),
        {
          qos,
          retain: true,
        },
        err => {
          if (err) {
            console.log('Error while publishing to topic: ', 'terminalQueue');
            reject(err);
          } else {
            resolve();
          }
        },
      );
    });

    await new Promise(async (resolve, reject) => {
      this.client.publish(
        `terminalQueue`,
        Buffer.from(
          JSON.stringify({
            data: pos_message,
          }),
        ),
        {
          qos,
          retain: true,
        },
        err => {
          if (err) {
            console.log('Error while publishing to topic: ', 'terminalQueue');
            reject(err);
          } else {
            resolve();
          }
        },
      );
    });
  }
  delete message.pos_message;
  await new Promise((resolve, reject) => {
    this.client.publish(
      topic,
      Buffer.from(JSON.stringify({ ...message, timestamp: new Date().toISOString() })),
      {
        qos,
        retain: topic?.includes('terminals_') ? true : retain,
      },
      err => {
        if (err) {
          console.log('Error while publishing to topic: ', topic);
          reject(err);
        } else {
          resolve();
        }
      },
    );
  });
};

const updateInuse = async ({ serial_number, in_use }) => {
  let key = mapSerialNumberToDetails.get(serial_number)?.key;
  let timestamp = new Date().getTime();
  if (!key) {
    return;
  }
  if (!redisClient.isOpen) {
    await redisClient.connect();
  }
  let data = await redisClient.get(`_${key}_`);
  if (data) {
    data = JSON.parse(data);
    await redisClient.setEx(key, 10, JSON.stringify({ ...data, in_use, timestamp }));
    await redisClient.set(
      `_${key}_`,
      JSON.stringify({
        ...data,
        timestamp,
        in_use,
      }),
    );
    this.publishToMqtt(
      `status_${serial_number}`,
      {
        store_id: data.store_id,
        mid: data.mid,
        status: data.status,
        serial_number: serial_number,
        online: data.online,
        in_use: in_use,
        is_pos: data.is_pos,
        timestamp,
      },
      2,
      true,
    );
  }
};

const saveConnectedToRedis = async (
  { serial_number, terminal_cert_version = null, zone, apiKey = '', RedisK = '', in_use, is_pos },
  simplePing = false,
) => {
  let timestamp = new Date().getTime();
  let online = true;
  let store_id = '';
  let mid = '';

  if (!redisClient.isOpen) {
    await redisClient.connect();
  }

  if (simplePing) {
    //TODO: Remove check once all devices have new build
    let key = RedisK.includes('terminal_') ? RedisK : mapSerialNumberToDetails.get(RedisK)?.key;
    let data = await redisClient.get(`_${key}_`);
    if (data) {
      data = JSON.parse(data);
      await redisClient.setEx(key, 10, JSON.stringify({ ...data, timestamp: new Date().getTime() }));
    }
    return;
  }

  let terminal = null;
  if (apiKey) {
    terminal = await TERMINAL.findOne({
      serial_number,
      status: 'Active',
      api_key: apiKey,
      is_deleted: { $ne: true },
    })
      .populate({
        path: 'business_id',
        select: 'mid',
        model: BUSINESS,
      })
      .sort({ created_at: -1 })
      .lean();

    if (!terminal) {
      this.publishToMqtt(serial_number, {
        type: 'device_deleted',
        apiKey,
      });
      return;
    }
    store_id = terminal.store_id;
    mid = terminal?.business_id?.mid;
    if (
      terminal.cert_version != terminal_cert_version &&
      terminal_cert_version != null &&
      terminal_cert_version != '' &&
      terminal_cert_version
    ) {
      let data = await fetchNewCertificate();
      this.publishToMqtt(serial_number, { type: 'update_certificates', ...data });
    }
  }
  let key = `terminal_${store_id}:${serial_number}:${mid}`;
  mapSerialNumberToDetails.set(serial_number, { key });
  if (terminal) {
    setTerminalTimeZone(serial_number, zone);
    await redisClient.setEx(
      key,
      10,
      JSON.stringify({
        serial_number,
        timestamp,
        zone,
        store_id,
        mid,
        status: terminal.status,
        online,
        in_use,
        is_pos,
      }),
    );
    await redisClient.set(
      `_${key}_`,
      JSON.stringify({
        serial_number,
        timestamp,
        zone,
        store_id,
        mid,
        status: terminal.status,
        online,
        in_use,
        is_pos,
      }),
    );
    this.publishToMqtt(
      `status_${serial_number}`,
      {
        store_id: store_id,
        mid: mid,
        status: terminal.status,
        serial_number: serial_number,
        online: online,
        in_use: in_use,
        is_pos: is_pos,
      },
      2,
      true,
    );
  } else {
    terminal = await TERMINAL.findOne({
      serial_number,
    })
      .sort({ created_at: -1 })
      .lean();
    if (!terminal) {
      terminal = { serial_number: serial_number, status: 'Force Deactivated', store_id: store_id, mid: mid };
    }
    await redisClient.del(key);
    this.publishToMqtt(
      `status_${serial_number}`,
      {
        store_id: terminal.store_id,
        mid: terminal.mid,
        status: terminal.status,
        serial_number: terminal.serial_number,
        online: false,
        in_use: false,
        is_pos: is_pos,
      },
      2,
      true,
    );
    this.publishToMqtt(serial_number, {
      type: 'device_deleted',
      apiKey,
    });
  }
};

const __deprecated__saveConnectedToRedis = async ({
  serial_number,
  terminal_cert_version = null,
  timestamp,
  zone,
  store,
  mid = 'n/a',
  apiKey = '',
}) => {
  if (!redisClient.isOpen) {
    await redisClient.connect();
  }
  if (apiKey) {
    let terminal = await TERMINAL.findOne({
      serial_number,
      status: 'Active',
      is_deleted: { $ne: true },
      api_key: apiKey,
    }).lean();
    if (!terminal) {
      this.publishToMqtt(serial_number, { type: 'device_deleted', apiKey });
      return;
    }
    if (terminal.cert_version != terminal_cert_version && terminal_cert_version != null) {
      let data = await fetchNewCertificate();
      this.publishToMqtt(message.serial_number, { type: 'update_certificates', ...data });
    }
  }
  let terminal = await TERMINAL.findOne({ serial_number, status: 'Active', is_deleted: false }).lean();
  let key = `terminal_${store}:${serial_number}:${mid}`;
  if (terminal) {
    setTerminalTimeZone(serial_number, zone);
    let key_existed = await redisClient.get(key);
    await redisClient.setEx(key, 5, JSON.stringify({ serial_number, timestamp, zone, store, status: 'Active', mid }));
    if (!key_existed) {
      this.publishToMqtt(
        'connected_terminals',
        {
          connectedTerminals: await this.getAllConnectedTerminals(),
          pos_message: { serial_number, mid, status: 'Active', online: true, hasOnlineStatus: true, store_id: store },
        },
        2,
        true,
      );
    }
  } else {
    await redisClient.del(key);
    this.publishToMqtt(
      'connected_terminals',
      {
        connectedTerminals: await this.getAllConnectedTerminals(),
        pos_message: {
          serial_number,
          store_id: store,
          mid,
          status: terminal?.status || 'Deleted',
          online: false,
          hasOnlineStatus: true,
        },
      },
      2,
      true,
    );
    this.publishToMqtt(serial_number, { type: 'device_deleted' });
  }
};

exports.getConnectedTerminals = async ({ room }) => {
  if (!redisClient.isOpen) {
    await redisClient.connect();
  }
  let keys = await redisClient.keys(`terminal_${room}:*`);
  let terminals = [];
  for (let key of keys) {
    let terminal = await redisClient.get(key);
    try {
      terminals.push(JSON.parse(terminal).serial_number);
    } catch (error) {
      console.log(`Error parsing terminal data: ${terminal}`);
      console.log(`Error parsing terminal error: ${error.message}`);
    }
  }
  return terminals;
};

exports.getAllConnectedTerminals = async () => {
  if (!redisClient.isOpen) {
    await redisClient.connect();
  }
  let keys = await redisClient.keys('terminal_*');
  let terminals = [];
  for (let key of keys) {
    try {
      terminals.push(key.split(':')[1]);
    } catch (error) {
      console.log(`getAllConnectedTerminals Error parsing terminal data: ${error.message}`);
    }
  }
  return terminals;
};

exports.sendSocketNotification = async ({ serial_number = '', store_id = '', event = '', data = {} }) => {
  try {
    if (store_id) {
      let terminals = await this.getConnectedTerminals({ room: store_id.toString() });
      terminals.forEach(async terminal => {
        await this.publishToMqtt(terminal, { type: event, ...data });
      });
    } else {
      if (serial_number) {
        await this.publishToMqtt(serial_number, { type: event, ...data });
      }
    }
  } catch (ex) {
    console.log(ex);
  }
};

exports.getDeviceData = async (req, res) => {
  try {
    const { serial_number } = req.terminal;
    serial_number = serial_number?.toLowerCase();
    let query = {};
    if (!serial_number) {
      console.log('getDeviceData: serial_number is required');
      return res.status(400).send({
        code: 400,
        success: false,
        message: 'serial_number is required',
      });
    }
    if (serial_number) {
      query = {
        serial_number: {
          $regex: new RegExp(serial_number, 'i'),
        },
      };
    }
    const devices = await getDevicesForSocketNotifications(query);

    if (devices.length === 0) {
      return res.status(404).send({
        code: 404,
        success: false,
        message: 'Device not found',
      });
    }

    const deviceDetails = devices[0];
    const store = await findStoreOfDevice(deviceDetails?.store_id?.toString());
    const users = deviceDetails?.user_id
      .filter(user => !user.is_deleted)
      .map(({ first_name, last_name, ...user }) => {
        const updatedUser = {
          ...user,
          name: `${first_name} ${last_name}`,
          passcode: store?.passcode,
        };

        return updatedUser;
      });

    const bin_ranges = await findRestrictedPinsForStore(deviceDetails?.store_id?.toString());
    const current_version = await CERTIFICATES.findOne({}).sort({ created_at: -1 });

    return res.status(200).json({
      serial_number: deviceDetails.serial_number,
      active_users: {
        type: 'ACTIVE_USERS',
        users: users,
        message: 'device users',
      },
      device_configurations: {
        type: 'DEVICE_CONFIGURATIONS',
        configuration: deviceDetails?.configuration,
        message: 'device details',
      },
      bin_ranges: {
        type: 'BIN_RANGES',
        bin_ranges: bin_ranges?.pins ?? [],
        message: 'bin ranges',
      },
      cert_version: current_version?.version,
      tagString: deviceDetails.tagString,
      updated_at: deviceDetails.updated_at,
      is_giftcard: deviceDetails?.business_id?.is_giftcard,
      is_report: deviceDetails?.business_id?.is_report,
    });
  } catch (error) {
    console.error('Error in getDeviceData:', error);
    return res.status(500).send({
      code: 500,
      success: false,
      message: 'Internal Server Error',
      error: error.message,
    });
  }
};

exports.sendDeviceData = async ({ serial_number = '', store_id = '', event = 'server_config_update' }) => {
  store_id = store_id?.toString() ?? '';
  serial_number = serial_number?.toLowerCase();
  let query = {};
  if (!serial_number && !store_id) {
    console.log('sendDeviceData: serial_number or store_id is required');
    return;
  }
  if (serial_number) {
    query = {
      serial_number: {
        $regex: new RegExp(serial_number, 'i'),
      },
    };
  } else if (store_id) {
    query = {
      store_id,
    };
  }
  const devices = await getDevicesForSocketNotifications(query);
  devices.forEach(async deviceDetails => {
    const store = await findStoreOfDevice(deviceDetails?.store_id?.toString());
    const users = deviceDetails?.user_id
      .filter(user => !user.is_deleted)
      .map(({ first_name, last_name, ...user }) => {
        const updatedUser = {
          ...user,
          name: `${first_name} ${last_name}`,
          passcode: store?.passcode,
        };

        return updatedUser;
      });

    const bin_ranges = await findRestrictedPinsForStore(deviceDetails?.store_id?.toString());

    const current_version = await CERTIFICATES.findOne().sort({ created_at: -1 });

    if (deviceDetails.serial_number) {
      this.sendSocketNotification({
        serial_number: deviceDetails.serial_number,
        event: event,
        data: {
          active_users: {
            type: 'ACTIVE_USERS',
            users: users,
            message: 'device users',
          },
          device_configurations: {
            type: 'DEVICE_CONFIGURATIONS',
            configuration: deviceDetails?.configuration,
            message: 'device details',
          },
          bin_ranges: {
            type: 'BIN_RANGES',
            bin_ranges: bin_ranges?.pins ?? [],
            message: 'bin ranges',
          },
          // cert_version: deviceDetails.cert_version,
          cert_version: current_version?.version,
          tagString: deviceDetails.tagString,
          updated_at: deviceDetails.updated_at,
          is_giftcard: deviceDetails?.business_id?.is_giftcard,
          is_report: deviceDetails?.business_id?.is_report,
        },
      });
    }
  });
};
