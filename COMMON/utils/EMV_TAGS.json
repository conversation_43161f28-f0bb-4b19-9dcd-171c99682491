[{"tag": "06", "name": "Object Identifier (OID)"}, {"tag": "41", "name": "Country code and national data"}, {"tag": "42", "name": "Issuer Identification Number (IIN)"}, {"tag": "43", "name": "Card service data"}, {"tag": "44", "name": "Initial access data"}, {"tag": "45", "name": "Card issuer`s data"}, {"tag": "46", "name": "Pre-issuing data"}, {"tag": "47", "name": "Card capabilities"}, {"tag": "48", "name": "Status information"}, {"tag": "4D", "name": "Extended header list"}, {"tag": "4F", "name": "Application Identifier (ADF Name)"}, {"tag": "50", "name": "Application Label"}, {"tag": "51", "name": "Path"}, {"tag": "52", "name": "Command to perform"}, {"tag": "53", "name": "Discretionary data, discretionary template"}, {"tag": "56", "name": "Track 1 Data"}, {"tag": "57", "name": "Track 2 Equivalent Data"}, {"tag": "58", "name": "Track 3 Equivalent Data"}, {"tag": "59", "name": "Card expiration date"}, {"tag": "5A", "name": "Application Primary Account Number (PAN)"}, {"tag": "5B", "name": "Name of an individual"}, {"tag": "5C", "name": "Tag list"}, {"tag": "5D", "name": "Deleted (see 9D)"}, {"tag": "5E", "name": "Proprietary login data"}, {"tag": "5F20", "name": "Cardholder Name"}, {"tag": "5F21", "name": "Track 1, identical to the data coded"}, {"tag": "5F22", "name": "Track 2, identical to the data coded"}, {"tag": "5F23", "name": "Track 3, identical to the data coded"}, {"tag": "5F24", "name": "Application Expiration Date"}, {"tag": "5F25", "name": "Application Effective Date"}, {"tag": "5F26", "name": "Date, Card Effective"}, {"tag": "5F27", "name": "Interchange control"}, {"tag": "5F28", "name": "Issuer Country Code"}, {"tag": "5F29", "name": "Interchange profile"}, {"tag": "5F2A", "name": "Transaction Currency Code"}, {"tag": "5F2B", "name": "Date of birth"}, {"tag": "5F2C", "name": "Cardholder nationality"}, {"tag": "5F2D", "name": "Language Preference"}, {"tag": "5F2E", "name": "Cardholder biometric data"}, {"tag": "5F2F", "name": "PIN usage policy"}, {"tag": "5F30", "name": "Service Code"}, {"tag": "5F32", "name": "Transaction counter"}, {"tag": "5F33", "name": "Date, Transaction"}, {"tag": "5F34", "name": "Application Primary Account Number (PAN) Sequence Number (PSN)"}, {"tag": "5F35", "name": "Sex (ISO 5218)"}, {"tag": "5F36", "name": "Transaction Currency Exponent"}, {"tag": "5F37", "name": "Static internal authentication (one-step)"}, {"tag": "5F38", "name": "Static internal authentication - first associated data"}, {"tag": "5F39", "name": "Static internal authentication - second associated data"}, {"tag": "5F3A", "name": "Dynamic internal authentication"}, {"tag": "5F3B", "name": "Dynamic external authentication"}, {"tag": "5F3C", "name": "Transaction Reference Currency Code"}, {"tag": "5F3D", "name": "Transaction Reference Currency Exponent"}, {"tag": "5F40", "name": "Cardholder portrait image"}, {"tag": "5F41", "name": "Element list"}, {"tag": "5F42", "name": "Address"}, {"tag": "5F43", "name": "Cardholder handwritten signature image"}, {"tag": "5F44", "name": "Application image"}, {"tag": "5F45", "name": "Display message"}, {"tag": "5F46", "name": "Timer"}, {"tag": "5F47", "name": "Message reference"}, {"tag": "5F48", "name": "Cardholder private key"}, {"tag": "5F49", "name": "Cardholder public key"}, {"tag": "5F4A", "name": "Public key of certification authority"}, {"tag": "5F4B", "name": "Deprecated (see note 2 below)"}, {"tag": "5F4C", "name": "Certificate holder authorization"}, {"tag": "5F4D", "name": "Integrated circuit manufacturer identifier"}, {"tag": "5F4E", "name": "Certificate content"}, {"tag": "5F50", "name": "Issuer Uniform resource locator (URL)"}, {"tag": "5F53", "name": "International Bank Account Number (IBAN)"}, {"tag": "5F54", "name": "Bank Identifier Code (BIC)"}, {"tag": "5F55", "name": "Issuer Country Code (alpha2 format)"}, {"tag": "5F56", "name": "Issuer Country Code (alpha3 format)"}, {"tag": "5F57", "name": "Account Type"}, {"tag": "60", "name": "Template, Dynamic Authentication"}, {"tag": "6080", "name": "Commitment (e.g., a positive number less than the public RSA modulus in use)"}, {"tag": "6081", "name": "Challenge (e.g., a number, possibly zero, less than the public RSA exponent in use)"}, {"tag": "6082", "name": "Response (e.g., a positive number less than the public RSA modulus in use)"}, {"tag": "6083", "name": "Committed challenge (e.g., the hash-code of a commitment data object)"}, {"tag": "6084", "name": "Authentication code (e.g., the hash-code of one or more data fields and a commitment data object)"}, {"tag": "6085", "name": "Exponential (e.g., a public positive number for establishing a session key by a DH method)"}, {"tag": "60A0", "name": "Template, Identification data"}, {"tag": "61", "name": "Application Template"}, {"tag": "62", "name": "File Control Parameters (FCP) Template"}, {"tag": "6280", "name": "Number of data bytes in the file, excluding structural information"}, {"tag": "6281", "name": "Number of data bytes in the file, including structural information if any"}, {"tag": "6282", "name": "File descriptor byte"}, {"tag": "6283", "name": "File identifier"}, {"tag": "6284", "name": "DF name"}, {"tag": "6285", "name": "Proprietary information, primitive encoding (i.e., not coded in BER-TLV)"}, {"tag": "6286", "name": "Security attribute in proprietary format"}, {"tag": "6287", "name": "Identifier of an EF containing an extension of the file control information"}, {"tag": "6288", "name": "Short EF identifier"}, {"tag": "628A", "name": "Life cycle status byte (LCS)"}, {"tag": "628B", "name": "Security attribute referencing the expanded format"}, {"tag": "628C", "name": "Security attribute in compact format"}, {"tag": "628D", "name": "Identifier of an EF containing security environment templates"}, {"tag": "62A0", "name": "Template, Security attribute for data objects"}, {"tag": "62A1", "name": "Template, Security attribute for physical interfaces"}, {"tag": "62A2", "name": "One or more pairs of data objects, short EF identifier (tag 88) - absolute or relative path (tag 51)"}, {"tag": "62A5", "name": "Proprietary information, constructed encoding"}, {"tag": "62AB", "name": "Security attribute in expanded format"}, {"tag": "62AC", "name": "Identifier of a cryptographic mechanism"}, {"tag": "63", "name": "Wrapper"}, {"tag": "64", "name": "Template, File Management Data (FMD)"}, {"tag": "65", "name": "Cardholder related data"}, {"tag": "66", "name": "Template, Card data"}, {"tag": "67", "name": "Template, Authentication data"}, {"tag": "68", "name": "Special user requirements"}, {"tag": "6A", "name": "Template, Login"}, {"tag": "6A80", "name": "Qualifier"}, {"tag": "6A81", "name": "Telephone Number"}, {"tag": "6A82", "name": "Text"}, {"tag": "6A83", "name": "Delay indicators, for detecting an end of message"}, {"tag": "6A84", "name": "Delay indicators, for detecting an absence of response"}, {"tag": "6B", "name": "<PERSON><PERSON><PERSON>, Qualified name"}, {"tag": "6B06", "name": "Qualified name"}, {"tag": "6B80", "name": "Name"}, {"tag": "6BA0", "name": "Name"}, {"tag": "6C", "name": "Template, Cardholder image"}, {"tag": "6D", "name": "Template, Application image"}, {"tag": "6E", "name": "Application related data"}, {"tag": "6F", "name": "File Control Information (FCI) Template"}, {"tag": "6FA5", "name": "Template, FCI A5"}, {"tag": "70", "name": "READ RECORD Response Message Template"}, {"tag": "71", "name": "Issuer Script Template 1"}, {"tag": "7186", "name": "Issuer Script Command"}, {"tag": "719F18", "name": "Issuer Script Identifier"}, {"tag": "72", "name": "Issuer Script Template 2"}, {"tag": "73", "name": "Directory Discretionary Template"}, {"tag": "77", "name": "Response Message Template Format 2"}, {"tag": "78", "name": "Compatible Tag Allocation Authority"}, {"tag": "79", "name": "Coexistent Tag Allocation Authority"}, {"tag": "7A", "name": "Template, Security Support (SS)"}, {"tag": "7A80", "name": "Card session counter"}, {"tag": "7A81", "name": "Session identifier"}, {"tag": "7A82", "name": "File selection counter"}, {"tag": "7A83", "name": "File selection counter"}, {"tag": "7A84", "name": "File selection counter"}, {"tag": "7A85", "name": "File selection counter"}, {"tag": "7A86", "name": "File selection counter"}, {"tag": "7A87", "name": "File selection counter"}, {"tag": "7A88", "name": "File selection counter"}, {"tag": "7A89", "name": "File selection counter"}, {"tag": "7A8A", "name": "File selection counter"}, {"tag": "7A8B", "name": "File selection counter"}, {"tag": "7A8C", "name": "File selection counter"}, {"tag": "7A8D", "name": "File selection counter"}, {"tag": "7A8E", "name": "File selection counter"}, {"tag": "7A93", "name": "Digital signature counter"}, {"tag": "7A9F2X", "name": "Internal progression value ('X'-is a specific index, e.g., an index referencing a counter of file selections)"}, {"tag": "7A9F3Y", "name": "External progression value ('Y'-is a specific index, e.g., an index referencing an external time stamp)"}, {"tag": "7B", "name": "Template, Security Environment (SE)"}, {"tag": "7B80", "name": "SEID byte, mandatory"}, {"tag": "7B8A", "name": "LCS byte, optional"}, {"tag": "7BAC", "name": "Cryptographic mechanism identifier template, optional"}, {"tag": "7BA4", "name": "Control reference template (CRT)"}, {"tag": "7BAA", "name": "Control reference template (CRT)"}, {"tag": "7BB4", "name": "Control reference template (CRT)"}, {"tag": "7BB6", "name": "Control reference template (CRT)"}, {"tag": "7BB8", "name": "Control reference template (CRT)"}, {"tag": "7D", "name": "Template, Secure Messaging (SM)"}, {"tag": "7D80", "name": "Plain value not coded in BER-TLV"}, {"tag": "7D81", "name": "Plain value not coded in BER-TLV"}, {"tag": "7D82", "name": "Cryptogram (plain value coded in BER-TLV and including secure messaging data objects)"}, {"tag": "7D83", "name": "Cryptogram (plain value coded in BER-TLV and including secure messaging data objects)"}, {"tag": "7D84", "name": "Cryptogram (plain value coded in BER-TLV, but not including secure messaging data objects)"}, {"tag": "7D85", "name": "Cryptogram (plain value coded in BER-TLV, but not including secure messaging data objects)"}, {"tag": "7D86", "name": "Padding-content indicator byte followed by cryptogram (plain value not coded in BER-TLV)"}, {"tag": "7D87", "name": "Padding-content indicator byte followed by cryptogram (plain value not coded in BER-TLV)"}, {"tag": "7D8E", "name": "Cryptographic checksum (at least four bytes)"}, {"tag": "7D90", "name": "Hash-code"}, {"tag": "7D91", "name": "Hash-code"}, {"tag": "7D92", "name": "Certificate (not BER-TLV coded data)"}, {"tag": "7D93", "name": "Certificate (not BER-TLV coded data)"}, {"tag": "7D94", "name": "Security environment identifier (SEID byte, see 6.5)"}, {"tag": "7D95", "name": "Security environment identifier (SEID byte, see 6.5)"}, {"tag": "7D96", "name": "Number Le in the unsecured command APDU (one or two bytes)"}, {"tag": "7D97", "name": "Number Le in the unsecured command APDU (one or two bytes)"}, {"tag": "7D99", "name": "Processing status of the secured response APDU (new SW1-SW2, two bytes)"}, {"tag": "7D9A", "name": "Input data element for the computation of a digital signature (the value field is signed)"}, {"tag": "7D9B", "name": "Input data element for the computation of a digital signature (the value field is signed)"}, {"tag": "7D9C", "name": "Public key"}, {"tag": "7D9D", "name": "Public key"}, {"tag": "7D9E", "name": "Digital signature"}, {"tag": "7DA0", "name": "Input template for the computation of a hash-code (the template is hashed)"}, {"tag": "7DA1", "name": "Input template for the computation of a hash-code (the template is hashed)"}, {"tag": "7DA2", "name": "Input template for the verification of a cryptographic checksum (the template is integrated)"}, {"tag": "7DA4", "name": "Control reference template for authentication (AT)"}, {"tag": "7DA5", "name": "Control reference template for authentication (AT)"}, {"tag": "7DA8", "name": "Input template for the verification of a digital signature (the template is signed)"}, {"tag": "7DAA", "name": "Template, Control reference for hash-code (HT)"}, {"tag": "7DAB", "name": "Template, Control reference for hash-code (HT)"}, {"tag": "7DAC", "name": "Input template for the computation of a digital signature (the concatenated value fields are signed)"}, {"tag": "7DAD", "name": "Input template for the computation of a digital signature (the concatenated value fields are signed)"}, {"tag": "7DAE", "name": "Input template for the computation of a certificate (the concatenated value fields are certified)"}, {"tag": "7DAF", "name": "Input template for the computation of a certificate (the concatenated value fields are certified)"}, {"tag": "7DB0", "name": "Plain value coded in BER-TLV and including secure messaging data objects"}, {"tag": "7DB1", "name": "Plain value coded in BER-TLV and including secure messaging data objects"}, {"tag": "7DB2", "name": "Plain value coded in BER-TLV, but not including secure messaging data objects"}, {"tag": "7DB3", "name": "Plain value coded in BER-TLV, but not including secure messaging data objects"}, {"tag": "7DB4", "name": "Control reference template for cryptographic checksum (CCT)"}, {"tag": "7DB5", "name": "Control reference template for cryptographic checksum (CCT)"}, {"tag": "7DB6", "name": "Control reference template for digital signature (DST)"}, {"tag": "7DB7", "name": "Control reference template for digital signature (DST)"}, {"tag": "7DB8", "name": "Control reference template for confidentiality (CT)"}, {"tag": "7DB9", "name": "Control reference template for confidentiality (CT)"}, {"tag": "7DBA", "name": "Response descriptor template"}, {"tag": "7DBB", "name": "Response descriptor template"}, {"tag": "7DBC", "name": "Input template for the computation of a digital signature (the template is signed)"}, {"tag": "7DBD", "name": "Input template for the computation of a digital signature (the template is signed)"}, {"tag": "7DBE", "name": "Input template for the verification of a certificate (the template is certified)"}, {"tag": "7E", "name": "Template, Nesting Interindustry data objects"}, {"tag": "7F20", "name": "Display control template"}, {"tag": "7F21", "name": "Cardholder certificate"}, {"tag": "7F2E", "name": "Biometric data template"}, {"tag": "7F49", "name": "Template, Cardholder public key"}, {"tag": "7F4980", "name": "Algorithm reference as used in control reference data objects for secure messaging"}, {"tag": "7F4981", "name": "RSA Modulus (a number denoted as n coded on x bytes), or DSA First prime (a number denoted as p coded on y bytes), or ECDSA Prime (a number denoted as p coded on\n\t\t\t\t\t\t\tz bytes)"}, {"tag": "7F4982", "name": "RSA Public exponent (a number denoted as v, e.g., 65537), or DSA Second prime (a number denoted as q dividing p-1, e.g., 20 bytes), or ECDSA First coefficient (a\n\t\t\t\t\t\t\tnumber denoted as a coded on z bytes)"}, {"tag": "7F4983", "name": "DSA Basis (a number denoted as g of order q coded on y bytes), or ECDSA Second coefficient (a number denoted as b coded on z bytes)"}, {"tag": "7F4984", "name": "DSA Public key (a number denoted as y equal to g to the power x mod p where x is the private key coded on y bytes), or ECDSA Generator (a point denoted as PB on\n\t\t\t\t\t\t\tthe curve, coded on 2z bytes)"}, {"tag": "7F4985", "name": "ECDSA Order (a prime number denoted as q, order of the generator PB, coded on z bytes)"}, {"tag": "7F4986", "name": "ECDSA Public key (a point denoted as PP on the curve, equal to x times PB where x is the private key, coded on 2z bytes)"}, {"tag": "7F4C", "name": "Template, Certificate Holder Authorization"}, {"tag": "7F4E", "name": "Certificate Body"}, {"tag": "7F4E42", "name": "Certificate Authority Reference"}, {"tag": "7F4E5F20", "name": "Certificate Holder Reference"}, {"tag": "7F4E5F24", "name": "Expiration Date, Certificate"}, {"tag": "7F4E5F25", "name": "Effective Date, Certificate"}, {"tag": "7F4E5F29", "name": "Certificate Profile Identifier"}, {"tag": "7F4E65", "name": "Certificate Extensions"}, {"tag": "7F60", "name": "Template, Biometric information"}, {"tag": "80", "name": "Response Message Template Format 1"}, {"tag": "81", "name": "Amount, Authorised (Binary)"}, {"tag": "82", "name": "Application Interchange Profile (AIP)"}, {"tag": "83", "name": "Command Template"}, {"tag": "84", "name": "Dedicated File (DF) Name"}, {"tag": "86", "name": "Issuer Script Command"}, {"tag": "87", "name": "Application Priority Indicator"}, {"tag": "88", "name": "Short File Identifier (SFI)"}, {"tag": "89", "name": "Authorisation Code"}, {"tag": "8A", "name": "Authorisation Response Code (ARC)"}, {"tag": "8C", "name": "Card Risk Management Data Object List 1 (CDOL1)"}, {"tag": "8D", "name": "Card Risk Management Data Object List 2 (CDOL2)"}, {"tag": "8E", "name": "Cardholder Verification Method (CVM) List"}, {"tag": "8F", "name": "Certification Authority Public Key Index (PKI)"}, {"tag": "90", "name": "Issuer Public Key Certificate"}, {"tag": "91", "name": "Issuer Authentication Data"}, {"tag": "92", "name": "Issuer Public Key Remainder"}, {"tag": "93", "name": "Signed Static Application Data (SAD)"}, {"tag": "94", "name": "Application File Locator (AFL)"}, {"tag": "95", "name": "Terminal Verification Results (TVR)"}, {"tag": "97", "name": "Transaction Certificate Data Object List (TDOL)"}, {"tag": "98", "name": "Transaction Certificate (TC) Hash Value"}, {"tag": "99", "name": "Transaction Personal Identification Number (PIN) Data"}, {"tag": "9A", "name": "Transaction Date"}, {"tag": "9B", "name": "Transaction Status Information (TSI)"}, {"tag": "9C", "name": "Transaction Type"}, {"tag": "9D", "name": "Directory Definition File (DDF) Name"}, {"tag": "9F01", "name": "Acquirer Identifier"}, {"tag": "9F02", "name": "Amount, Authorised (Numeric)"}, {"tag": "9F03", "name": "Amount, Other (Numeric)"}, {"tag": "9F04", "name": "Amount, Other (Binary)"}, {"tag": "9F05", "name": "Application Discretionary Data"}, {"tag": "9F06", "name": "Application Identifier (AID), Terminal"}, {"tag": "9F07", "name": "Application Usage Control (AUC)"}, {"tag": "9F08", "name": "Application Version Number"}, {"tag": "9F09", "name": "Application Version Number"}, {"tag": "9F0B", "name": "Cardholder Name - Extended"}, {"tag": "9F0D", "name": "Issuer Action Code - Default"}, {"tag": "9F0E", "name": "Issuer Action Code - Denial"}, {"tag": "9F0F", "name": "Issuer Action Code - Online"}, {"tag": "9F10", "name": "Issuer Application Data (IAD)"}, {"tag": "9F11", "name": "Issuer Code Table Index"}, {"tag": "9F12", "name": "Application Preferred Name"}, {"tag": "9F13", "name": "Last Online Application Transaction Counter (ATC) Register"}, {"tag": "9F14", "name": "Lower Consecutive Offline Limit (LCOL)"}, {"tag": "9F15", "name": "Merchant Category Code (MCC)"}, {"tag": "9F16", "name": "Merchant Identifier"}, {"tag": "9F17", "name": "Personal Identification Number (PIN) Try Counter"}, {"tag": "9F18", "name": "Issuer Script Identifier"}, {"tag": "9F19", "name": "Deleted (see 9F49)"}, {"tag": "9F1A", "name": "Terminal Country Code"}, {"tag": "9F1B", "name": "Terminal Floor Limit"}, {"tag": "9F1C", "name": "Terminal Identification"}, {"tag": "9F1D", "name": "Terminal Risk Management Data"}, {"tag": "9F1E", "name": "Interface Device (IFD) Serial Number"}, {"tag": "9F1F", "name": "Track 1 Discretionary Data"}, {"tag": "krn1", "name": "Track 1 Discretionary Data"}, {"tag": "9F20", "name": "Track 2 Discretionary Data"}, {"tag": "9F21", "name": "Transaction Time"}, {"tag": "9F22", "name": "Certification Authority Public Key Index (PKI)"}, {"tag": "9F23", "name": "Upper Consecutive Offline Limit (UCOL)"}, {"tag": "9F24", "name": "Payment Account Reference (PAR) generated or linked directly to the provision request in the token vault"}, {"tag": "9F26", "name": "Application Cryptogram (AC)"}, {"tag": "9F27", "name": "Cryptogram Information Data (CID)"}, {"tag": "9F29", "name": "Extended Selection"}, {"tag": "9F2A", "name": "Kernel Identifier"}, {"tag": "9F2D", "name": "Integrated Circuit Card (ICC) PIN Encipherment Public Key Certificate"}, {"tag": "9F2E", "name": "Integrated Circuit Card (ICC) PIN Encipherment Public Key Exponent"}, {"tag": "9F2F", "name": "Integrated Circuit Card (ICC) PIN Encipherment Public Key Remainder"}, {"tag": "9F32", "name": "Issuer Public Key Exponent"}, {"tag": "9F33", "name": "Terminal Capabilities"}, {"tag": "9F34", "name": "Cardholder Verification Method (CVM) Results"}, {"tag": "9F35", "name": "Terminal Type"}, {"tag": "9F36", "name": "Application Transaction Counter (ATC)"}, {"tag": "9F37", "name": "Unpredictable Number (UN)"}, {"tag": "9F38", "name": "Processing Options Data Object List (PDOL)"}, {"tag": "9F39", "name": "Point-of-Service (POS) Entry Mode"}, {"tag": "9F3A", "name": "Amount, Reference Currency (Binary)"}, {"tag": "9F3B", "name": "Currency Code, Application Reference"}, {"tag": "9F3C", "name": "Currency Code, Transaction Reference"}, {"tag": "9F3D", "name": "Currency Exponent, Transaction Reference"}, {"tag": "9F40", "name": "Additional Terminal Capabilities (ATC)"}, {"tag": "9F41", "name": "Transaction Sequence Counter"}, {"tag": "9F42", "name": "Currency Code, Application"}, {"tag": "9F43", "name": "Currency Exponent, Application Reference"}, {"tag": "9F44", "name": "Currency Exponent, Application"}, {"tag": "9F45", "name": "Data Authentication Code"}, {"tag": "9F46", "name": "Integrated Circuit Card (ICC) Public Key Certificate"}, {"tag": "9F47", "name": "Integrated Circuit Card (ICC) Public Key Exponent"}, {"tag": "9F48", "name": "Integrated Circuit Card (ICC) Public Key Remainder"}, {"tag": "9F49", "name": "Dynamic Data Authentication Data Object List (DDOL)"}, {"tag": "9F4A", "name": "Static Data Authentication Tag List (SDA)"}, {"tag": "9F4B", "name": "Signed Dynamic Application Data (SDAD)"}, {"tag": "9F4C", "name": "ICC Dynamic Number"}, {"tag": "9F4D", "name": "Log Entry"}, {"tag": "9F4E", "name": "Merchant Name and Location"}, {"tag": "9F4F", "name": "Log Format"}, {"tag": "9F50", "name": "Offline Accumulator Balance"}, {"tag": "9F51", "name": "Application Currency Code"}, {"tag": "9F52", "name": "Application Default Action (ADA)"}, {"tag": "9F53", "name": "Consecutive Transaction Counter International Limit (CTCIL)"}, {"tag": "9F54", "name": "Cumulative Total Transaction Amount Limit (CTTAL)"}, {"tag": "9F55", "name": "Geographic Indicator"}, {"tag": "9F56", "name": "Issuer Authentication Indicator"}, {"tag": "9F57", "name": "Issuer Country Code"}, {"tag": "9F58", "name": "Consecutive Transaction Counter Limit (CTCL)"}, {"tag": "9F59", "name": "Consecutive Transaction Counter Upper Limit (CTCUL)"}, {"tag": "9F5A", "name": "Application Program Identifier (Program ID)"}, {"tag": "9F5B", "name": "Issuer Script Results"}, {"tag": "9F5C", "name": "Cumulative Total Transaction Amount Upper Limit (CTTAUL)"}, {"tag": "9F5D", "name": "Available Offline Spending Amount (AOSA)"}, {"tag": "9F5E", "name": "Consecutive Transaction International Upper Limit (CTIUL)"}, {"tag": "9F5F", "name": "DS Slot Availability"}, {"tag": "9F60", "name": "CVC3 (Track1)"}, {"tag": "9F61", "name": "CVC3 (Track2)"}, {"tag": "9F62", "name": "PCVC3 (Track1)"}, {"tag": "9F63", "name": "Offline Counter Initial Value"}, {"tag": "9F64", "name": "NATC (Track1)"}, {"tag": "9F65", "name": "PCVC3 (Track2)"}, {"tag": "9F66", "name": "Terminal Transaction Qualifiers (TTQ)"}, {"tag": "9F67", "name": "MSD Offset"}, {"tag": "9F68", "name": "Card Additional Processes"}, {"tag": "9F69", "name": "Card Authentication Related Data"}, {"tag": "9F6A", "name": "Unpredictable Number (Numeric)"}, {"tag": "9F6B", "name": "Card CVM Limit"}, {"tag": "9F6C", "name": "Card Transaction Qualifiers (CTQ)"}, {"tag": "9F6D", "name": "VLP Reset Threshold"}, {"tag": "9F6E", "name": "Third Party Data"}, {"tag": "9F6F", "name": "DS Slot Management Control"}, {"tag": "9F70", "name": "Protected Data Envelope 1"}, {"tag": "9F71", "name": "Protected Data Envelope 2"}, {"tag": "9F72", "name": "Protected Data Envelope 3"}, {"tag": "9F73", "name": "Protected Data Envelope 4"}, {"tag": "9F74", "name": "Protected Data Envelope 5"}, {"tag": "9F75", "name": "Unprotected Data Envelope 1"}, {"tag": "9F76", "name": "Unprotected Data Envelope 2"}, {"tag": "9F77", "name": "Unprotected Data Envelope 3"}, {"tag": "9F78", "name": "Unprotected Data Envelope 4"}, {"tag": "9F79", "name": "Unprotected Data Envelope 5"}, {"tag": "9F7B", "name": "VLP Terminal Transaction Limit"}, {"tag": "9F7C", "name": "Customer Exclusive Data (CED)"}, {"tag": "9F7D", "name": "DS Summary 1"}, {"tag": "9F7E", "name": "Mobile Support Indicator"}, {"tag": "9F7F", "name": "DS Unpredictable Number"}, {"tag": "A5", "name": "File Control Information (FCI) Proprietary Template"}, {"tag": "BF0C", "name": "File Control Information (FCI) Issuer Discretionary Data"}, {"tag": "BF50", "name": "Visa Fleet - CDO"}, {"tag": "BF60", "name": "Integrated Data Storage Record Update Template"}, {"tag": "C3", "name": "Card issuer action code -decline"}, {"tag": "C4", "name": "Card issuer action code -default"}, {"tag": "C5", "name": "Card issuer action code online"}, {"tag": "C6", "name": "PIN Try Limit"}, {"tag": "C7", "name": "CDOL 1 Related Data Length"}, {"tag": "C8", "name": "Card risk management country code"}, {"tag": "C9", "name": "Card risk management currency code"}, {"tag": "CA", "name": "Lower cumulative offline transaction amount"}, {"tag": "CB", "name": "Upper cumulative offline transaction amount"}, {"tag": "CD", "name": "Card Issuer Action Code (PayPass) - Default"}, {"tag": "CE", "name": "Card Issuer Action Code (PayPass) - Online"}, {"tag": "CF", "name": "Card Issuer Action Code (PayPass) - Decline"}, {"tag": "D1", "name": "Currency conversion table"}, {"tag": "D2", "name": "Integrated Data Storage Directory (IDSD)"}, {"tag": "D3", "name": "Additional check table"}, {"tag": "D5", "name": "Application Control"}, {"tag": "D6", "name": "Default ARPC response code"}, {"tag": "D7", "name": "Application Control (PayPass)"}, {"tag": "D8", "name": "AIP (PayPass)"}, {"tag": "D9", "name": "AFL (PayPass)"}, {"tag": "DA", "name": "Static CVC3-TRACK1"}, {"tag": "DB", "name": "Static CVC3-TRACK2"}, {"tag": "DC", "name": "IVCVC3-TRACK1"}, {"tag": "DD", "name": "IVCVC3-TRACK2"}, {"tag": "DF01", "name": "Encrypted PIN Block in Tag 9F62 - ISO 95641 Format 0"}, {"tag": "DF02", "name": "PEK Version Number"}, {"tag": "DF03", "name": "PIN Try Limit"}, {"tag": "DF04", "name": "PIN Try Counter (VSDC Application)"}, {"tag": "DF05", "name": "AIP - For VISA Contactless"}, {"tag": "DF06", "name": "Products permitted"}, {"tag": "DF07", "name": "Offline checks mandated"}, {"tag": "DF08", "name": "UDKmac"}, {"tag": "DF09", "name": "UDKenc"}, {"tag": "DF0B", "name": "Retries Permitted Limit"}, {"tag": "DF0C", "name": "Script Message Update"}, {"tag": "DF0D", "name": "Fleet Issuer Action Code - Default"}, {"tag": "DF0E", "name": "Fleet Issuer Action Code - Denial"}, {"tag": "DF0F", "name": "Fleet Issuer Action Code - Online"}, {"tag": "DF12", "name": "Vehicle Registration Number"}, {"tag": "DF13", "name": "DDA Public Modulus"}, {"tag": "DF14", "name": "Driver Name"}, {"tag": "DF15", "name": "Driver ID"}, {"tag": "DF16", "name": "Max Fill Volume"}, {"tag": "DF17", "name": "DDA Public Modulus Length"}, {"tag": "DF18", "name": "Mileage"}, {"tag": "DF20", "name": "Issuer Proprietary Bitmap (IPB)"}, {"tag": "DF21", "name": "Internet Authentication Flag (IAF)"}, {"tag": "DF22", "name": "Encrypted PEK - RFU"}, {"tag": "DF23", "name": "PEK Key Check Value - RFU"}, {"tag": "DF24", "name": "MDK - Key derivation Index"}, {"tag": "DF25", "name": "VISA DPA - MDK - Key derivation Index"}, {"tag": "DF26", "name": "Encrypted PIN Block - ISO 9564-1 Format 1 PIN Block (Thales P3 Format 05)"}, {"tag": "DF40", "name": "qVSDC AIP"}, {"tag": "DF41", "name": "VSDC AIP"}, {"tag": "DF42", "name": "UDKac"}, {"tag": "DF43", "name": "UDKmac"}, {"tag": "DF44", "name": "UDKenc"}, {"tag": "DF47", "name": "UDKcvc"}, {"tag": "DF48", "name": "UDKac KCV"}, {"tag": "DF49", "name": "UDKmac KCV"}, {"tag": "DF4A", "name": "UDKenc KCV"}, {"tag": "DF4B", "name": "UDKcvc KCV"}, {"tag": "DF51", "name": "Grand Parent AC"}, {"tag": "DF52", "name": "Parent AC"}, {"tag": "DF53", "name": "Grand Parent MAC"}, {"tag": "DF54", "name": "Parent MAC"}, {"tag": "DF55", "name": "Grand Parent ENC"}, {"tag": "DF56", "name": "Parent ENC/Terminal Action Code - Default"}, {"tag": "DF57", "name": "Terminal Action Code - Decline"}, {"tag": "DF60", "name": "DS Input (Card)"}, {"tag": "DF61", "name": "DDA Component Q"}, {"tag": "DF62", "name": "DS ODS Info"}, {"tag": "DF63", "name": "DDA Component D2"}, {"tag": "DF64", "name": "DDA Component Q Minus 1 Mod P"}, {"tag": "DF65", "name": "DDA Private Exponent"}, {"tag": "DF6B", "name": "Paypass Contactless"}, {"tag": "DF79", "name": "Dynamic Data Authentication Keys"}, {"tag": "DF8101", "name": "DS Summary 2"}, {"tag": "DF8102", "name": "DS Summary 3"}, {"tag": "DF8104", "name": "Balance Read Before Gen AC"}, {"tag": "DF8105", "name": "Balance Read After Gen AC"}, {"tag": "DF8106", "name": "Data Needed"}, {"tag": "DF8107", "name": "CDOL1 Related Data"}, {"tag": "DF8108", "name": "DS AC Type"}, {"tag": "DF8109", "name": "DS Input (Term)"}, {"tag": "DF810A", "name": "DS ODS Info For Reader"}, {"tag": "DF810B", "name": "DS Summary Status"}, {"tag": "DF810C", "name": "Kernel ID"}, {"tag": "DF810D", "name": "DSVN Term"}, {"tag": "DF810E", "name": "Post-Gen AC Put Data Status"}, {"tag": "DF810F", "name": "Pre-Gen AC Put Data Status"}, {"tag": "DF8110", "name": "Proceed To First Write Flag"}, {"tag": "DF8111", "name": "PDOL Related Data"}, {"tag": "DF8112", "name": "Tags To Read"}, {"tag": "DF8113", "name": "DRDOL Related Data"}, {"tag": "DF8114", "name": "Reference Control Parameter"}, {"tag": "DF8115", "name": "Error Indication"}, {"tag": "DF8116", "name": "User Interface Request Data"}, {"tag": "DF8117", "name": "Card Data Input Capability"}, {"tag": "DF8118", "name": "CVM Capability - CVM Required"}, {"tag": "DF8119", "name": "CVM Capability - No CVM Required"}, {"tag": "DF811A", "name": "Default UDOL"}, {"tag": "DF811B", "name": "Kernel Configuration"}, {"tag": "DF811C", "name": "Max Lifetime of Torn Transaction Log Record"}, {"tag": "DF811D", "name": "Max Number of Torn Transaction Log Records"}, {"tag": "DF811E", "name": "Mag-stripe CVM Capability - CVM Required"}, {"tag": "DF811F", "name": "Security Capability"}, {"tag": "DF8120", "name": "Terminal Action Code - Default"}, {"tag": "DF8121", "name": "Terminal Action Code - Denial"}, {"tag": "DF8122", "name": "Terminal Action Code - Online"}, {"tag": "DF8123", "name": "Reader Contactless Floor Limit"}, {"tag": "DF8124", "name": "Reader Contactless Transaction Limit (No On-device CVM)"}, {"tag": "DF8125", "name": "Reader Contactless Transaction Limit (On-device CVM)"}, {"tag": "DF8126", "name": "Reader CVM Required Limit"}, {"tag": "DF8127", "name": "Time Out Value"}, {"tag": "DF8128", "name": "IDS Status"}, {"tag": "DF8129", "name": "Outcome Parameter Set"}, {"tag": "DF812A", "name": "DD Card (Track1)"}, {"tag": "DF812B", "name": "DD Card (Track2)"}, {"tag": "DF812C", "name": "Mag-stripe CVM Capability - No CVM Required"}, {"tag": "DF812D", "name": "Message Hold Time"}, {"tag": "DF8130", "name": "Hold Time Value"}, {"tag": "DF8131", "name": "Phone Message Table"}, {"tag": "FF60", "name": "Visa International"}, {"tag": "FF62", "name": "Visa Magnetic Stripe"}, {"tag": "FF63", "name": "Visa Quick VSDC"}, {"tag": "FF8101", "name": "Torn Record"}, {"tag": "FF8102", "name": "Tags To Write Before Gen AC"}, {"tag": "FF8103", "name": "Tags To Write After Gen AC"}, {"tag": "FF8104", "name": "Data To Send"}, {"tag": "FF8105", "name": "Data Record"}, {"tag": "FF8106", "name": "Discretionary Data"}]