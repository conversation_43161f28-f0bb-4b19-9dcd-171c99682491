const moment = require('moment-timezone');
const {
  TERMINAL,
  CONFIGURATION,
  USER,
  STORE,
  RESTRICTED_PINS,
  MESSAGE_QUEUE,
  CERTIFICATES,
  BUSINESS,
} = require('../models');
const { exec } = require('child_process');
const redis = require('redis');
const aws = require('aws-sdk');
const path = require('path');
const {
  redis_url,
  aws_access_key,

  aws_bucket_name,
  aws_region,
  aws_secret_key_id,
  merchant_tool_api_key,
  merchant_tool_url,
} = require('../configs');

const axios = require('axios');
const { decryptCBC } = require('./cbc');

exports.filterQuery = req => ({
  ...req.query,
  page: req.query.page ? Number(req.query.page) : 1,
  itemsPerPage: req.query.itemsPerPage
    ? Number(req.query.itemsPerPage)
    : req.query.perPage
      ? Number(req.query.perPage)
      : 10,
  searchText:
    req.query.searchText !== 'null' && req.query.searchText !== 'undefined' && req.query.searchText
      ? req.query.searchText
      : '',
  startDate:
    req.query.startDate !== 'null' && req.query.startDate !== 'undefined' && req.query.startDate
      ? req.query.startDate
      : '',
  endDate:
    req.query.endDate !== 'null' && req.query.endDate !== 'undefined' && req.query.endDate ? req.query.endDate : '',
  storeId:
    req.query.storeId !== 'null' && req.query.storeId !== 'undefined' && req.query.storeId ? req.query.storeId : '',
});

exports.pagination = (items = [], page = 1, totalItems = 0, itemsPerPage = 5) => {
  return {
    currentPage: page,
    hasNextPage: itemsPerPage * page < totalItems,
    hasPreviousPage: page > 1,
    nextPage: page + 1,
    previousPage: page - 1,
    lastPage: Math.ceil(totalItems / itemsPerPage),
    totalItems: totalItems,
    items: items,
  };
};

exports.removeDuplicatesById = arr => {
  const uniqueIds = new Set(); // Set to keep track of unique IDs
  return arr.filter(obj => {
    if (!uniqueIds.has(obj._id.toString())) {
      uniqueIds.add(obj._id.toString());
      return true; // Include the object in the filtered array
    }
    return false; // Exclude the object from the filtered array
  });
};

exports.timeToMilliseconds = timeString => {
  const [hours, minutes] = timeString.split(':').map(Number);
  const totalMilliseconds = hours * 3600000 + minutes * 60000;
  return totalMilliseconds;
};

exports.millisecondsToTime = milliseconds => {
  const totalSeconds = Math.floor(milliseconds / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);

  const formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
  return formattedTime;
};

exports.minutesToMs = minutes => minutes * 60 * 1000;

exports.getCurrentTimeByZone = timeZoneInfo => {
  let { timeZoneName } = timeZoneInfo;
  if (!timeZoneName) {
    return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  const timeMoment = moment().tz(timeZoneName);

  return timeMoment.format('HH:mm');
};

exports.removeMessageFromQueue = async body => {
  try {
    await MESSAGE_QUEUE.findOneAndRemove({ serial_number: body.serial_number });
    return true;
  } catch (error) {
    console.error('removeMessageFromQueue', error);
    return false;
  }
};

exports.updatePushedVersion = async body => {
  try {
    await TERMINAL.findOneAndUpdate(
      { serial_number: body.serial_number, is_deleted: false },
      { app_version: body.app_version },
    );
    return true;
  } catch (error) {
    console.error('updatePushedVersion', error);
    return false;
  }
};

exports.updatePrintedReceipt = async body => {
  try {
    await TERMINAL.findOneAndUpdate(
      { serial_number: body.serial_number, is_deleted: false },
      { last_printed: body.date },
    );
    return true;
  } catch (error) {
    console.error('updatePrintedReceipt', error);
    return false;
  }
};

exports.setTerminalTimeZone = async (serial_number, zone) => {
  try {
    await TERMINAL.findOneAndUpdate(
      { serial_number, is_deleted: false },
      { $set: { 'setting.report_print.timeZoneName': zone } },
    );
    return true;
  } catch (error) {
    console.error('setTerminalTimeZone', error);
    return false;
  }
};
exports.setTerminalLocationCoordinates = async (serial_number, coordinates) => {
  try {
    const result = await TERMINAL.findOneAndUpdate(
      { serial_number, is_deleted: false },
      { $set: { location: { type: 'Point', coordinates: coordinates } } },
    );

    return true;
  } catch (error) {
    console.error('setTerminalLocationCoordinates', error);
    return false;
  }
};

exports.checkDevicePresence = async serial_number => {
  try {
    const result = await TERMINAL.findOne({
      serial_number,
      is_deleted: false,
      status: { $ne: 'ForcedDeactivated' },
      api_key: { $ne: '' },
    }).lean();
    return result;
  } catch (error) {
    console.error('checkDevicePresence', error);
    return false;
  }
};

exports.findStoreOfDevice = async store_id => {
  try {
    const store = await STORE.findById(store_id);
    return store;
  } catch (error) {
    console.error('findStoreOfDevice', error);
    return false;
  }
};

exports.findRestrictedPinsForStore = async store_id => {
  try {
    const restrictedPins = await RESTRICTED_PINS.findOne({ store_id }).select('pins');
    return restrictedPins;
  } catch (error) {
    console.error('findRestrictedPinsForStore', error);
    return false;
  }
};

exports.getDevicesForSocketNotifications = async query => {
  try {
    const devices = await TERMINAL.find({ is_deleted: false, ...query })
      .populate({
        path: 'configuration',
        model: CONFIGURATION,
      })
      .populate({
        path: 'user_id',
        model: USER,
      })
      .populate({
        path: 'business_id',
        model: BUSINESS,
        select: { is_giftcard: 1, mid: 1, is_report: 1 },
      })
      .lean();
    return devices || [];
  } catch (error) {
    console.error('getDevicesForSocketNotifications', error);
    return false;
  }
};

exports.execShellCommand = async cmd => {
  return new Promise((resolve, reject) => {
    exec(cmd, (error, stdout, stderr) => {
      if (error) {
        console.warn(error);
        reject(error);
      }
      resolve(stdout ? stdout : stderr);
    });
  });
};

exports.createFile = async (filePath, fileContent) => {
  const fsPromises = require('fs').promises;
  try {
    await fsPromises.writeFile(filePath, fileContent);
    console.log('File created successfully!');
  } catch (err) {
    console.error('Error creating file:', err);
  }
};

exports.createRedisClient = () => {
  const client = redis.createClient({ url: redis_url });
  client.connect();
  return client;
};

exports.updateTerminal = async ({ serial_number, version }) => {
  try {
    await TERMINAL.findOneAndUpdate({ serial_number, is_deleted: false }, { cert_version: version });
    return true;
  } catch (error) {
    console.error('updateTerminal', error);
    return false;
  }
};

exports.fetchNewCertificate = async () => {
  let res = await CERTIFICATES.findOne({}).sort({ created_at: -1 }).lean();
  res.ca = decryptCBC(res.ca);
  res.key = decryptCBC(res.key);
  res.cert = decryptCBC(res.cert);
  return res;
};

exports.upload_to_s3 = async data => {
  try {
    const s3 = new aws.S3({
      accessKeyId: aws_access_key,
      secretAccessKey: aws_secret_key_id,
      region: aws_region,
    });

    const fileName = data.fieldname + '-' + Date.now() + path.extname(data.originalname);

    const params = {
      Bucket: aws_bucket_name,
      Key: fileName,
      Body: Buffer.from(data.buffer, 'base64'),
      ContentType: data.mimetype,
    };
    let response = await s3.upload(params).promise();

    return response;
  } catch (ex) {
    console.log(ex);
    return {
      Location: '',
      errorMessage: {
        originalError: ex.message,
      },
    };
  }
};
exports.remove_from_s3 = async fileName => {
  try {
    const s3 = new aws.S3({
      accessKeyId: aws_access_key,
      secretAccessKey: aws_secret_key_id,
      region: aws_region,
    });

    const params = {
      Bucket: aws_bucket_name,
      Key: fileName,
    };
    let response = await s3.deleteObject(params).promise();
    return response;
  } catch {
    return { DeleteMarker: false };
  }
};

exports.sendToMerchantTools = async payload => {
  try {
    await axios.post(
      `${merchant_tool_url}/api/add-transaction`,
      { data: payload, type: 'PSPAPP' },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + merchant_tool_api_key,
        },
      },
    );
    return '';
  } catch (err) {
    console.log(err.message);
    return '';
  }
};
