const dotenv = require('dotenv');
dotenv.config();

module.exports = {
  env: process.env.NODE_ENV,
  window: process.env.WINDOW,
  max_limit: process.env.MAX_LIMIT,
  port: process.env.PORT,
  redis_url: process.env.REDIS_URL,
  secret: process.env.SECRET,
  mongo_string: process.env.MONGO_URI,
  mail_host: process.env.MAIL_HOST,
  mail_port: process.env.MAIL_PORT,
  mail_username: process.env.MAIL_USERNAME,
  mail_password: process.env.MAIL_PASSWORD,
  mail_from_address: process.env.MAIL_FROM_ADDRESS,
  mail_from_name: process.env.MAIL_FROM_NAME,
  access_key: process.env.ACCESS_KEY,
  twillo_account_ssid: process.env.TWILLO_ACCOUNT_SSID,
  twillo_auth_token: process.env.TWILLO_AUTH_TOKEN,
  twillo_number: process.env.TWILLO_NUMBER,
  websocket_path: process.env.WEBSOCKET_PATH,
  sendgrid_api_key: process.env.SENDGRID_API_KEY,
  sendgrid_user_name: process.env.SENDGRID_USER_NAME,
  sendgrid_host: process.env.SENDGRID_HOST,
  sendgrid_port: process.env.SENDGRID_PORT,
  sendgrid_email_from: process.env.SENDGRID_FROM_EMAIL,
  reward_api_secret: process.env.REWARD_API_SECRET,
  reward_api_url: process.env.REWARD_API_URL,
  aws_access_key: process.env.AWS_ACCESS_KEY,
  aws_secret_key_id: process.env.AWS_SECRET_KEY_ID,
  aws_region: process.env.AWS_REGION,
  aws_bucket_name: process.env.AWS_BUCKET_NAME,
  base_url: process.env.BASE_URL,

  mqtt_url: process.env.MQTT_URL,
  mqtt_port: process.env.MQTT_PORT,
  mqtt_ca: process.env.MQTT_CA,
  mqtt_cert: process.env.MQTT_CERT,
  mqtt_key: process.env.MQTT_KEY,

  merchant_tool_api_key: process.env.MERCHANT_TOOL_API_KEY,
  merchant_tool_url: process.env.MERCHANT_TOOL_URL,
  encryption_key: process.env.ENCRYPTION_KEY,
  enc_iv: process.env.ENC_IV,
};
