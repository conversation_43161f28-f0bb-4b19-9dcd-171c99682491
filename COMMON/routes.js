const express = require('express');
require('express-group-routes');
const router = express.Router();
const { commonController } = require('./controllers');
const { HAS_KEY, IS_ADMIN, HAS_API_KEY } = require('./middlewares');
const tryCatch = require('./utils/tryCatch');
const upload = require('./middlewares/multer');
const { getDeviceData } = require('./utils/mqtt-connection');

router.post('/upload', IS_ADMIN, upload.single('image'), tryCatch(commonController.uploadImageToS3));

  /**
   * @swagger
   * /common/v1/remove-image:
   *   delete:
   *     tags: [Remove Image Management]
   *     summary: Delete remove image
   *     description: Delete remove image in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete remove image successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/remove-image:
   *   delete:
   *     tags: [Remove Image Management]
   *     summary: Delete remove image
   *     description: Delete remove image in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete remove image successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.delete('/remove-image', IS_ADMIN, tryCatch(commonController.removeFromS3));


  /**
   * @swagger
   * /common/v1/send-socket-notification:
   *   post:
   *     tags: [Send Socket Notification Management]
   *     summary: Create send socket notification
   *     description: Create send socket notification in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send socket notification successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /common/v1/send-socket-notification:
   *   post:
   *     tags: [Send Socket Notification Management]
   *     summary: Create send socket notification
   *     description: Create send socket notification in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send socket notification successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
router.post('/send-socket-notification', HAS_KEY, tryCatch(commonController.socketnotification));

  /**
   * @swagger
   * /common/v1/get-connected-terminals:
   *   post:
   *     tags: [Get Connected Terminals Management]
   *     summary: Create get connected terminals
   *     description: Create get connected terminals in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create get connected terminals successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /common/v1/get-connected-terminals:
   *   post:
   *     tags: [Get Connected Terminals Management]
   *     summary: Create get connected terminals
   *     description: Create get connected terminals in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create get connected terminals successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
router.post('/get-connected-terminals', HAS_KEY, tryCatch(commonController.connectedTerminals));

  /**
   * @swagger
   * /common/v1/send-device-data:
   *   post:
   *     tags: [Send Device Data Management]
   *     summary: Create send device data
   *     description: Create send device data in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send device data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /common/v1/send-device-data:
   *   post:
   *     tags: [Send Device Data Management]
   *     summary: Create send device data
   *     description: Create send device data in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send device data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
router.post('/send-device-data', HAS_KEY, tryCatch(commonController.deviceData));

  /**
   * @swagger
   * /common/v1/get-device-data:
   *   get:
   *     tags: [Get Device Data Management]
   *     summary: Get get device data
   *     description: Retrieve get device data in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get device data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/get-device-data:
   *   get:
   *     tags: [Get Device Data Management]
   *     summary: Get get device data
   *     description: Retrieve get device data in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get device data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/get-device-data', HAS_API_KEY, tryCatch(getDeviceData));

  /**
   * @swagger
   * /common/v1/push-data-queue:
   *   post:
   *     tags: [Push Data Queue Management]
   *     summary: Create push data queue
   *     description: Create push data queue in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create push data queue successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /common/v1/push-data-queue:
   *   post:
   *     tags: [Push Data Queue Management]
   *     summary: Create push data queue
   *     description: Create push data queue in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create push data queue successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
router.post('/push-data-queue', HAS_KEY, tryCatch(commonController.pushDataQueue));

/**LOGS CURD */

  /**
   * @swagger
   * /common/v1/device-log:
   *   post:
   *     tags: [Device Log Management]
   *     summary: Create device log
   *     description: Create device log in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create device log successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /common/v1/device-log:
   *   post:
   *     tags: [Device Log Management]
   *     summary: Create device log
   *     description: Create device log in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create device log successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
router.post('/device-log', HAS_KEY, tryCatch(commonController.deviceLog));

  /**
   * @swagger
   * /common/v1/device-logs:
   *   get:
   *     tags: [Device Logs Management]
   *     summary: Get device logs
   *     description: Retrieve device logs in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get device logs successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/device-logs:
   *   get:
   *     tags: [Device Logs Management]
   *     summary: Get device logs
   *     description: Retrieve device logs in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get device logs successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/device-logs', IS_ADMIN, tryCatch(commonController.getDeviceLogs));


  /**
   * @swagger
   * /common/v1/all-in-one-search:
   *   get:
   *     tags: [All In One Search Management]
   *     summary: Get all in one search
   *     description: Retrieve all in one search in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get all in one search successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/all-in-one-search:
   *   get:
   *     tags: [All In One Search Management]
   *     summary: Get all in one search
   *     description: Retrieve all in one search in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get all in one search successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/all-in-one-search', IS_ADMIN, tryCatch(commonController.allInOneSearch));


  /**
   * @swagger
   * /common/v1/card-data:
   *   post:
   *     tags: [Card Data Management]
   *     summary: Create card data
   *     description: Create card data in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create card data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /common/v1/card-data:
   *   post:
   *     tags: [Card Data Management]
   *     summary: Create card data
   *     description: Create card data in the common service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create card data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
// router.post('/card-data', tryCatch(commonController.saveCardData));

  /**
   * @swagger
   * /common/v1/card-data/:id:
   *   get:
   *     tags: [Card Data Management]
   *     summary: Get card data
   *     description: Retrieve card data in the common service
   *     responses:
   *       200:
   *         description: Get card data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /common/v1/card-data/:id:
   *   get:
   *     tags: [Card Data Management]
   *     summary: Get card data
   *     description: Retrieve card data in the common service
   *     responses:
   *       200:
   *         description: Get card data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
// router.get('/card-data/:id', tryCatch(commonController.getCardData));

  /**
   * @swagger
   * /common/v1/get-all-cards-data:
   *   get:
   *     tags: [Get All Cards Data Management]
   *     summary: Get get all cards data
   *     description: Retrieve get all cards data in the common service
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all cards data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /common/v1/get-all-cards-data:
   *   get:
   *     tags: [Get All Cards Data Management]
   *     summary: Get get all cards data
   *     description: Retrieve get all cards data in the common service
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all cards data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
// router.get('/get-all-cards-data', tryCatch(commonController.getAllCardsData));


  /**
   * @swagger
   * /common/v1/create-certificate:
   *   post:
   *     tags: [Create Certificate Management]
   *     summary: Create create certificate
   *     description: Create create certificate in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create certificate successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/create-certificate:
   *   post:
   *     tags: [Create Certificate Management]
   *     summary: Create create certificate
   *     description: Create create certificate in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create certificate successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.post('/create-certificate', IS_ADMIN, tryCatch(commonController.createCertificate));

  /**
   * @swagger
   * /common/v1/get-all-certificates:
   *   get:
   *     tags: [Get All Certificates Management]
   *     summary: Get get all certificates
   *     description: Retrieve get all certificates in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all certificates successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/get-all-certificates:
   *   get:
   *     tags: [Get All Certificates Management]
   *     summary: Get get all certificates
   *     description: Retrieve get all certificates in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all certificates successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/get-all-certificates', IS_ADMIN, tryCatch(commonController.getAllCertificates));

  /**
   * @swagger
   * /common/v1/get-cert/:name:
   *   get:
   *     tags: [Get Cert Management]
   *     summary: Get get cert
   *     description: Retrieve get cert in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get cert successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/get-cert/:name:
   *   get:
   *     tags: [Get Cert Management]
   *     summary: Get get cert
   *     description: Retrieve get cert in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get cert successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/get-cert/:name', HAS_API_KEY, tryCatch(commonController.getCertificate));

  /**
   * @swagger
   * /common/v1/capk-file:
   *   get:
   *     tags: [Capk File Management]
   *     summary: Get capk file
   *     description: Retrieve capk file in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get capk file successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/capk-file:
   *   get:
   *     tags: [Capk File Management]
   *     summary: Get capk file
   *     description: Retrieve capk file in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get capk file successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/capk-file', HAS_API_KEY, tryCatch(commonController.getCAPKfile));


  /**
   * @swagger
   * /common/v1/redis/logs:
   *   get:
   *     tags: [Redis Management]
   *     summary: Get redis logs
   *     description: Retrieve redis logs in the common service
   *     responses:
   *       200:
   *         description: Get redis logs successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /common/v1/redis/logs:
   *   get:
   *     tags: [Redis Management]
   *     summary: Get redis logs
   *     description: Retrieve redis logs in the common service
   *     responses:
   *       200:
   *         description: Get redis logs successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
// router.get('/redis/logs', tryCatch(commonController.redisLogs));

  /**
   * @swagger
   * /common/v1/redis/clear:
   *   get:
   *     tags: [Redis Management]
   *     summary: Get redis clear
   *     description: Retrieve redis clear in the common service
   *     responses:
   *       200:
   *         description: Get redis clear successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /common/v1/redis/clear:
   *   get:
   *     tags: [Redis Management]
   *     summary: Get redis clear
   *     description: Retrieve redis clear in the common service
   *     responses:
   *       200:
   *         description: Get redis clear successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
// router.get('/redis/clear', tryCatch(commonController.redisClear));

  /**
   * @swagger
   * /common/v1/redis-ping:
   *   get:
   *     tags: [Redis Ping Management]
   *     summary: Get redis ping
   *     description: Retrieve redis ping in the common service
   *     responses:
   *       200:
   *         description: Get redis ping successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /common/v1/redis-ping:
   *   get:
   *     tags: [Redis Ping Management]
   *     summary: Get redis ping
   *     description: Retrieve redis ping in the common service
   *     responses:
   *       200:
   *         description: Get redis ping successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
// router.get('/redis-ping', tryCatch(commonController.checkRedisConnection));

// Integration with Points Engine

  /**
   * @swagger
   * /common/v1/webhook:
   *   post:
   *     tags: [Webhook Management]
   *     summary: Create webhook
   *     description: Create webhook in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create webhook successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/webhook:
   *   post:
   *     tags: [Webhook Management]
   *     summary: Create webhook
   *     description: Create webhook in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create webhook successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.post('/webhook', HAS_API_KEY, tryCatch(commonController.pointsNotification));

  /**
   * @swagger
   * /common/v1/points-data/:userId:
   *   get:
   *     tags: [Points Data Management]
   *     summary: Get points data
   *     description: Retrieve points data in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get points data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/points-data/:userId:
   *   get:
   *     tags: [Points Data Management]
   *     summary: Get points data
   *     description: Retrieve points data in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get points data successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/points-data/:userId', HAS_API_KEY, tryCatch(commonController.getPointsdata));

  /**
   * @swagger
   * /common/v1/verify-pin:
   *   post:
   *     tags: [Verify Pin Management]
   *     summary: Create verify pin
   *     description: Create verify pin in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create verify pin successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/verify-pin:
   *   post:
   *     tags: [Verify Pin Management]
   *     summary: Create verify pin
   *     description: Create verify pin in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create verify pin successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.post('/verify-pin', HAS_API_KEY, tryCatch(commonController.verifyPin));

  /**
   * @swagger
   * /common/v1/send-point-request:
   *   post:
   *     tags: [Send Point Request Management]
   *     summary: Create send point request
   *     description: Create send point request in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send point request successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/send-point-request:
   *   post:
   *     tags: [Send Point Request Management]
   *     summary: Create send point request
   *     description: Create send point request in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send point request successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.post('/send-point-request', HAS_API_KEY, tryCatch(commonController.sendPointRequest));

  /**
   * @swagger
   * /common/v1/complete-point-request:
   *   post:
   *     tags: [Complete Point Request Management]
   *     summary: Create complete point request
   *     description: Create complete point request in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create complete point request successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/complete-point-request:
   *   post:
   *     tags: [Complete Point Request Management]
   *     summary: Create complete point request
   *     description: Create complete point request in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create complete point request successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.post('/complete-point-request', HAS_API_KEY, tryCatch(commonController.completePointRequest));

// EMV Modules


  /**
   * @swagger
   * /common/v1/search-validate-tag:
   *   get:
   *     tags: [Search Validate Tag Management]
   *     summary: Get search validate tag
   *     description: Retrieve search validate tag in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get search validate tag successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/search-validate-tag:
   *   get:
   *     tags: [Search Validate Tag Management]
   *     summary: Get search validate tag
   *     description: Retrieve search validate tag in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get search validate tag successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/search-validate-tag', IS_ADMIN, tryCatch(commonController.searchTag));

  /**
   * @swagger
   * /common/v1/tag:
   *   post:
   *     tags: [Tag Management]
   *     summary: Create tag
   *     description: Create tag in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create tag successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/tag:
   *   post:
   *     tags: [Tag Management]
   *     summary: Create tag
   *     description: Create tag in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create tag successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.post('/tag', IS_ADMIN, tryCatch(commonController.createTag));

  /**
   * @swagger
   * /common/v1/tag/:id:
   *   delete:
   *     tags: [Tag Management]
   *     summary: Delete tag
   *     description: Delete tag in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete tag successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/tag/:id:
   *   delete:
   *     tags: [Tag Management]
   *     summary: Delete tag
   *     description: Delete tag in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete tag successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.delete('/tag/:id', IS_ADMIN, tryCatch(commonController.deleteTag));

  /**
   * @swagger
   * /common/v1/tag/:id:
   *   put:
   *     tags: [Tag Management]
   *     summary: Update tag
   *     description: Update tag in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update tag successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/tag/:id:
   *   put:
   *     tags: [Tag Management]
   *     summary: Update tag
   *     description: Update tag in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update tag successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.put('/tag/:id', IS_ADMIN, tryCatch(commonController.updateTag));

  /**
   * @swagger
   * /common/v1/tags:
   *   get:
   *     tags: [Tags Management]
   *     summary: Get tags
   *     description: Retrieve tags in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get tags successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/tags:
   *   get:
   *     tags: [Tags Management]
   *     summary: Get tags
   *     description: Retrieve tags in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get tags successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/tags', IS_ADMIN, tryCatch(commonController.getAllTags));


  /**
   * @swagger
   * /common/v1/tag-template:
   *   post:
   *     tags: [Tag Template Management]
   *     summary: Create tag template
   *     description: Create tag template in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create tag template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/tag-template:
   *   post:
   *     tags: [Tag Template Management]
   *     summary: Create tag template
   *     description: Create tag template in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create tag template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.post('/tag-template', IS_ADMIN, tryCatch(commonController.createTagTemplate));

  /**
   * @swagger
   * /common/v1/tag-template:
   *   get:
   *     tags: [Tag Template Management]
   *     summary: Get tag template
   *     description: Retrieve tag template in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get tag template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/tag-template:
   *   get:
   *     tags: [Tag Template Management]
   *     summary: Get tag template
   *     description: Retrieve tag template in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get tag template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/tag-template', IS_ADMIN, tryCatch(commonController.getAllTagTemplates));

  /**
   * @swagger
   * /common/v1/tag-template/:id:
   *   put:
   *     tags: [Tag Template Management]
   *     summary: Update tag template
   *     description: Update tag template in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update tag template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/tag-template/:id:
   *   put:
   *     tags: [Tag Template Management]
   *     summary: Update tag template
   *     description: Update tag template in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update tag template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.put('/tag-template/:id', IS_ADMIN, tryCatch(commonController.updateTagTemplate));

  /**
   * @swagger
   * /common/v1/tag-template/:id:
   *   delete:
   *     tags: [Tag Template Management]
   *     summary: Delete tag template
   *     description: Delete tag template in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete tag template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/tag-template/:id:
   *   delete:
   *     tags: [Tag Template Management]
   *     summary: Delete tag template
   *     description: Delete tag template in the common service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete tag template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.delete('/tag-template/:id', IS_ADMIN, tryCatch(commonController.deleteTagTemplate));


  /**
   * @swagger
   * /common/v1/get-location/:id:
   *   get:
   *     tags: [Get Location Management]
   *     summary: Get get location
   *     description: Retrieve get location in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get location successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/get-location/:id:
   *   get:
   *     tags: [Get Location Management]
   *     summary: Get get location
   *     description: Retrieve get location in the common service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get location successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.get('/get-location/:id', IS_ADMIN, tryCatch(commonController.getDeviceLocation));

//mqtt

  /**
   * @swagger
   * /common/v1/publish-to-mqtt:
   *   post:
   *     tags: [Publish To Mqtt Management]
   *     summary: Create publish to mqtt
   *     description: Create publish to mqtt in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create publish to mqtt successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/publish-to-mqtt:
   *   post:
   *     tags: [Publish To Mqtt Management]
   *     summary: Create publish to mqtt
   *     description: Create publish to mqtt in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create publish to mqtt successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
router.post('/publish-to-mqtt', HAS_API_KEY, tryCatch(commonController.publishMqttTerminal));

// merchant-tool-test

  /**
   * @swagger
   * /common/v1/upload-transaction:
   *   post:
   *     tags: [Upload Transaction Management]
   *     summary: Create upload transaction
   *     description: Create upload transaction in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create upload transaction successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/upload-transaction:
   *   post:
   *     tags: [Upload Transaction Management]
   *     summary: Create upload transaction
   *     description: Create upload transaction in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create upload transaction successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
// router.post('/upload-transaction', IS_ADMIN, tryCatch(commonController.saveTransactions));

  /**
   * @swagger
   * /common/v1/upload-settlements:
   *   post:
   *     tags: [Upload Settlements Management]
   *     summary: Create upload settlements
   *     description: Create upload settlements in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create upload settlements successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /common/v1/upload-settlements:
   *   post:
   *     tags: [Upload Settlements Management]
   *     summary: Create upload settlements
   *     description: Create upload settlements in the common service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create upload settlements successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
// router.post('/upload-settlements', IS_ADMIN, tryCatch(commonController.saveSettlement));

module.exports = router;
