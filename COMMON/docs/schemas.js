/**
 * @fileoverview OpenAPI schemas specific to COMMON service
 */

module.exports = {
  Common: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      name: {
        type: 'string',
        example: 'Sample Common',
        description: 'Common name'
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['name']
  },

  CreateCommonRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'New Common',
        description: 'Common name'
      },
      description: {
        type: 'string',
        example: 'Common description',
        description: 'Common description'
      }
    },
    required: ['name']
  },

  UpdateCommonRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Updated Common',
        description: 'Common name'
      },
      description: {
        type: 'string',
        example: 'Updated description',
        description: 'Common description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  CommonListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Common'
            }
          }
        }
      }
    ]
  }
};