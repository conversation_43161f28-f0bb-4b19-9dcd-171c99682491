/**
 * @fileoverview OpenAPI configuration for COMMON service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const commonSchemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for COMMON service
 * @param {Object} app - Express app instance
 */
function setupCommonDocs(app) {
  const config = {
    serviceName: 'COMMON',
    serviceDescription: `
      Common utilities and shared functionality service

      Key features:
      - Entity management
      - Authentication and authorization
      - Data validation and processing
    `,
    version: '1.0.0',
    port: 4005,
    basePath: '/common',
    additionalSchemas: commonSchemas,
    docsPath: '/common/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  setupCommonDocs
};