const { sendSocketNotification, sendDeviceData, getConnectedTerminals } = require('../utils/mqtt-connection');
const {
  DEVICE_LOGS,
  TERMINAL,
  STORE,
  BUSINESS,
  USER,
  CARD_DATA,
  MESSAGE_QUEUE,
  CERTIFICATES,
  TAG_TEMPLATE,
  EMV_TAG,
  TRANSACTION,
  SETTLEMENT,
} = require('../models');
const {
  filterQuery,
  pagination,
  removeDuplicatesById,
  createRedisClient,
  upload_to_s3,
  remove_from_s3,
} = require('../utils/helper');
const { reward_api_secret, reward_api_url } = require('../configs');
const jsonData = require('../utils/EMV_TAGS.json');
const { default: axios } = require('axios');
const { publishToMqtt } = require('../utils/mqtt-connection');
const { encryptCBC, decryptCBC } = require('../utils/cbc');

exports.socketnotification = async (req, res) => {
  sendSocketNotification(req.body);

  return res.status(200).send({
    code: 200,
    success: true,
  });
};

exports.connectedTerminals = async (req, res) => {
  const { room } = req.body;

  if (!room) {
    throw new Error('Payload empty:400');
  }
  const data = await getConnectedTerminals({ room });

  return res.status(200).send({
    code: 200,
    success: true,
    data,
  });
};

exports.deviceData = async (req, res) => {
  sendDeviceData(req.body);

  return res.status(200).send({
    code: 200,
    success: true,
  });
};

exports.deviceLog = async (req, res) => {
  const url = req.body.url.split('/')[1];

  try {
    const allowed_apis = [
      'update-device-configuration',
      'update-passcode',
      'create-clerk-server',
      'delete-user-from-device',
    ];
    if (allowed_apis.includes(url)) {
      await DEVICE_LOGS.create(req.body);
    }
    return res.status(200).json({
      error: false,
      message: 'Log received',
    });
  } catch (ex) {
    return res.status(200).json({
      //200 to not crash the actual device request even if the log fails
      error: true,
      message: ex.message,
    });
  }
};

exports.getDeviceLogs = async (req, res) => {
  try {
    let { page, itemsPerPage, startDate, endDate, searchText, storeId } = filterQuery(req);
    let query = {};

    if (storeId) {
      query = { store_id: storeId };
    }

    if (startDate && endDate) {
      query = {
        created_at: {
          $gte: new Date(startDate),
          $lt: new Date(endDate),
        },
      };
    }
    if (searchText && searchText !== '') {
      const regExp = new RegExp(searchText, 'i');

      query.$or = [{ 'body.user_details': regExp }, { method: regExp }, { url: regExp }, { terminal: regExp }];
    }

    const logs = await DEVICE_LOGS.find(query)
      .sort({ created_at: -1 })
      .skip((page - 1) * itemsPerPage)
      .limit(itemsPerPage);
    const total = await DEVICE_LOGS.countDocuments(query);
    const record = pagination(logs, page, total, itemsPerPage);
    return res.status(200).json({ ...record, code: 200 });
  } catch (ex) {
    return res.status(500).json({
      message: ex.message,
    });
  }
};

exports.allInOneSearch = async (req, res) => {
  const { searchText } = req.query;

  const regExp = new RegExp(searchText, 'i');
  const [terminals, users, stores, businesses] = await Promise.all([
    TERMINAL.find({
      $or: [{ title: regExp }, { device_id: regExp }, { serial_number: regExp }, { tid: regExp }],
    })
      .populate({
        path: 'user_id',
        model: USER,
        populate: {
          path: 'store_id',
          model: STORE,
        },
      })
      .populate({
        path: 'store_id',
        model: STORE,
      })
      .populate({
        path: 'business_id',
        model: BUSINESS,
      }),
    USER.find({ $or: [{ first_name: regExp }, { last_name: regExp }, { email: regExp }] })
      .populate({
        path: 'store_id',
        model: STORE,
      })
      .populate({
        path: 'business_id',
        model: BUSINESS,
      }),
    STORE.find({ $or: [{ title: regExp }, { address: regExp }, { owner: regExp }] }).populate({
      path: 'business_id',
      model: BUSINESS,
    }),
    BUSINESS.find({
      $or: [
        { title: regExp },
        { 'address.formatted_address': regExp },
        { 'owner.first_name': regExp },
        { 'owner.last_name': regExp },
        { mid: regExp },
      ],
    }),
  ]);

  let filteredUsers = [];
  let filteredTerminals = [];
  let filteredStores = [];
  let filteredBusiness = [];

  if (terminals?.length > 0) {
    filteredTerminals = terminals.map(_ => {
      _.user_id.forEach(_ => filteredUsers.push(_));
      filteredStores.push(_.store_id);
      filteredBusiness.push(_.business_id);
      return _;
    });
  }

  if (users?.length > 0) {
    const userIds = users.map(_ => _._id);
    filteredUsers = [
      ...filteredUsers,
      ...users.map(_ => {
        filteredStores.push(_.store_id);
        filteredBusiness.push(_.business_id);
        return _;
      }),
    ];
    filteredStores = filteredStores.filter(_ => _);
    filteredBusiness = filteredBusiness.filter(_ => _);
    filteredTerminals = [
      ...filteredTerminals,
      ...(await TERMINAL.find({ user_id: { $in: userIds } })
        .populate({
          path: 'user_id',
          model: USER,
        })
        .populate({
          path: 'store_id',
          model: STORE,
        })
        .populate({
          path: 'business_id',
          model: BUSINESS,
        })),
    ];
  }

  if (stores?.length > 0) {
    const storeIds = stores.map(_ => _._id);
    filteredStores = [
      ...filteredStores,
      ...stores.map(_ => {
        filteredBusiness.push(_.business_id);
        return _;
      }),
    ];
    filteredTerminals = [
      ...filteredTerminals,
      ...(await TERMINAL.find({ store_id: { $in: storeIds } })
        .populate({
          path: 'user_id',
          model: USER,
        })
        .populate({
          path: 'store_id',
          model: STORE,
        })
        .populate({
          path: 'business_id',
          model: BUSINESS,
        })),
    ];
    filteredUsers = [
      ...filteredUsers,
      ...(await USER.find({ store_id: { $in: storeIds } })
        .populate({
          path: 'store_id',
          model: STORE,
        })
        .populate({
          path: 'business_id',
          model: BUSINESS,
        })),
    ];
  }

  if (businesses?.length > 0) {
    const businessIds = businesses.map(_ => {
      filteredBusiness.push(_);
      return _._id;
    });
    filteredUsers = [
      ...filteredUsers,
      ...(await USER.find({ business_id: { $in: businessIds } })
        .populate({
          path: 'store_id',
          model: STORE,
        })
        .populate({
          path: 'business_id',
          model: BUSINESS,
        })),
    ];
    filteredTerminals = [
      ...filteredTerminals,
      ...(await TERMINAL.find({ business_id: { $in: businessIds } })
        .populate({
          path: 'user_id',
          model: USER,
        })
        .populate({
          path: 'store_id',
          model: STORE,
        })
        .populate({
          path: 'business_id',
          model: BUSINESS,
        })),
    ];
    filteredStores = [
      ...filteredStores,
      ...(await STORE.find({ business_id: { $in: businessIds } }).populate({
        path: 'business_id',
        model: BUSINESS,
      })),
    ];
  }

  return res.status(200).send({
    success: true,
    data: {
      filteredUsers: removeDuplicatesById(filteredUsers),
      filteredTerminals: removeDuplicatesById(filteredTerminals),
      filteredStores: removeDuplicatesById(filteredStores),
      filteredBusiness: removeDuplicatesById(filteredBusiness),
    },
  });
};

exports.saveCardData = async (req, res) => {
  const { id, ...restOfKeys } = req.body;
  let newCardData;

  if (id) {
    const updatedData = { $set: { ...restOfKeys } };
    newCardData = await CARD_DATA.findByIdAndUpdate(id, updatedData, { new: true });
  } else {
    newCardData = await CARD_DATA.create({ ...restOfKeys });
  }

  return res.status(200).json({ code: 200, message: 'Data Saved!', data: newCardData });
};

exports.getCardData = async (req, res) => {
  const { id } = req.params;

  const cardData = await CARD_DATA.findById(id);

  if (!cardData) {
    throw new Error('Data not found:400');
  }

  return res.status(200).json({ code: 200, data: cardData });
};

exports.getAllCardsData = async (req, res) => {
  let { page, itemsPerPage } = filterQuery(req);

  const totalDataRecords = await CARD_DATA.find({}).countDocuments();

  let cardsData = await CARD_DATA.find({})
    .sort([['updated_at', -1]])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .lean();

  const data = pagination(cardsData, page, totalDataRecords, itemsPerPage);

  return res.status(200).json({ code: 200, data });
};

exports.pushDataQueue = async (req, res) => {
  let { serial_number, configuration } = req.body;
  const isPresent = await MESSAGE_QUEUE.findOne({ serial_number });

  if (isPresent) {
    await MESSAGE_QUEUE.findOneAndUpdate({ serial_number }, { $set: { configuration: configuration } });
  } else {
    await MESSAGE_QUEUE.create({ serial_number, configuration });
  }

  sendDeviceData({ serial_number: serial_number, event: 'server_config_update' });

  return res.status(200).json({ code: 200 });
};

exports.pointsNotification = async (req, res) => {
  const { serial_number, approved, event } = req.body;
  if (!serial_number) {
    throw new Error('Serial_number Required:403');
  }

  sendSocketNotification({ serial_number, event: event, data: { approved } });

  console.log('Webhook data:', serial_number, approved);

  res.status(200).json({ message: 'Notification Sent', code: 200 });
};

exports.getPointsdata = async (req, res) => {
  const { userId } = req.params;

  const resp = await axios.get(`${reward_api_url}/points-data/${userId}`, {
    headers: {
      Authorization: `Bearer ${reward_api_secret}`,
      'Content-Type': 'application/json',
    },
  });

  const response = resp.data;

  if (response.success === true) {
    res.status(200).json({ message: response.message, code: 200, data: response.data });
  } else {
    throw new Error(response.message || `Error in points data:${response.status}`);
  }
};

exports.verifyPin = async (req, res) => {
  const { pin } = req.body;

  const resp = await axios.post(
    `${reward_api_url}/verify-pin`,
    { pin: pin },
    {
      headers: {
        Authorization: `Bearer ${reward_api_secret}`,
        'Content-Type': 'application/json',
      },
    },
  );

  const response = resp.data;

  if (response.success === true) {
    res.status(200).json({ message: response.message, code: 200, data: response.data });
  } else {
    throw new Error(response.message || `Error in verify pin:${response.status}`);
  }
};

exports.sendPointRequest = async (req, res) => {
  const { serial_number, points, userId } = req.body;

  const resp = await axios.post(
    `${reward_api_url}/send-point-request`,
    { serial_number, points, userId },
    {
      headers: {
        Authorization: `Bearer ${reward_api_secret}`,
        'Content-Type': 'application/json',
      },
    },
  );

  const response = resp.data;

  if (response.success === true) {
    res.status(200).json({ message: response.message, code: 200, data: response.data });
  } else {
    throw new Error(response.message || `Error in send points request:${response.status}`);
  }
};

exports.completePointRequest = async (req, res) => {
  const { user_id, points } = req.body;

  const resp = await axios.post(
    `${reward_api_url}/complete-point-request`,
    { user_id, points },
    {
      headers: {
        Authorization: `Bearer ${reward_api_secret}`,
        'Content-Type': 'application/json',
      },
    },
  );

  const response = await resp.data;

  if (response.success === true) {
    res.status(200).json({ message: response.message, code: 200, data: response.data });
  } else {
    throw new Error(response.message || `Error in complete points request:${response.status}`);
  }
};

exports.createCertificate = async (req, res) => {
  const { key, cert, ca, passphrase, ip, port } = req.body;

  if (!key && !cert && !ca && !passphrase && !ip && !port) {
    throw new Error('Error in payload:400');
  }
  let version = 1;

  const lastCertificate = await CERTIFICATES.findOne().sort({ created_at: -1 });

  if (lastCertificate) {
    version = lastCertificate.version + 1;
  }

  const createdCertificate = await CERTIFICATES.create({
    ca: encryptCBC(ca),
    key: encryptCBC(key),
    cert: encryptCBC(cert),
    passphrase,
    ip,
    port,
    version,
  });

  if (createdCertificate._id) {
    const currDate = new Date();
    await CERTIFICATES.updateMany(
      { _id: { $ne: createdCertificate._id } },
      { $set: { status: 'Expired', expired: currDate } },
    );

    const stores = await STORE.find({ is_deleted: false });
    await Promise.all(
      stores.map(async store => {
        return sendSocketNotification({
          store_id: store.serial_number,
          event: 'update_certificates',
          data: {
            ca,
            key,
            cert,
            passphrase,
            ip,
            port,
            version,
          },
        });
      }),
    );
  }
  res.status(200).json({ message: 'Certificate Created', code: 200 });
};

exports.getCertificate = async (req, res) => {
  try {
    const { name } = req.params;
    const path = require('path');
    res.sendFile(path.join(__dirname + '../../cert/' + name));
  } catch (ex) {
    res.status(500).json({
      message: ex.message,
    });
  }
};

exports.getAllCertificates = async (req, res) => {
  let { page, itemsPerPage, startDate, endDate, filterStatus, searchText } = filterQuery(req);

  let start = new Date(startDate);
  start.setHours(0, 0, 0, 0);
  let end = new Date(endDate);
  end.setHours(23, 59, 59, 999);
  let query = {
    $and: [],
    $or: [],
  };
  if (startDate && endDate) {
    query?.$and.push({ created_at: { $gte: start, $lt: end } });
  }

  if (searchText && searchText !== '') {
    query.$or = [
      {
        key: { $regex: '.*' + searchText + '.*', $options: 'i' },
      },
    ];
  }
  if (filterStatus && filterStatus !== null) {
    query?.$and.push({ status: filterStatus });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }
  if (!query.$or.length > 0) {
    delete query.$or;
  }

  let totalItems = await CERTIFICATES.countDocuments(query);
  let terminal_info = await TERMINAL.find(
    { is_deleted: false },
    {
      cert_version: 1,
      serial_number: 1,
    },
  ).lean();
  let certificates = await CERTIFICATES.find(query)
    .sort([['created_at', -1]])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .lean();
  if (certificates.length > 0) certificates[0].terminal_info = terminal_info;

  let data = pagination(certificates, page, totalItems, itemsPerPage);

  return res.status(200).json({ ...data, code: 200 });
};

exports.redisClear = async (req, res) => {
  const redis = createRedisClient();
  await redis.flushAll();
  return res.status(200).send('OK');
};

exports.checkRedisConnection = async (req, res) => {
  const redis = createRedisClient();
  const result = await redis.ping();

  if (!result) {
    throw new Error('Not Conneted!:500');
  }
  return res.status(200).send({
    success: true,
    message: result,
  });
};

exports.redisLogs = async (req, res) => {
  const redis = createRedisClient();

  const keys = await redis.KEYS('*');

  const connectedRoomsValues = await redis.MGET(keys);

  const keyValues = keys.reduce((acc, key, index) => {
    acc[key] = connectedRoomsValues[index];
    return acc;
  }, {});

  return res.status(200).json({
    message: 'Keys',
    data: keyValues,
  });
};

exports.removeFromS3 = async (req, res) => {
  const { fileName } = req.body;
  if (fileName.includes('psplogo.png')) {
    return res.status(200).json({
      code: 200,
      message: 'Image Removed',
    });
  }

  const resp = await remove_from_s3(fileName);

  if (resp.DeleteMarker) {
    return res.status(200).json({
      code: 200,
      message: 'Image Removed',
    });
  } else {
    return res.status(404).json({
      code: 404,
      message: 'Image already removed',
    });
  }
};

exports.uploadImageToS3 = async (req, res) => {
  const body = req.file;
  const resp = await upload_to_s3(body);

  return res.status(200).json({
    code: 200,
    message: 'OK',
    secure_url: resp.Location,
    error_message: resp.errorMessage,
  });
};

exports.getCAPKfile = async (req, res) => {
  // const xmlbuilder = require('xmlbuilder');
  const fs = require('fs');
  const path = require('path');
  // const capkListData = [
  //   {
  //     RID: 'A000000003',
  //     KeyID: '08',
  //     HashArithmeticIndex: '1',
  //     RSAArithmeticIndex: '1',
  //     ModuleLength: '176',
  //     Module: 'DD0E30664BD157023EAA1FFA8',
  //     ExponentLength: '1',
  //     Exponent: '03',
  //     ExpiryDate: '241231',
  //     CheckSum: '822BD22DE21CF9A2D2131DE205ADC2FD28',
  //   },
  //   {
  //     RID: 'A000000003',
  //     KeyID: '08',
  //     HashArithmeticIndex: '1',
  //     RSAArithmeticIndex: '1',
  //     ModuleLength: '176',
  //     Module: 'DD0E30664BD157023EAA1FFA8',
  //     ExponentLength: '1',
  //     Exponent: '03',
  //     ExpiryDate: '241231',
  //     CheckSum: '822BD22DE21CF9A2D2131DE205ADC2FD28',
  //   },
  // ];
  // const xml = xmlbuilder.create('EMVPARAM');
  // const capkList = xml.ele('CAPKLIST');
  // capkListData.forEach(capkData => {
  //   const capkElement = capkList.ele('CAPK');
  //   for (const key in capkData) {
  //     capkElement.ele(key).txt(capkData[key]);
  //   }
  // });
  // // End EMVPARAM element
  // xml.end({ pretty: true });
  // res.type('xml').send(xml.toString());
  // fs.writeFileSync('capklist.xml', xml.toString());
  // res.download('capklist.xml', 'capklist.xml', err => {
  //   if (err) {
  //     console.error('Error sending file:', err);
  //     res.status(500).send('Internal Server Error');
  //     fs.unlinkSync('capklist.xml');
  //   } else {
  //     console.log('File sent successfully');
  //     fs.unlinkSync('capklist.xml');
  //   }
  // });

  const directoryPath = path.resolve(__dirname, '..', 'files');

  const filePath = path.join(directoryPath, 'capk.xml');

  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      console.error('Error reading XML file:', err);
      res.status(500).send({ code: 500, success: false, message: err.message || 'Internal Server Error' });
    } else {
      res.status(200).type('xml');
      res.send(data);
    }
  });
};

exports.searchTag = async (req, res) => {
  const searchTerm = req.query.searchText;

  async function filterTagsWithDB(tags) {
    try {
      const dbTags = await EMV_TAG.find({}, 'tag').lean();

      const dbTagsSet = new Set(dbTags.map(dbTag => dbTag.tag));

      const filteredTags = tags.filter(tag => !dbTagsSet.has(tag.tag));

      return filteredTags;
    } catch (error) {
      console.error('Error filtering tags with DB:', error);
      throw error;
    }
  }

  const filteredTags = await filterTagsWithDB(jsonData);

  const matchingObjects = filteredTags.filter(item => {
    const regex = new RegExp(searchTerm, 'i');
    return regex.test(item.tag);
  });

  return res.status(200).json({
    code: 200,
    message: 'Tag created',
    data: matchingObjects,
  });
};
exports.createTag = async (req, res) => {
  const payload = req.body;

  if (!Array.isArray(payload) || payload.length < 1) {
    throw new Error('Bad Request:400');
  }

  for (const ele of payload) {
    await EMV_TAG.create({ tag: ele.tag, tag_name: ele.title });
  }

  return res.status(200).json({
    code: 200,
    success: true,
    message: 'Tags created',
  });
};

exports.updateTag = async (req, res) => {
  const { id } = req.params;
  const payload = req.body;

  await EMV_TAG.findByIdAndUpdate(id, payload);

  const temp = await TAG_TEMPLATE.find({ tags: id }).populate({ path: 'tags', model: EMV_TAG });
  const allTerminals = await TERMINAL.find({ tags: id, is_deleted: false })
    .populate({ path: 'tags', model: EMV_TAG })
    .select('serial_number')
    .lean();

  for (const template of temp) {
    const updatedTagString = template.tags.map(tag => tag.tag).join('');
    await TAG_TEMPLATE.findOneAndUpdate({ _id: template._id }, { $set: { tagString: updatedTagString } });
  }
  for (const terminal of allTerminals) {
    const updatedTagString = terminal.tags.map(tag => tag.tag).join('');
    await TERMINAL.findOneAndUpdate({ _id: terminal._id }, { $set: { tagString: updatedTagString } });
    sendSocketNotification({
      serial_number: terminal?.serial_number,
      event: 'tag_updated',
      data: { tagString: updatedTagString },
    });
  }

  return res.status(200).json({
    code: 200,
    message: 'Tag updated',
  });
};

exports.deleteTag = async (req, res) => {
  const { id } = req.params;

  const isTagExists = await EMV_TAG.findById(id);

  if (!isTagExists) {
    throw new Error('Tag not found:404');
  }

  await EMV_TAG.findByIdAndDelete(id);

  const temp = await TAG_TEMPLATE.find({ tags: id }).populate({ path: 'tags', model: EMV_TAG });
  const allTerminals = await TERMINAL.find({ tags: id, is_deleted: false })
    .populate({ path: 'tags', model: EMV_TAG })
    .select('serial_number')
    .lean();

  for (const template of temp) {
    const updatedTags = template.tags.filter(tag => id !== tag._id.toString());
    const updatedTagString = updatedTags.map(tag => tag.tag).join('');

    await TAG_TEMPLATE.findOneAndUpdate(
      { _id: template._id },
      { $set: { tags: updatedTags, tagString: updatedTagString } },
    );
  }

  for (const terminal of allTerminals) {
    const updatedTags = terminal.tags.filter(tag => id !== tag._id.toString());
    const updatedTagString = updatedTags.map(tag => tag.tag).join('');
    await TERMINAL.findOneAndUpdate(
      { _id: terminal._id },
      { $set: { tags: updatedTags, tagString: updatedTagString } },
    );
    sendSocketNotification({
      serial_number: terminal?.serial_number,
      event: 'tag_updated',
      data: { tagString: updatedTagString },
    });
  }

  return res.status(200).json({
    code: 200,
    success: true,
    message: 'Tag Deleted',
  });
};

exports.getAllTags = async (req, res) => {
  let { page, itemsPerPage, searchText } = filterQuery(req);
  let query = {};

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [{ tag: regExp }, { tag_name: regExp }];
  }

  const tags = await EMV_TAG.find(query)
    .sort({ created_at: -1 })
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage);
  const total = await EMV_TAG.countDocuments(query);

  const record = pagination(tags, page, total, itemsPerPage);

  return res.status(200).json({
    code: 200,
    message: 'Tag created',
    data: record,
  });
};

exports.createTagTemplate = async (req, res) => {
  const { tags, title } = req.body;

  const isTagTemplateExists = await TAG_TEMPLATE.findOne({ title });

  if (isTagTemplateExists) {
    throw new Error('Title is already used:409');
  }

  const tag = await EMV_TAG.find({ _id: { $in: tags } });

  if (tag.length < 1) {
    throw new Error('Data not Found:404');
  }

  const stringOfTags = tag
    .map(_ => _.tag)
    .filter(_ => _)
    .join('');

  await TAG_TEMPLATE.create({ title: title.trim(), tags: tags, tagString: stringOfTags });

  return res.status(200).json({
    code: 200,
    message: 'Tag created',
  });
};

exports.getAllTagTemplates = async (req, res) => {
  let { page, itemsPerPage, searchText } = filterQuery(req);
  let query = {};

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [{ title: regExp }, { tagString: regExp }];
  }

  const tagTemplates = await TAG_TEMPLATE.find(query)
    .populate({ path: 'tags', model: EMV_TAG })
    .sort({ created_at: -1 })
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage);

  const total = await TAG_TEMPLATE.countDocuments(query);

  const record = pagination(tagTemplates, page, total, itemsPerPage);

  return res.status(200).json({
    code: 200,
    message: 'Tag created',
    ...record,
  });
};

exports.updateTagTemplate = async (req, res) => {
  const { id } = req.params;
  const { tags, title } = req.body;

  const isTagTemplateExists = await TAG_TEMPLATE.findById(id);

  if (!isTagTemplateExists) {
    throw new Error('Template not found:404');
  }

  const tag = await EMV_TAG.find({ _id: { $in: tags } });

  if (tag.length < 1) {
    throw new Error('Data not Found:404');
  }

  const stringOfTags = tag
    .map(_ => _.tag)
    .filter(_ => _)
    .join('');

  await TAG_TEMPLATE.findByIdAndUpdate(id, { title: title.trim(), tags: tags, tagString: stringOfTags }).catch(
    error => {
      if (error) {
        throw new Error(
          error?.codeName === 'DuplicateKey' ? 'Tag Template Already Exists with this title' : error?.message,
        );
      }
    },
  );
  const allTerminals = await TERMINAL.find({ tag_template: id, tagStringFormat: 'default', is_deleted: false })
    .select('serial_number')
    .lean();
  for (const terminal of allTerminals) {
    await TERMINAL.findOneAndUpdate({ _id: terminal._id }, { $set: { tagString: stringOfTags, tags: tags } });
    sendSocketNotification({
      serial_number: terminal?.serial_number,
      event: 'tag_updated',
      data: { tagString: stringOfTags },
    });
  }

  return res.status(200).json({
    code: 200,
    success: true,
    message: 'Tag Template Updated',
  });
};

exports.deleteTagTemplate = async (req, res) => {
  const { id } = req.params;

  const isTagTemplateExists = await TAG_TEMPLATE.findById(id);

  if (!isTagTemplateExists) {
    throw new Error('Template not found:404');
  }

  await TAG_TEMPLATE.findByIdAndDelete(id);

  await STORE.updateMany({ tag_template: id }, { $unset: { tag_template: 1 } });

  const allTerminals = await TERMINAL.find({ tag_template: id, tagStringFormat: 'default', is_deleted: false })
    .select('serial_number')
    .lean();
  for (const terminal of allTerminals) {
    await TERMINAL.findOneAndUpdate(
      { _id: terminal._id },
      {
        $set: {
          tagString: '',
          tags: [],
          tagStringFormat: 'default',
        },
        $unset: {
          tag_template: 1,
        },
      },
    );
    sendSocketNotification({
      serial_number: terminal?.serial_number,
      event: 'tag_updated',
      data: { tagString: '' },
    });
  }

  return res.status(200).json({
    code: 200,
    success: true,
    message: 'Tag Template Deleted',
  });
};

exports.getDeviceLocation = async (req, res) => {
  const { id } = req.params;
  const device = await TERMINAL.findById(id);
  if (!device) {
    throw new Error('Error while requesting location please try after sometime:400');
  }
  sendSocketNotification({ serial_number: device.serial_number, event: 'update_location' });

  return res.status(200).json({
    code: 200,
    success: true,
    message: 'Requested For location try after sometime',
  });
};

exports.publishMqttTerminal = async (req, res) => {
  const { topic, message } = req.body;
  await publishToMqtt(topic, message);
  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Message Published',
  });
};

// exports.saveTransactions = async (req, res) => {
//   try {
//     const { transactions } = req.body;

//     // let records = transactions?.map(transaction => ({
//     //   updateOne: {
//     //     filter: { transactionID: transaction.transactionID },
//     //     update: { $set: transaction },
//     //     upsert: true,
//     //   },
//     // }));
//     const updateTransactionRecords = async () => {
//       // Map transactions to an array of update operations wrapped in Promises
//       const promises = transactions?.map(async transaction => {
//         return {
//           updateOne: {
//             filter: { transactionID: transaction.transactionID },
//             update: { $set: transaction },
//             upsert: true,
//           },
//         };
//       });

//       // Use Promise.all to wait for all promises to resolve
//       const updateOperations = await Promise.all(promises || []);

//       return updateOperations;
//     };
//     console.time('saveTransaction');
//     updateTransactionRecords()
//       .then(async updateOperations => {
//         if (updateOperations.length > 0) {
//           console.time('transactionQuery');
//           await TRANSACTION.bulkWrite(updateOperations).then(res => {
//             console.log(res.insertedCount, res.modifiedCount, res.deletedCount);
//             console.timeEnd('transactionQuery');
//           });
//         }
//         console.log(updateOperations.length);
//         console.timeEnd('saveTransaction');
//         // Here you can proceed with further processing
//       })
//       .catch(error => {
//         console.error('Error updating transaction records:', error);
//         return res.status(500).json({ message: error });
//       });

//     // if (records.length > 0) {
//     //   await TRANSACTION.bulkWrite(records);
//     // }

//     return res.status(200).json({ message: 'success' });
//   } catch (ex) {
//     return res.status(500).json({ message: ex.message });
//   }
// };
// exports.saveSettlement = async (req, res) => {
//   try {
//     let { settlements } = req.body;

//     let records = settlements?.map(settlement => ({
//       updateOne: {
//         filter: {
//           merchantNumber: settlement.merchantNumber,
//           settlementDate: settlement.settlementDate.split('T')[0].toString(),
//         },
//         update: { $set: settlement },
//         upsert: true,
//       },
//     }));

//     if (records.length > 0) {
//       await SETTLEMENT.bulkWrite(records);
//     }

//     return res.status(200).json({ message: 'Success' });
//   } catch (ex) {
//     return res.status(500).json({ message: ex.message });
//   }
// };
