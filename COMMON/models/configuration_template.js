const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const configurationTemplate = new Schema(
  {
    title: {
      type: String,
      required: true,
      unique: true,
    },
    configuration: {
      type: Object,
      required: true,
    },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

configurationTemplate.plugin(uniqueValidator);

const CONFIGURATION_TEMPLATE = model('configuration_template', configurationTemplate);
module.exports = CONFIGURATION_TEMPLATE;
