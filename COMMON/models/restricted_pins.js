const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const restricted_pins = new Schema(
  {
    store_id: { type: String, required: true },
    pins: [Object],
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

restricted_pins.plugin(uniqueValidator);

const RESTRICTED_PINS = model('restricted_pins', restricted_pins);

module.exports = RESTRICTED_PINS;
