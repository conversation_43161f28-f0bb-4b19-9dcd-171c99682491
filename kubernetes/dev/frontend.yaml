apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: eftjob
  name: deployment-2048
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: app-2048
  replicas: 5
  template:
    metadata:
      labels:
        app.kubernetes.io/name: app-2048
    spec:
      containers:
      - image: 241889469729.dkr.ecr.ap-south-1.amazonaws.com/frontend-psp-pos:COMMIT_ID
        imagePullPolicy: Always
        name: app-2048
        ports:
        - containerPort: 80
        - containerPort: 33666
---
apiVersion: v1
kind: Service
metadata:
  namespace: eftjob
  name: service-2048
spec:
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
    - port: 33666
      targetPort: 33666
      protocol: TCP
  type: NodePort
  selector:
    app.kubernetes.io/name: app-2048
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: eftjob
  name: ingress-2048
  annotations:
    nginx.org/tcp-services: "33666:service-2048"
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
spec:
  rules:
    - http:
        paths:
          - path: /admin
            pathType: Prefix
            backend:
              service:
                name: admin-service
                port:
                  number: 4000
          - path: /users
            pathType: Prefix
            backend:
              service:
                name: users-service
                port:
                  number: 4004
          - path: /business
            pathType: Prefix
            backend:
              service:
                name: business-service
                port:
                  number: 4003
          - path: /common
            pathType: Prefix
            backend:
              service:
                name: common-service
                port:
                  number: 4005
          - path: /config
            pathType: Prefix
            backend:
              service:
                name: config-service
                port:
                  number: 4006
          - path: /store
            pathType: Prefix
            backend:
              service:
                name: store-service
                port:
                  number: 4002
          - path: /terminal
            pathType: Prefix
            backend:
              service:
                name: terminal-service
                port:
                  number: 4001
          - path: /websockets
            pathType: Prefix
            backend:
              service:
                name: common-service
                port:
                  number: 4005
          - path: /ipos
            pathType: Prefix
            backend:
              service:
                name: ipos-service
                port:
                  number: 4007
          - path: /*
            pathType: Prefix
            backend:
              service:
                name: service-2048
                port:
                  number: 80
