apiVersion: apps/v1
kind: Deployment
metadata:
  name: users-deployment
  namespace: eftjob
spec:
  replicas: 1
  selector:
    matchLabels:
      app: users
  template:
    metadata:
      labels:
        app: users
    spec:
      containers:
        - name: users
          image: 241889469729.dkr.ecr.ap-south-1.amazonaws.com/psp-pos-users:COMMIT_ID
          ports:
            - containerPort: 4004
          env:
            - name: NODE_ENV
              value: "staging"
            - name: WINDOW
              value: "30"
            - name: MAX_LIMIT
              value: "10"
            - name: PORT
              value: "4004"
            - name: SECRET
              value: "secret"
            - name: MONGO_URI
              value: "*****************************************************************"
            - name: ACCESS_KEY
              value: "qwertyuiop"
            - name: TWILLO_ACCOUNT_SSID
              valueFrom:
                secretKeyRef:
                  name: twillossid
                  key: TWILLO_ACCOUNT_SSID
            - name: TWILLO_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: twilloauth
                  key: TWILLO_AUTH_TOKEN
            - name: TWILLO_NUMBER
              value: "+***********"
            - name: SENDGRID_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sendgridapi
                  key: SENDGRID_API_KEY
            - name: SENDGRID_USER_NAME
              value: "apikey"
            - name: SENDGRID_HOST
              value: "smtp.sendgrid.net"
            - name: SENDGRID_PORT
              value: "587"
            - name: SENDGRID_FROM_EMAIL
              value: "<EMAIL>"
            - name: WEBSOCKET_PATH
              value: "/websockets"
            - name: MAIL_HOST
              value: "localhost"
            - name: MAIL_PORT
              value: "1025"
            - name: MAIL_USERNAME
              value: "<EMAIL>"
            - name: MAIL_PASSWORD
              value: "123456"
            - name: MAIL_FROM_ADDRESS
              value: "<EMAIL>"
            - name: MAIL_FROM_NAME
              value: "PSP-POS Team"
            - name: BASE_URL
              value: "https://tms-dev.pspservicesco.com"
---
apiVersion: v1
kind: Service
metadata:
  name: users-service
  namespace: eftjob
spec:
  selector:
    app: users
  ports:
    - protocol: TCP
      port: 4004
      targetPort: 4004