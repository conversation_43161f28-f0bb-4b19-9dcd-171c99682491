apiVersion: apps/v1
kind: Deployment
metadata:
  name: common-deployment
  namespace: tms-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: common
  template:
    metadata:
      labels:
        app: common
    spec:
      containers:
        - name: common
          image: 275568445452.dkr.ecr.ca-central-1.amazonaws.com/psp-pos-psp-common:COMMIT_ID
          ports:
            - containerPort: 4005
          env:
            - name: NODE_ENV
              value: 'staging'
            - name: WINDOW
              value: '30'
            - name: MAX_LIMIT
              value: '10'
            - name: PORT
              value: '4005'
            - name: SECRET
              value: 'secret'
            - name: MONGO_URI
              valueFrom:
                secretKeyRef:
                  name: mongo-uri-secret
                  key: MONGO_URI
            - name: ENCRYPTION_KEY
              value: 'dc35bab0996328ecfae9666bc537409bda9b112ec7a2e644abd671e692ec644c'
            - name: ENC_IV
              value: '1354e5507b839e14eb89ab45c7601a0d'
            - name: ACCESS_KEY
              value: 'qwertyuiop'
            - name: TWILLO_ACCOUNT_SSID
              valueFrom:
                secretKeyRef:
                  key: TWILLO_ACCOUNT_SSID
                  name: twillossid
            - name: TWILLO_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
	              key: TWILLO_AUTH_TOKEN
		          name: twilloauth  
            - name: TWILLO_NUMBER
              valueFrom:
                secretKeyRef:
	              key: TWILLO_NUMBER
		          name: twillonumber
            - name: SENDGRID_API_KEY
              value: ''
            - name: SENDGRID_USER_NAME
              value: ''
            - name: SENDGRID_HOST
              value: 'smtp.sendgrid.net'
            - name: SENDGRID_PORT
              value: '587'
            - name: SENDGRID_FROM_EMAIL
              value: '<EMAIL>'
            - name: WEBSOCKET_PATH
              value: '/websockets'
            - name: REDIS_URL
              value: 'redis://*********:6379'
            - name: AWS_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-access-key
                  key: AWS_ACCESS_KEY
            - name: AWS_SECRET_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-secret-key-id
                  key: AWS_SECRET_KEY_ID
            - name: AWS_REGION
              valueFrom:
                secretKeyRef:
                  name: aws-region
                  key: AWS_REGION
            - name: AWS_BUCKET_NAME
              valueFrom:
                secretKeyRef:
                  name: aws-bucket-name
                  key: AWS_BUCKET_NAME
            - name: AWS_BUCKET_NAME_CERT
              value: 'tms-dev-certs'
            - name: MQTT_URL
              value: rabbitmq-dev.pspservicesco.com
            - name: MQTT_PORT
              value: '8883'
            - name: MQTT_CA
              value: './utils/certs-dev/certs/ca_cert.pem'
            - name: MQTT_CERT
              value: './utils/certs-dev/certs/client_cert.pem'
            - name: MQTT_KEY
              value: './utils/certs-dev/certs/client_key.pem'
            - name: MERCHANT_TOOL_URL
              value: 'https://merchanttool-dev.pspservicesco.com'
            - name: MERCHANT_TOOL_API_KEY
              value: 'EuC/gBCfS#0txdB$AP2;u<#I)1/-r7R`d2H{2M>i-DF4+M'

---
apiVersion: v1
kind: Service
metadata:
  name: common-service
  namespace: tms-dev
spec:
  selector:
    app: common
  ports:
    - protocol: TCP
      port: 4005
      targetPort: 4005
