apiVersion: apps/v1
kind: Deployment
metadata:
  name: amex-deployment
  namespace: eftjob
spec:
  replicas: 1
  selector:
    matchLabels:
      app: amex
  template:
    metadata:
      labels:
        app: amex
    spec:
      containers:
        - name: amex
          image: 241889469729.dkr.ecr.ap-south-1.amazonaws.com/psp-pos-amex:COMMIT_ID
          ports:
            - containerPort: 4008
          env:
            - name: NODE_ENV
              value: "staging"
            - name: WINDOW
              value: "30"
            - name: MAX_LIMIT
              value: "10"
            - name: PORT
              value: "4008"
            - name: SECRET
              value: "secret"
            - name: MONGO_URI
              value: "mongodb://root:rootadmin1!2!@*************:27017/amex?authSource=admin"
            - name: ACCESS_KEY
              value: "qwertyuiop"
            - name: TWILLO_ACCOUNT_SSID
              value: ""
            - name: TWILLO_AUTH_TOKEN
              value: ""
            - name: TWILLO_NUMBER
              value: ""
            - name: SENDGRID_API_KEY
              value: "apikey"
            - name: SENDGRID_USER_NAME
              value: ""
            - name: SENDGRID_HOST
              value: "smtp.sendgrid.net"
            - name: SENDGRID_PORT
              value: "587"
            - name: SENDGRID_FROM_EMAIL
              value: "<EMAIL>"
            - name: WEBSOCKET_PATH
              value: "/websockets"
            - name: MAIL_HOST
              value: "localhost"
            - name: MAIL_PORT
              value: "1025"
            - name: MAIL_USERNAME
              value: "<EMAIL>"
            - name: MAIL_PASSWORD
              value: "123456"
            - name: MAIL_FROM_ADDRESS
              value: "<EMAIL>"
            - name: MAIL_FROM_NAME
              value: "PSP-POS Team"
            - name: FIRST_ADMIN_EMAIL
              value: "<EMAIL>"
            - name: FISRT_ADMIN_PASSWORD
              value: "Test%4i90"
            - name: KEY
              value: "PSiUpload-048j54323k53qpmz5vmfdv"
            - name: SSL_CERT_PATH
              value: "./certificate/amex-dev.pspservicesco.com_fullchain.cer"
            - name: SSL_KEY_PATH
              value: "./certificate/amex-dev.pspservicesco.com_private_key.key"
            - name: AMEX_URL
              value: "https://apigateway2sma-qa.americanexpress.com"
            - name: AMEX_CLIENT_ID
              value: "ywP8XmTg5l4w6wWRWOingFLqQSEGd9Pv"
            - name: AMEX_CLIENT_SECRET
              value: "7PwNKkvHoz4PL9kl6rxueMfZS01r9UsK"
            - name: AMEX_HOST
              value: "https://apigateway2sma-qa.americanexpress.com"
            - name: AMEX_END_POINT
              value: ="https://apigateway2sma-qa.americanexpress.com"
            - name: "MONDO_DB_NAME"
              value: amex
            - name: MONGO_DB_COLLECTION
              value: "merchantDetails"
            - name: PARTICIPANT_SE
              value: "SC8/aPGgFZan0obN8mnvG4ycT7MhDh3iZLCiBMJj+48="
---
apiVersion: v1
kind: Service
metadata:
  name: amex-service
  namespace: eftjob
spec:
  selector:
    app: amex
  ports:
    - protocol: TCP
      port: 4008
      targetPort: 4008