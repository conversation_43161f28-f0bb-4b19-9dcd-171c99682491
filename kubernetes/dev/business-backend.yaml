apiVersion: apps/v1
kind: Deployment
metadata:
  name: business-deployment
  namespace: eftjob
spec:
  replicas: 1
  selector:
    matchLabels:
      app: business
  template:
    metadata:
      labels:
        app: business
    spec:
      containers:
        - name: business
          image: 241889469729.dkr.ecr.ap-south-1.amazonaws.com/psp-pos-psp-business:COMMIT_ID
          ports:
            - containerPort: 4003
          env:
            - name: NODE_ENV
              value: 'staging'
            - name: WINDOW
              value: '30'
            - name: MAX_LIMIT
              value: '10'
            - name: PORT
              value: '4003'
            - name: SECRET
              value: 'secret'
            - name: MONGO_URI
              value: '*****************************************************************'
            - name: ACCESS_KEY
              value: 'qwertyuiop'
            - name: TWILLO_ACCOUNT_SSID
              value: ''
            - name: TWILLO_AUTH_TOKEN
              value: ''
            - name: TWILLO_NUMBER
              value: ''
            - name: SENDGRID_API_KEY
              value: ''
            - name: SENDGRID_USER_NAME
              value: ''
            - name: SENDGRID_HOST
              value: 'smtp.sendgrid.net'
            - name: SENDGRID_PORT
              value: '587'
            - name: SENDGRID_FROM_EMAIL
              value: '<EMAIL>'
            - name: WEBSOCKET_PATH
              value: '/websockets'
            - name: MAIL_HOST
              value: 'localhost'
            - name: MAIL_PORT
              value: '1025'
            - name: MAIL_USERNAME
              value: '<EMAIL>'
            - name: MAIL_PASSWORD
              value: '123456'
            - name: MAIL_FROM_ADDRESS
              value: '<EMAIL>'
            - name: MAIL_FROM_NAME
              value: 'PSP-POS Team'
            - name: REWARD_API_KEY
              value: '2y8n30y/LofhJKobjH4bTjgtfvqKckCBrGQQ1HvjZW4='
            - name: BASE_URL
              value: 'https://tms-dev.pspservicesco.com'
            - name: MERCHANT_TOOL_URL
              value: 'https://merchanttool-dev.pspservicesco.com'
            - name: MERCHANT_TOOL_API_KEY
              value: 'EuC/gBCfS#0txdB$AP2;u<#I)1/-r7R`d2H{2M>i-DF4+M'
---
apiVersion: v1
kind: Service
metadata:
  name: business-service
  namespace: eftjob
spec:
  selector:
    app: business
  ports:
    - protocol: TCP
      port: 4003
      targetPort: 4003
