apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-deployment
  namespace: eftjob
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin
  template:
    metadata:
      labels:
        app: admin
    spec:
      containers:
        - name: admin
          image: 241889469729.dkr.ecr.ap-south-1.amazonaws.com/psp-pos-admin:COMMIT_ID
          ports:
            - containerPort: 4000
          env:
            - name: NODE_ENV
              value: "staging"
            - name: WINDOW
              value: "30"
            - name: MAX_LIMIT
              value: "10"
            - name: PORT
              value: "4000"
            - name: SECRET
              value: "secret"
            - name: MONGO_URI
              value: "*****************************************************************"
            - name: ACCESS_KEY
              value: "qwertyuiop"
            - name: TWILLO_ACCOUNT_SSID
              value: ""
            - name: TWILLO_AUTH_TOKEN
              value: ""
            - name: TWILLO_NUMBER
              value: ""
            - name: SENDGRID_API_KEY
              value: "apikey"
            - name: SENDGRID_USER_NAME
              value: ""
            - name: SENDGRID_HOST
              value: "smtp.sendgrid.net"
            - name: SENDGRID_PORT
              value: "587"
            - name: SENDGRID_FROM_EMAIL
              value: "<EMAIL>"
            - name: WEBSOCKET_PATH
              value: "/websockets"
            - name: MAIL_HOST
              value: "localhost"
            - name: MAIL_PORT
              value: "1025"
            - name: MAIL_USERNAME
              value: "<EMAIL>"
            - name: MAIL_PASSWORD
              value: "123456"
            - name: MAIL_FROM_ADDRESS
              value: "<EMAIL>"
            - name: MAIL_FROM_NAME
              value: "PSP-POS Team"
            - name: FIRST_ADMIN_EMAIL
              value: "<EMAIL>"
            - name: FISRT_ADMIN_PASSWORD
              value: "Test%4i90"
---
apiVersion: v1
kind: Service
metadata:
  name: admin-service
  namespace: eftjob
spec:
  selector:
    app: admin
  ports:
    - protocol: TCP
      port: 4000
      targetPort: 4000