apiVersion: apps/v1
kind: Deployment
metadata:
  name: config-deployment
  namespace: eftjob
spec:
  replicas: 1
  selector:
    matchLabels:
      app: config
  template:
    metadata:
      labels:
        app: config
    spec:
      containers:
        - name: config
          image: 241889469729.dkr.ecr.ap-south-1.amazonaws.com/psp-pos-psp-config:COMMIT_ID
          ports:
            - containerPort: 4006
          env:
            - name: NODE_ENV
              value: 'staging'
            - name: WINDOW
              value: '30'
            - name: MAX_LIMIT
              value: '10'
            - name: PORT
              value: '4006'
            - name: SECRET
              value: 'secret'
            - name: MONGO_URI
              value: '*****************************************************************'
            - name: ACCESS_KEY
              value: 'qwertyuiop'
            - name: TWILLO_ACCOUNT_SSID
              value: ''
            - name: TWILLO_AUTH_TOKEN
              value: ''
            - name: TWILLO_NUMBER
              value: ''
            - name: SENDGRID_API_KEY
              value: ''
            - name: SENDGRID_USER_NAME
              value: ''
            - name: SENDGRID_HOST
              value: 'smtp.sendgrid.net'
            - name: SENDGRID_PORT
              value: '587'
            - name: SENDGRID_FROM_EMAIL
              value: '<EMAIL>'
            - name: WEBSOCKET_PATH
              value: '/websockets'
            - name: MAIL_HOST
              value: 'localhost'
            - name: MAIL_PORT
              value: '1025'
            - name: MAIL_USERNAME
              value: '<EMAIL>'
            - name: ENCRYPTION_KEY
              value: 'dc35bab0996328ecfae9666bc537409bda9b112ec7a2e644abd671e692ec644c'
            - name: ENC_IV
              value: '1354e5507b839e14eb89ab45c7601a0d'
            - name: MAIL_PASSWORD
              value: '123456'
            - name: MAIL_FROM_ADDRESS
              value: '<EMAIL>'
            - name: MAIL_FROM_NAME
              value: 'PSP-POS Team'
            - name: BASE_URL
              value: 'https://tms-dev.pspservicesco.com'
            - name: REDIS_URL
              value: 'redis://ec2-13-201-48-23.ap-south-1.compute.amazonaws.com:6379'
---
apiVersion: v1
kind: Service
metadata:
  name: config-service
  namespace: eftjob
spec:
  selector:
    app: config
  ports:
    - protocol: TCP
      port: 4006
      targetPort: 4006
