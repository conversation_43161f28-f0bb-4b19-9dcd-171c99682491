apiVersion: apps/v1
kind: Deployment
metadata:
  name: terminal-deployment
  namespace: tms-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: terminal
  template:
    metadata:
      labels:
        app: terminal
    spec:
      containers:
        - name: terminal
          image: 275568445452.dkr.ecr.ca-central-1.amazonaws.com/psp-pos-terminal:COMMIT_ID
          ports:
            - containerPort: 4001
          env:
            - name: NODE_ENV
              value: 'staging'
            - name: WINDOW
              value: '30'
            - name: MAX_LIMIT
              value: '10'
            - name: PORT
              value: '4001'
            - name: SECRET
              value: 'secret'
            - name: MONGO_URI
              valueFrom:
                secretKeyRef:
                  name: mongo-uri-secret
                  key: MONGO_URI
            - name: ACCESS_KEY
              value: 'qwertyuiop'
            - name: ENCRYPTION_KEY
              value: 'dc35bab0996328ecfae9666bc537409bda9b112ec7a2e644abd671e692ec644c'
            - name: ENC_IV
              value: '1354e5507b839e14eb89ab45c7601a0d'
            - name: TWILLO_ACCOUNT_SSID
              valueFrom:
                secretKeyRef:
                  name: twillossid
                  key: TWILLO_ACCOUNT_SSID
            - name: TWILLO_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: twilloauth
                  key: TWILLO_AUTH_TOKEN
            - name: TWILLO_NUMBER
              value: '+***********'
            - name: SENDGRID_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sendgridapi
                  key: SENDGRID_API_KEY
            - name: SENDGRID_USER_NAME
              value: 'apikey'
            - name: SENDGRID_HOST
              value: 'smtp.sendgrid.net'
            - name: SENDGRID_PORT
              value: '587'
            - name: SENDGRID_FROM_EMAIL
              value: '<EMAIL>'
            - name: WEBSOCKET_PATH
              value: '/websockets'
            - name: BASE_URL
              value: 'https://tms-dev.pspservicesco.com'
            - name: MERCHANT_TOOL_URL
              value: 'https://merchanttool-dev.pspservicesco.com'
            - name: MERCHANT_TOOL_API_KEY
              value: 'EuC/gBCfS#0txdB$AP2;u<#I)1/-r7R`d2H{2M>i-DF4+M'
            - name: REWARDS_BACKEND_URL
              value: 'http://k8s-rewards-rewardsi-2c6146c085-83233346.ca-central-1.elb.amazonaws.com'

---
apiVersion: v1
kind: Service
metadata:
  name: terminal-service
  namespace: tms-dev
spec:
  selector:
    app: terminal
  ports:
    - protocol: TCP
      port: 4001
      targetPort: 4001
