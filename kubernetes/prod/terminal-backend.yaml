apiVersion: apps/v1
kind: Deployment
metadata:
  name: terminal-deployment
  namespace: tms-application
spec:
  replicas: 3
  selector:
    matchLabels:
      app: terminal
  template:
    metadata:
      labels:
        app: terminal
    spec:
      containers:
        - name: terminal
          image: 025066247888.dkr.ecr.ca-central-1.amazonaws.com/tms_terminal_backend:COMMIT_ID
          ports:
            - containerPort: 4001
          resources:
            requests:
              memory: '2Gi'
              cpu: '2000m'
            limits:
              memory: '4Gi'
              cpu: '4000m'
          env:
            - name: NODE_ENV
              value: 'production'
            - name: WINDOW
              value: '30'
            - name: MAX_LIMIT
              value: '10'
            - name: PORT
              value: '4001'
            - name: ENCRYPTION_KEY
              value: 'dc35bab0996328ecfae9666bc537409bda9b112ec7a2e644abd671e692ec644c'
            - name: ENC_IV
              value: '1354e5507b839e14eb89ab45c7601a0d'
            - name: SECRET
              value: 'secret'
            - name: MONGO_URI
              valueFrom:
                secretKeyRef:
                  name: updated-mongo-uri-secret
                  key: MONGO_URI
            - name: ACCESS_KEY
              value: 'qwertyuiop'
            - name: TWILLO_ACCOUNT_SSID
              valueFrom:
                secretKeyRef:
                  name: twillossid
                  key: TWILLO_ACCOUNT_SSID
            - name: TWILLO_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: twilloauth
                  key: TWILLO_AUTH_TOKEN
            - name: TWILLO_NUMBER
              value: '+***********'
            - name: SENDGRID_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sendgridapi
                  key: SENDGRID_API_KEY
            - name: SENDGRID_USER_NAME
              value: 'apikey'
            - name: SENDGRID_HOST
              value: 'smtp.sendgrid.net'
            - name: SENDGRID_PORT
              value: '587'
            - name: SENDGRID_FROM_EMAIL
              value: '<EMAIL>'
            - name: WEBSOCKET_PATH
              value: '/websockets'
            - name: MAIL_HOST
              value: 'localhost'
            - name: MAIL_PORT
              value: '1025'
            - name: MAIL_USERNAME
              value: '<EMAIL>'
            - name: MAIL_PASSWORD
              value: '123456'
            - name: MAIL_FROM_ADDRESS
              value: '<EMAIL>'
            - name: MAIL_FROM_NAME
              value: 'PSP-POS Team'
            - name: BASE_URL
              value: 'https://tms.pspservicesco.com'
            - name: MQTT_URL
              value: rabbitmq.pspservicesco.com
            - name: MQTT_PORT
              value: '8883'
            - name: MQTT_CA
              value: './utils/certs-dev/certs/ca_cert.pem'
            - name: MQTT_CERT
              value: './utils/certs-dev/certs/client_cert.pem'
            - name: MQTT_KEY
              value: './utils/certs-dev/certs/client_key.pem'
            - name: MERCHANT_TOOL_URL
              value: 'https://merchanttool.pspservicesco.com'
            - name: MERCHANT_TOOL_API_KEY
              value: 'EuC/gBCfS#0txdB$AP2;u<#I)1/-r7R`d2H{2M>i-DF4+M'
---
apiVersion: v1
kind: Service
metadata:
  name: terminal-service
  namespace: tms-application
spec:
  selector:
    app: terminal
  ports:
    - protocol: TCP
      port: 4001
      targetPort: 4001
