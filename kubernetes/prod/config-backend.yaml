apiVersion: apps/v1
kind: Deployment
metadata:
  name: config-deployment
  namespace: tms-application
spec:
  replicas: 2
  selector:
    matchLabels:
      app: config
  template:
    metadata:
      labels:
        app: config
    spec:
      containers:
        - name: config
          image: 025066247888.dkr.ecr.ca-central-1.amazonaws.com/tms_config_backend:COMMIT_ID
          ports:
            - containerPort: 4006
          env:
            - name: NODE_ENV
              value: 'production'
            - name: WINDOW
              value: '30'
            - name: MAX_LIMIT
              value: '10'
            - name: PORT
              value: '4006'
            - name: SECRET
              value: 'secret'
            - name: MONGO_URI
              valueFrom:
                secretKeyRef:
                  name: updated-mongo-uri-secret
                  key: MONGO_URI
            - name: ACCESS_KEY
              value: 'qwertyuiop'
            - name: TWILLO_ACCOUNT_SSID
              value: ''
            - name: TWILLO_AUTH_TOKEN
              value: ''
            - name: TWILLO_NUMBER
              value: ''
            - name: SENDGRID_API_KEY
              value: ''
            - name: SENDGRID_USER_NAME
              value: ''
            - name: SENDGRID_HOST
              value: 'smtp.sendgrid.net'
            - name: SENDGRID_PORT
              value: '587'
            - name: SENDGRID_FROM_EMAIL
              value: '<EMAIL>'
            - name: WEBSOCKET_PATH
              value: '/websockets'
            - name: MAIL_HOST
              value: 'localhost'
            - name: MAIL_PORT
              value: '1025'
            - name: MAIL_USERNAME
              value: '<EMAIL>'
            - name: MAIL_PASSWORD
              value: '123456'
            - name: MAIL_FROM_ADDRESS
              value: '<EMAIL>'
            - name: MAIL_FROM_NAME
              value: 'PSP-POS Team'
            - name: REDIS_URL
              # value: 'redis://ec2-13-201-48-23.ap-south-1.compute.amazonaws.com:6379'
              value: 'redis://tms-prod-prod-redis-cluster.gvddv0.0001.cac1.cache.amazonaws.com:6379'
            - name: BASE_URL
              value: 'https://tms.pspservicesco.com'
---
apiVersion: v1
kind: Service
metadata:
  name: config-service
  namespace: tms-application
spec:
  selector:
    app: config
  ports:
    - protocol: TCP
      port: 4006
      targetPort: 4006
