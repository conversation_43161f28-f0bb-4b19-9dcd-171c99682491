apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-deployment
  namespace: tms-application
spec:
  replicas: 2
  selector:
    matchLabels:
      app: admin
  template:
    metadata:
      labels:
        app: admin
    spec:
      containers:
        - name: admin
          image: 025066247888.dkr.ecr.ca-central-1.amazonaws.com/tms_admin_backend:COMMIT_ID
          ports:
            - containerPort: 4000
          env:
            - name: NODE_ENV
              value: "production"
            - name: WINDOW
              value: "30"
            - name: MAX_LIMIT
              value: "10"
            - name: PORT
              value: "4000"
            - name: SECRET
              value: "secret"
            - name: MONGO_URI
              valueFrom:
                secretKeyRef:
                  name: updated-mongo-uri-secret
                  key: MONGO_URI
            - name: ACCESS_KEY
              value: "qwertyuiop"
            - name: TWILLO_ACCOUNT_SSID
              value: ""
            - name: TWILLO_AUTH_TOKEN
              value: ""
            - name: TWILLO_NUMBER
              value: ""
            - name: SENDGRID_API_KEY
              value: ""
            - name: SEND<PERSON><PERSON>_USER_NAME
              value: ""
            - name: SEND<PERSON>ID_HOST
              value: "smtp.sendgrid.net"
            - name: SENDGRID_PORT
              value: "587"
            - name: SENDGRID_FROM_EMAIL
              value: "<EMAIL>"
            - name: WEBSOCKET_PATH
              value: "/websockets"
            - name: MAIL_HOST
              value: "localhost"
            - name: MAIL_PORT
              value: "1025"
            - name: MAIL_USERNAME
              value: "<EMAIL>"
            - name: MAIL_PASSWORD
              value: "123456"
            - name: MAIL_FROM_ADDRESS
              value: "<EMAIL>"
            - name: MAIL_FROM_NAME
              value: "PSP-POS Team"
            - name: FIRST_ADMIN_EMAIL
              value: "<EMAIL>"
            - name: FISRT_ADMIN_PASSWORD
              value: "Test%4i90"
---
apiVersion: v1
kind: Service
metadata:
  name: admin-service
  namespace: tms-application
spec:
  selector:
    app: admin
  ports:
    - protocol: TCP
      port: 4000
      targetPort: 4000