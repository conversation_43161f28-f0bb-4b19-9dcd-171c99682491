apiVersion: apps/v1
kind: Deployment
metadata:
  name: common-deployment
  namespace: tms-application
spec:
  replicas: 3
  selector:
    matchLabels:
      app: common
  template:
    metadata:
      labels:
        app: common
    spec:
      containers:
        - name: common
          image: 025066247888.dkr.ecr.ca-central-1.amazonaws.com/tms_common_backend:COMMIT_ID
          ports:
            - containerPort: 4005
          resources:
            requests:
              memory: '2Gi'
              cpu: '2000m'
            limits:
              memory: '4Gi'
              cpu: '4000m'
          env:
            - name: NODE_ENV
              value: 'production'
            - name: WINDOW
              value: '30'
            - name: MAX_LIMIT
              value: '10'
            - name: PORT
              value: '4005'
            - name: SECRET
              value: 'secret'
            - name: ENCRYPTION_KEY
              value: 'dc35bab0996328ecfae9666bc537409bda9b112ec7a2e644abd671e692ec644c'
            - name: ENC_IV
              value: '1354e5507b839e14eb89ab45c7601a0d'
            - name: MONGO_URI
              valueFrom:
                secretKeyRef:
                  name: updated-mongo-uri-secret
                  key: MONGO_URI
            - name: ACCESS_KEY
              value: 'qwertyuiop'
            - name: TWILLO_ACCOUNT_SSID
              value: ''
            - name: TWILLO_AUTH_TOKEN
              value: ''
            - name: TWILLO_NUMBER
              value: ''
            - name: SENDGRID_API_KEY
              value: ''
            - name: SENDGRID_USER_NAME
              value: ''
            - name: SENDGRID_HOST
              value: 'smtp.sendgrid.net'
            - name: SENDGRID_PORT
              value: '587'
            - name: SENDGRID_FROM_EMAIL
              value: '<EMAIL>'
            - name: WEBSOCKET_PATH
              value: '/websockets'
            - name: MAIL_HOST
              value: 'localhost'
            - name: MAIL_PORT
              value: '1025'
            - name: MAIL_USERNAME
              value: '<EMAIL>'
            - name: MAIL_PASSWORD
              value: '123456'
            - name: MAIL_FROM_ADDRESS
              value: '<EMAIL>'
            - name: MAIL_FROM_NAME
              value: 'PSP-POS Team'
            - name: REDIS_URL
              # value: 'redis://ec2-13-201-48-23.ap-south-1.compute.amazonaws.com:6379'
              value: 'redis://tms-prod-prod-redis-cluster.gvddv0.0001.cac1.cache.amazonaws.com:6379'
            - name: AWS_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-access-key
                  key: AWS_ACCESS_KEY
            - name: AWS_SECRET_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-secret-key-id
                  key: AWS_SECRET_KEY_ID
            - name: AWS_REGION
              valueFrom:
                secretKeyRef:
                  name: aws-region
                  key: AWS_REGION
            - name: AWS_BUCKET_NAME
              valueFrom:
                secretKeyRef:
                  name: aws-bucket-name
                  key: AWS_BUCKET_NAME
            - name: MQTT_URL
              value: rabbitmq.pspservicesco.com
            - name: MQTT_PORT
              value: '8883'
            - name: MQTT_CA
              value: './utils/certs-prod/certs/ca_cert.pem'
            - name: MQTT_CERT
              value: './utils/certs-prod/certs/client_cert.pem'
            - name: MQTT_KEY
              value: './utils/certs-prod/certs/client_key.pem'
            - name: MERCHANT_TOOL_URL
              value: 'https://merchanttool.pspservicesco.com'
            - name: MERCHANT_TOOL_API_KEY
              value: 'EuC/gBCfS#0txdB$AP2;u<#I)1/-r7R`d2H{2M>i-DF4+M'

---
apiVersion: v1
kind: Service
metadata:
  name: common-service
  namespace: tms-application
spec:
  selector:
    app: common
  ports:
    - protocol: TCP
      port: 4005
      targetPort: 4005
