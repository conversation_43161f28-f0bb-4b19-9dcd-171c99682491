{"openapi": "3.1.0", "info": {"title": "AMEX Service API", "description": "American Express integration service\n\nGenerated on: 2025-06-13T08:12:20.869Z", "version": "1.0.0", "contact": {"name": "PSP Development Team", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://pspservicesco.com/license"}}, "servers": [{"url": "http://localhost:4008/amex", "description": "Local development server"}, {"url": "https://tms-dev.pspservicesco.com/amex", "description": "Development server"}, {"url": "https://tms.pspservicesco.com/amex", "description": "Production server"}], "components": {"schemas": {"SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200, "description": "HTTP status code"}, "success": {"type": "boolean", "example": true, "description": "Operation success status"}, "message": {"type": "string", "example": "Operation completed successfully", "description": "Response message"}, "data": {"type": "object", "description": "Response data payload"}}, "required": ["code", "success", "message"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400, "description": "HTTP status code"}, "success": {"type": "boolean", "example": false, "description": "Operation success status"}, "message": {"type": "string", "example": "Validation error", "description": "Error message"}, "error": {"type": "boolean", "example": true, "description": "Error flag"}}, "required": ["code", "success", "message"]}, "PaginationQuery": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number"}, "itemsPerPage": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Items per page"}, "search": {"type": "string", "description": "Search query"}, "sortBy": {"type": "string", "description": "Field to sort by"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "Sort order"}}}, "PaginatedResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Data retrieved successfully"}, "data": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object", "properties": {"currentPage": {"type": "integer", "example": 1}, "totalPages": {"type": "integer", "example": 10}, "totalItems": {"type": "integer", "example": 100}, "itemsPerPage": {"type": "integer", "example": 10}}}}}, "ObjectId": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$", "example": "507f1f77bcf86cd799439011", "description": "MongoDB ObjectId"}, "Timestamp": {"type": "string", "format": "date-time", "example": "2023-12-01T10:30:00.000Z", "description": "ISO 8601 timestamp"}, "Status": {"type": "string", "enum": ["Active", "Inactive", "Pending", "Suspended"], "description": "Entity status"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "example": "password123", "description": "User password"}, "token": {"type": "string", "example": "123456", "description": "2FA token"}}, "required": ["email", "password"]}, "AuthResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful!"}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "description": "JWT authentication token"}, "admin": {"type": "object", "description": "User information"}}}, "Merchant": {"type": "object", "properties": {"_id": {"$ref": "#/components/schemas/ObjectId"}, "record_number": {"type": "string", "example": "MRC001234", "description": "Merchant record number"}, "merchant_name": {"type": "string", "example": "ABC Store Inc.", "description": "Merchant business name"}, "merchant_id": {"type": "string", "example": "AMEX123456", "description": "AMEX merchant ID"}, "dba_name": {"type": "string", "example": "ABC Store", "description": "Doing business as name"}, "address": {"type": "object", "properties": {"street": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "state": {"type": "string", "example": "NY"}, "zip": {"type": "string", "example": "10001"}, "country": {"type": "string", "example": "USA"}}}, "status": {"$ref": "#/components/schemas/Status"}, "created_at": {"$ref": "#/components/schemas/Timestamp"}, "updated_at": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["record_number", "merchant_name", "merchant_id"]}, "CreateMerchantRequest": {"type": "object", "properties": {"record_number": {"type": "string", "minLength": 3, "example": "MRC001234", "description": "Merchant record number"}, "merchant_name": {"type": "string", "minLength": 2, "example": "ABC Store Inc.", "description": "Merchant business name"}, "merchant_id": {"type": "string", "minLength": 3, "example": "AMEX123456", "description": "AMEX merchant ID"}, "dba_name": {"type": "string", "example": "ABC Store", "description": "Doing business as name"}, "address": {"type": "object", "properties": {"street": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "state": {"type": "string", "example": "NY"}, "zip": {"type": "string", "example": "10001"}, "country": {"type": "string", "example": "USA"}}, "required": ["street", "city", "state", "zip"]}}, "required": ["record_number", "merchant_name", "merchant_id", "address"]}, "UpdateAmexRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "Updated Amex", "description": "Amex name"}, "description": {"type": "string", "example": "Updated description", "description": "Amex description"}, "status": {"$ref": "#/components/schemas/Status"}}}, "AmexListResponse": {"allOf": [{"$ref": "#/components/schemas/PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Amex"}}}}]}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for admin authentication"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key", "description": "API key for device authentication"}, "AccessKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "Access key for internal service communication"}}, "responses": {"UnauthorizedError": {"description": "Authentication information is missing or invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 401, "success": false, "message": "Unauthorized access"}}}}, "ForbiddenError": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 403, "success": false, "message": "Access forbidden"}}}}, "NotFoundError": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "success": false, "message": "Resource not found"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "Validation failed"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "success": false, "message": "Internal server error"}}}}}, "parameters": {"PageQuery": {"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, "ItemsPerPageQuery": {"name": "itemsPerPage", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, "SearchQuery": {"name": "search", "in": "query", "description": "Search query string", "required": false, "schema": {"type": "string"}}, "ObjectIdPath": {"name": "id", "in": "path", "description": "MongoDB ObjectId", "required": true, "schema": {"$ref": "#/components/schemas/ObjectId"}}}}, "tags": [{"name": "Health", "description": "Service health and status endpoints"}, {"name": "Authentication", "description": "Authentication and authorization endpoints"}], "paths": {"/amex/v1/merchant": {"get": {"tags": ["Merchant Management"], "summary": "Get all merchants", "description": "Retrieve a list of all AMEX merchants", "responses": {"200": {"description": "Merchants retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}, "post": {"tags": ["Merchant Management"], "summary": "Create merchant", "description": "Create a new AMEX merchant", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMerchantRequest"}}}}, "responses": {"201": {"description": "Merchant created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}, "delete": {"tags": ["Merchant Management"], "summary": "Delete merchant", "description": "Delete an AMEX merchant", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"record_number": {"type": "string", "description": "Merchant record number to delete"}}, "required": ["record_number"]}}}}, "responses": {"200": {"description": "Merchant deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/amex/v1/merchant/{record_number}": {"get": {"tags": ["Merchant Management"], "summary": "Get merchant by record number", "description": "Retrieve a specific AMEX merchant by record number", "parameters": [{"name": "record_number", "in": "path", "required": true, "description": "Merchant record number", "schema": {"type": "string"}}], "responses": {"200": {"description": "Merchant retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}}}