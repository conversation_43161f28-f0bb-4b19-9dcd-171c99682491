{"openapi": "3.1.0", "info": {"title": "TERMINAL Service API", "description": "Terminal management and operations service\n\nGenerated on: 2025-06-13T08:12:20.686Z", "version": "1.0.0", "contact": {"name": "PSP Development Team", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://pspservicesco.com/license"}}, "servers": [{"url": "http://localhost:4001/terminal", "description": "Local development server"}, {"url": "https://tms-dev.pspservicesco.com/terminal", "description": "Development server"}, {"url": "https://tms.pspservicesco.com/terminal", "description": "Production server"}], "components": {"schemas": {"SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200, "description": "HTTP status code"}, "success": {"type": "boolean", "example": true, "description": "Operation success status"}, "message": {"type": "string", "example": "Operation completed successfully", "description": "Response message"}, "data": {"type": "object", "description": "Response data payload"}}, "required": ["code", "success", "message"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400, "description": "HTTP status code"}, "success": {"type": "boolean", "example": false, "description": "Operation success status"}, "message": {"type": "string", "example": "Validation error", "description": "Error message"}, "error": {"type": "boolean", "example": true, "description": "Error flag"}}, "required": ["code", "success", "message"]}, "PaginationQuery": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number"}, "itemsPerPage": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Items per page"}, "search": {"type": "string", "description": "Search query"}, "sortBy": {"type": "string", "description": "Field to sort by"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "Sort order"}}}, "PaginatedResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Data retrieved successfully"}, "data": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object", "properties": {"currentPage": {"type": "integer", "example": 1}, "totalPages": {"type": "integer", "example": 10}, "totalItems": {"type": "integer", "example": 100}, "itemsPerPage": {"type": "integer", "example": 10}}}}}, "ObjectId": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$", "example": "507f1f77bcf86cd799439011", "description": "MongoDB ObjectId"}, "Timestamp": {"type": "string", "format": "date-time", "example": "2023-12-01T10:30:00.000Z", "description": "ISO 8601 timestamp"}, "Status": {"type": "string", "enum": ["Active", "Inactive", "Pending", "Suspended"], "description": "Entity status"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "example": "password123", "description": "User password"}, "token": {"type": "string", "example": "123456", "description": "2FA token"}}, "required": ["email", "password"]}, "AuthResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful!"}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "description": "JWT authentication token"}, "admin": {"type": "object", "description": "User information"}}}, "Terminal": {"type": "object", "properties": {"_id": {"$ref": "#/components/schemas/ObjectId"}, "serial_number": {"type": "string", "example": "TRM001234", "description": "Terminal serial number"}, "terminal_name": {"type": "string", "example": "Store Front Terminal", "description": "Terminal display name"}, "store_id": {"$ref": "#/components/schemas/ObjectId", "description": "Associated store ID"}, "business_id": {"$ref": "#/components/schemas/ObjectId", "description": "Associated business ID"}, "terminal_type": {"type": "string", "enum": ["POS", "ATM", "KIOSK"], "example": "POS", "description": "Type of terminal"}, "api_key": {"type": "string", "example": "api_key_123456", "description": "Terminal API key"}, "status": {"$ref": "#/components/schemas/Status"}, "last_ping": {"$ref": "#/components/schemas/Timestamp", "description": "Last ping timestamp"}, "created_at": {"$ref": "#/components/schemas/Timestamp"}, "updated_at": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["serial_number", "terminal_name", "store_id", "business_id"]}, "CreateTerminalRequest": {"type": "object", "properties": {"serial_number": {"type": "string", "minLength": 3, "example": "TRM001234", "description": "Terminal serial number"}, "terminal_name": {"type": "string", "minLength": 2, "example": "Store Front Terminal", "description": "Terminal display name"}, "store_id": {"$ref": "#/components/schemas/ObjectId", "description": "Store ID to assign terminal to"}, "business_id": {"$ref": "#/components/schemas/ObjectId", "description": "Business ID to assign terminal to"}, "terminal_type": {"type": "string", "enum": ["POS", "ATM", "KIOSK"], "example": "POS", "description": "Type of terminal"}, "location": {"type": "string", "example": "Front Counter", "description": "Terminal location description"}}, "required": ["serial_number", "terminal_name", "store_id", "business_id", "terminal_type"]}, "UpdateTerminalRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "Updated Terminal", "description": "Terminal name"}, "description": {"type": "string", "example": "Updated description", "description": "Terminal description"}, "status": {"$ref": "#/components/schemas/Status"}}}, "TerminalListResponse": {"allOf": [{"$ref": "#/components/schemas/PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Terminal"}}}}]}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for admin authentication"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key", "description": "API key for device authentication"}, "AccessKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "Access key for internal service communication"}}, "responses": {"UnauthorizedError": {"description": "Authentication information is missing or invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 401, "success": false, "message": "Unauthorized access"}}}}, "ForbiddenError": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 403, "success": false, "message": "Access forbidden"}}}}, "NotFoundError": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "success": false, "message": "Resource not found"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "Validation failed"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "success": false, "message": "Internal server error"}}}}}, "parameters": {"PageQuery": {"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, "ItemsPerPageQuery": {"name": "itemsPerPage", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, "SearchQuery": {"name": "search", "in": "query", "description": "Search query string", "required": false, "schema": {"type": "string"}}, "ObjectIdPath": {"name": "id", "in": "path", "description": "MongoDB ObjectId", "required": true, "schema": {"$ref": "#/components/schemas/ObjectId"}}}}, "tags": [{"name": "Health", "description": "Service health and status endpoints"}, {"name": "Authentication", "description": "Authentication and authorization endpoints"}], "paths": {"/terminal/v1/activate-terminal": {"post": {"tags": ["Activate Terminal Management"], "summary": "Create activate terminal", "description": "Create activate terminal in the terminal service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"serial_number": {"type": "string", "example": "TRM001234"}, "activation_code": {"type": "string", "example": "ACT123456"}}, "required": ["serial_number", "activation_code"]}}}}, "responses": {"200": {"description": "Create activate terminal successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/terminal/v1/receipt/{id}": {"get": {"tags": ["Terminal Management"], "summary": "Send receipt via SMS", "description": "Send transaction receipt to customer via SMS", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Transaction ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Receipt sent successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/receipt/:id": {"get": {"tags": ["Receipt Management"], "summary": "Get receipt", "description": "Retrieve receipt in the terminal service", "responses": {"200": {"description": "Get receipt successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/terminal/v1/create-terminal": {"post": {"tags": ["Create Terminal Management"], "summary": "Create create terminal", "description": "Create create terminal in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTerminalRequest", "type": "object"}}}}, "responses": {"200": {"description": "Create create terminal successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "201": {"description": "Terminal created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/update-terminal/{id}": {"patch": {"tags": ["Terminal Management"], "summary": "Update terminal", "description": "Update terminal information", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTerminalRequest"}}}}, "responses": {"200": {"description": "Terminal updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/update-terminal/:id": {"put": {"tags": ["Update Terminal Management"], "summary": "Update update terminal", "description": "Update update terminal in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update terminal successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/delete-terminal/{id}": {"delete": {"tags": ["Terminal Management"], "summary": "Delete terminal", "description": "Delete a terminal from the system", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Terminal deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/delete-terminal/:id": {"delete": {"tags": ["Delete Terminal Management"], "summary": "Delete delete terminal", "description": "Delete delete terminal in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete delete terminal successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/get-all-terminals": {"get": {"tags": ["Get All Terminals Management"], "summary": "Get get all terminals", "description": "Retrieve get all terminals in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all terminals successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/get-single-terminal/{id}": {"get": {"tags": ["Terminal Management"], "summary": "Get single terminal", "description": "Retrieve details of a specific terminal", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Terminal details retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Terminal"}}}]}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/get-single-terminal/:id": {"get": {"tags": ["Get Single Terminal Management"], "summary": "Get get single terminal", "description": "Retrieve get single terminal in the terminal service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get single terminal successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/change-terminal-status/{id}": {"put": {"tags": ["Terminal Management"], "summary": "Change terminal status", "description": "Change the status of a terminal (Active/Inactive)", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/Status"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Terminal status changed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/change-terminal-status/:id": {"put": {"tags": ["Change Terminal Status Management"], "summary": "Update change terminal status", "description": "Update change terminal status in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update change terminal status successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/revoke-api/:id": {"post": {"tags": ["Revoke Api Management"], "summary": "Create revoke api", "description": "Create revoke api in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create revoke api successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/update-ping-time/:id": {"post": {"tags": ["Update Ping Time Management"], "summary": "Create update ping time", "description": "Create update ping time in the terminal service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create update ping time successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/terminal/v1/update-ping-all-device": {"post": {"tags": ["Update Ping All Device Management"], "summary": "Create update ping all device", "description": "Create update ping all device in the terminal service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create update ping all device successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/terminal/v1/force-deactivate-device/:id": {"put": {"tags": ["Force Deactivate Device Management"], "summary": "Update force deactivate device", "description": "Update force deactivate device in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update force deactivate device successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/tag-string/:id": {"put": {"tags": ["Tag String Management"], "summary": "Update tag string", "description": "Update tag string in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update tag string successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/get-location/:id": {"get": {"tags": ["Get Location Management"], "summary": "Get get location", "description": "Retrieve get location in the terminal service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get location successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/report": {"post": {"tags": ["Report Management"], "summary": "Create report", "description": "Create report in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create report successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/report/:store_id": {"get": {"tags": ["Report Management"], "summary": "Get report", "description": "Retrieve report in the terminal service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get report successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/create-transaction": {"post": {"tags": ["Create Transaction Management"], "summary": "Create create transaction", "description": "Create create transaction in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create create transaction successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/get-terminal-transactions": {"post": {"tags": ["Get Terminal Transactions Management"], "summary": "Create get terminal transactions", "description": "Create get terminal transactions in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create get terminal transactions successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/send-receipt-email": {"post": {"tags": ["Send Receipt Email Management"], "summary": "Create send receipt email", "description": "Create send receipt email in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create send receipt email successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/send-receipt-sms": {"post": {"tags": ["Send Receipt Sms Management"], "summary": "Create send receipt sms", "description": "Create send receipt sms in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create send receipt sms successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/report-email": {"post": {"tags": ["Report Email Management"], "summary": "Create report email", "description": "Create report email in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create report email successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/create-device-group": {"post": {"tags": ["Create Device Group Management"], "summary": "Create create device group", "description": "Create create device group in the terminal service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create create device group successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/delete-device-group/:id": {"delete": {"tags": ["Delete Device Group Management"], "summary": "Delete delete device group", "description": "Delete delete device group in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete delete device group successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/get-group-details/:id": {"get": {"tags": ["Get Group Details Management"], "summary": "Get get group details", "description": "Retrieve get group details in the terminal service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get group details successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/update-device-group/:id": {"put": {"tags": ["Update Device Group Management"], "summary": "Update update device group", "description": "Update update device group in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update device group successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/terminal/v1/get-all-device-groups": {"get": {"tags": ["Get All Device Groups Management"], "summary": "Get get all device groups", "description": "Retrieve get all device groups in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all device groups successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/terminal/v1/deactivate-device/:id": {"put": {"tags": ["Deactivate Device Management"], "summary": "Update deactivate device", "description": "Update deactivate device in the terminal service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update deactivate device successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}}}