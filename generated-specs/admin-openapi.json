{"openapi": "3.1.0", "info": {"title": "ADMIN Service API", "description": "Admin management and authentication service\n\nGenerated on: 2025-06-13T08:12:20.633Z", "version": "1.0.0", "contact": {"name": "PSP Development Team", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://pspservicesco.com/license"}}, "servers": [{"url": "http://localhost:4000/admin", "description": "Local development server"}, {"url": "https://tms-dev.pspservicesco.com/admin", "description": "Development server"}, {"url": "https://tms.pspservicesco.com/admin", "description": "Production server"}], "components": {"schemas": {"SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200, "description": "HTTP status code"}, "success": {"type": "boolean", "example": true, "description": "Operation success status"}, "message": {"type": "string", "example": "Operation completed successfully", "description": "Response message"}, "data": {"type": "object", "description": "Response data payload"}}, "required": ["code", "success", "message"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400, "description": "HTTP status code"}, "success": {"type": "boolean", "example": false, "description": "Operation success status"}, "message": {"type": "string", "example": "Validation error", "description": "Error message"}, "error": {"type": "boolean", "example": true, "description": "Error flag"}}, "required": ["code", "success", "message"]}, "PaginationQuery": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number"}, "itemsPerPage": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Items per page"}, "search": {"type": "string", "description": "Search query"}, "sortBy": {"type": "string", "description": "Field to sort by"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "Sort order"}}}, "PaginatedResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Data retrieved successfully"}, "data": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object", "properties": {"currentPage": {"type": "integer", "example": 1}, "totalPages": {"type": "integer", "example": 10}, "totalItems": {"type": "integer", "example": 100}, "itemsPerPage": {"type": "integer", "example": 10}}}}}, "ObjectId": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$", "example": "507f1f77bcf86cd799439011", "description": "MongoDB ObjectId"}, "Timestamp": {"type": "string", "format": "date-time", "example": "2023-12-01T10:30:00.000Z", "description": "ISO 8601 timestamp"}, "Status": {"type": "string", "enum": ["Active", "Inactive", "Pending", "Suspended"], "description": "Entity status"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "example": "password123", "description": "User password"}, "token": {"type": "string", "example": "123456", "description": "2FA token"}}, "required": ["email", "password"]}, "AuthResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful!"}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "description": "JWT authentication token"}, "admin": {"type": "object", "description": "User information"}}}, "Admin": {"type": "object", "properties": {"_id": {"$ref": "#/components/schemas/ObjectId"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "Admin email address"}, "first_name": {"type": "string", "example": "<PERSON>", "description": "Admin first name"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>", "description": "<PERSON><PERSON> last name"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectId"}, "description": "Admin role IDs"}, "permissions": {"type": "array", "items": {"type": "string"}, "description": "Admin permissions"}, "status": {"$ref": "#/components/schemas/Status"}, "created_at": {"$ref": "#/components/schemas/Timestamp"}, "updated_at": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["email", "first_name", "last_name"]}, "CreateAdminRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "Admin email address"}, "password": {"type": "string", "minLength": 8, "example": "SecurePassword123!", "description": "Admin password"}, "first_name": {"type": "string", "minLength": 2, "example": "<PERSON>", "description": "Admin first name"}, "last_name": {"type": "string", "minLength": 2, "example": "<PERSON>", "description": "<PERSON><PERSON> last name"}, "role": {"type": "string", "enum": ["super_admin", "admin", "manager"], "example": "admin", "description": "Admin role type"}}, "required": ["email", "password", "first_name", "last_name", "role"]}, "Role": {"type": "object", "properties": {"_id": {"$ref": "#/components/schemas/ObjectId"}, "name": {"type": "string", "example": "Administrator", "description": "Role name"}, "type": {"type": "string", "example": "admin", "description": "Role type"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectId"}, "description": "Permission IDs associated with this role"}, "description": {"type": "string", "example": "Full system administrator access", "description": "Role description"}, "created_at": {"$ref": "#/components/schemas/Timestamp"}, "updated_at": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["name", "type"]}, "Permission": {"type": "object", "properties": {"_id": {"$ref": "#/components/schemas/ObjectId"}, "name": {"type": "string", "example": "Create User", "description": "Permission name"}, "can": {"type": "string", "example": "create_user", "description": "Permission identifier"}, "description": {"type": "string", "example": "Allows creating new users", "description": "Permission description"}, "module": {"type": "string", "example": "users", "description": "Module this permission belongs to"}, "created_at": {"$ref": "#/components/schemas/Timestamp"}, "updated_at": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["name", "can", "module"]}, "CreateRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "Store Manager", "description": "Role name"}, "type": {"type": "string", "example": "manager", "description": "Role type"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectId"}, "description": "Permission IDs to assign to this role"}, "description": {"type": "string", "example": "Manages store operations", "description": "Role description"}}, "required": ["name", "type", "permissions"]}, "CreatePermissionRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "Delete Terminal", "description": "Permission name"}, "can": {"type": "string", "minLength": 2, "example": "delete_terminal", "description": "Permission identifier"}, "description": {"type": "string", "example": "Allows deleting terminals", "description": "Permission description"}, "module": {"type": "string", "example": "terminals", "description": "Module this permission belongs to"}}, "required": ["name", "can", "module"]}, "AdminListResponse": {"allOf": [{"$ref": "#/components/schemas/PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Admin"}}}}]}, "RoleListResponse": {"allOf": [{"$ref": "#/components/schemas/PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}]}, "PermissionListResponse": {"allOf": [{"$ref": "#/components/schemas/PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Permission"}}}}]}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for admin authentication"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key", "description": "API key for device authentication"}, "AccessKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "Access key for internal service communication"}}, "responses": {"UnauthorizedError": {"description": "Authentication information is missing or invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 401, "success": false, "message": "Unauthorized access"}}}}, "ForbiddenError": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 403, "success": false, "message": "Access forbidden"}}}}, "NotFoundError": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "success": false, "message": "Resource not found"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "Validation failed"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "success": false, "message": "Internal server error"}}}}}, "parameters": {"PageQuery": {"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, "ItemsPerPageQuery": {"name": "itemsPerPage", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, "SearchQuery": {"name": "search", "in": "query", "description": "Search query string", "required": false, "schema": {"type": "string"}}, "ObjectIdPath": {"name": "id", "in": "path", "description": "MongoDB ObjectId", "required": true, "schema": {"$ref": "#/components/schemas/ObjectId"}}}}, "tags": [{"name": "Health", "description": "Service health and status endpoints"}, {"name": "Authentication", "description": "Authentication and authorization endpoints"}], "paths": {"/admin/v1/login": {"post": {"tags": ["Login Management"], "summary": "Create login", "description": "Create login in the admin service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest", "type": "object"}, "example": {"email": "<EMAIL>", "password": "password123", "token": "123456"}}}}, "responses": {"200": {"description": "Create login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/register": {"post": {"tags": ["Register Management"], "summary": "Create register", "description": "Create register in the admin service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminRequest", "type": "object"}}}}, "responses": {"200": {"description": "Create register successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "201": {"description": "Admin registered successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "409": {"description": "Admin already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/admin/v1/logout": {"delete": {"tags": ["Logout Management"], "summary": "Delete logout", "description": "Delete logout in the admin service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Delete logout successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}, "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}]}}, "/admin/v1/add-admin": {"post": {"tags": ["Add Admin Management"], "summary": "Create add admin", "description": "Create add admin in the admin service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminRequest", "type": "object"}}}}, "responses": {"200": {"description": "Create add admin successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "201": {"description": "Admin created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "409": {"description": "Admin already exists"}}}}, "/admin/v1/delete-admin/{id}": {"delete": {"tags": ["Admin Management"], "summary": "Delete admin", "description": "Delete an admin user by ID", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Admin deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/admin/v1/delete-admin/:id": {"delete": {"tags": ["Delete Admin Management"], "summary": "Delete delete admin", "description": "Delete delete admin in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete delete admin successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/admin/v1/get-all-admins": {"get": {"tags": ["Get All Admins Management"], "summary": "Get get all admins", "description": "Retrieve get all admins in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all admins successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/get-perms": {"get": {"tags": ["Get Perms Management"], "summary": "Get get perms", "description": "Retrieve get perms in the admin service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get perms successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["create_user", "delete_user", "manage_roles"]}}, "$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/signup": {"post": {"tags": ["Signup Management"], "summary": "Create signup", "description": "Create signup in the admin service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create signup successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/admin/v1/update-admin/:id": {"put": {"tags": ["Update Admin Management"], "summary": "Update update admin", "description": "Update update admin in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update admin successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/admin/v1/force-logout-admin/:id": {"post": {"tags": ["Force Logout Admin Management"], "summary": "Create force logout admin", "description": "Create force logout admin in the admin service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create force logout admin successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/create-role": {"post": {"tags": ["Create Role Management"], "summary": "Create create role", "description": "Create create role in the admin service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create create role successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/get-all-role": {"get": {"tags": ["Get All Role Management"], "summary": "Get get all role", "description": "Retrieve get all role in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all role successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/update-role/:id": {"put": {"tags": ["Update Role Management"], "summary": "Update update role", "description": "Update update role in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update role successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/admin/v1/delete-role/:id": {"delete": {"tags": ["Delete Role Management"], "summary": "Delete delete role", "description": "Delete delete role in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete delete role successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/admin/v1/create-permission": {"post": {"tags": ["Create Permission Management"], "summary": "Create create permission", "description": "Create create permission in the admin service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create create permission successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/get-all-permission": {"get": {"tags": ["Get All Permission Management"], "summary": "Get get all permission", "description": "Retrieve get all permission in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all permission successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/restore-permissions": {"post": {"tags": ["Restore Permissions Management"], "summary": "Create restore permissions", "description": "Create restore permissions in the admin service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create restore permissions successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/admin/v1/delete-permission/:id": {"delete": {"tags": ["Delete Permission Management"], "summary": "Delete delete permission", "description": "Delete delete permission in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete delete permission successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/admin/v1/update-permission/:id": {"put": {"tags": ["Update Permission Management"], "summary": "Update update permission", "description": "Update update permission in the admin service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update permission successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/admin/v1/add-permission-roles": {"post": {"tags": ["Add Permission Roles Management"], "summary": "Create add permission roles", "description": "Create add permission roles in the admin service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create add permission roles successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}}}