{"openapi": "3.1.0", "info": {"title": "STORE Service API", "description": "Store management and configuration service\n\nGenerated on: 2025-06-13T08:12:20.710Z", "version": "1.0.0", "contact": {"name": "PSP Development Team", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://pspservicesco.com/license"}}, "servers": [{"url": "http://localhost:4002/store", "description": "Local development server"}, {"url": "https://tms-dev.pspservicesco.com/store", "description": "Development server"}, {"url": "https://tms.pspservicesco.com/store", "description": "Production server"}], "components": {"schemas": {"SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200, "description": "HTTP status code"}, "success": {"type": "boolean", "example": true, "description": "Operation success status"}, "message": {"type": "string", "example": "Operation completed successfully", "description": "Response message"}, "data": {"type": "object", "description": "Response data payload"}}, "required": ["code", "success", "message"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400, "description": "HTTP status code"}, "success": {"type": "boolean", "example": false, "description": "Operation success status"}, "message": {"type": "string", "example": "Validation error", "description": "Error message"}, "error": {"type": "boolean", "example": true, "description": "Error flag"}}, "required": ["code", "success", "message"]}, "PaginationQuery": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number"}, "itemsPerPage": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Items per page"}, "search": {"type": "string", "description": "Search query"}, "sortBy": {"type": "string", "description": "Field to sort by"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "Sort order"}}}, "PaginatedResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Data retrieved successfully"}, "data": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object", "properties": {"currentPage": {"type": "integer", "example": 1}, "totalPages": {"type": "integer", "example": 10}, "totalItems": {"type": "integer", "example": 100}, "itemsPerPage": {"type": "integer", "example": 10}}}}}, "ObjectId": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$", "example": "507f1f77bcf86cd799439011", "description": "MongoDB ObjectId"}, "Timestamp": {"type": "string", "format": "date-time", "example": "2023-12-01T10:30:00.000Z", "description": "ISO 8601 timestamp"}, "Status": {"type": "string", "enum": ["Active", "Inactive", "Pending", "Suspended"], "description": "Entity status"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "example": "password123", "description": "User password"}, "token": {"type": "string", "example": "123456", "description": "2FA token"}}, "required": ["email", "password"]}, "AuthResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful!"}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "description": "JWT authentication token"}, "admin": {"type": "object", "description": "User information"}}}, "Store": {"type": "object", "properties": {"_id": {"$ref": "#/components/schemas/ObjectId"}, "name": {"type": "string", "example": "Sample Store", "description": "Store name"}, "status": {"$ref": "#/components/schemas/Status"}, "created_at": {"$ref": "#/components/schemas/Timestamp"}, "updated_at": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["name"]}, "CreateStoreRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "New Store", "description": "Store name"}, "description": {"type": "string", "example": "Store description", "description": "Store description"}}, "required": ["name"]}, "UpdateStoreRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "Updated Store", "description": "Store name"}, "description": {"type": "string", "example": "Updated description", "description": "Store description"}, "status": {"$ref": "#/components/schemas/Status"}}}, "StoreListResponse": {"allOf": [{"$ref": "#/components/schemas/PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Store"}}}}]}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for admin authentication"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key", "description": "API key for device authentication"}, "AccessKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "Access key for internal service communication"}}, "responses": {"UnauthorizedError": {"description": "Authentication information is missing or invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 401, "success": false, "message": "Unauthorized access"}}}}, "ForbiddenError": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 403, "success": false, "message": "Access forbidden"}}}}, "NotFoundError": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "success": false, "message": "Resource not found"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "Validation failed"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "success": false, "message": "Internal server error"}}}}}, "parameters": {"PageQuery": {"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, "ItemsPerPageQuery": {"name": "itemsPerPage", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, "SearchQuery": {"name": "search", "in": "query", "description": "Search query string", "required": false, "schema": {"type": "string"}}, "ObjectIdPath": {"name": "id", "in": "path", "description": "MongoDB ObjectId", "required": true, "schema": {"$ref": "#/components/schemas/ObjectId"}}}}, "tags": [{"name": "Health", "description": "Service health and status endpoints"}, {"name": "Authentication", "description": "Authentication and authorization endpoints"}], "paths": {"/store/v1/create-store": {"post": {"tags": ["Create Store Management"], "summary": "Create create store", "description": "Create create store in the store service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStoreRequest", "type": "object"}}}}, "responses": {"200": {"description": "Create create store successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "201": {"description": "Store created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/store/v1/get-all-stores": {"get": {"tags": ["Get All Stores Management"], "summary": "Get get all stores", "description": "Retrieve get all stores in the store service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all stores successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/store/v1/update-store/{id}": {"put": {"tags": ["Store Management"], "summary": "Update store", "description": "Update store information", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStoreRequest"}}}}, "responses": {"200": {"description": "Store updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/store/v1/update-store/:id": {"put": {"tags": ["Update Store Management"], "summary": "Update update store", "description": "Update update store in the store service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update store successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/store/v1/delete-store/{id}": {"post": {"tags": ["Store Management"], "summary": "Delete store", "description": "Delete a store from the system", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Store deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/store/v1/delete-store/:id": {"post": {"tags": ["Delete Store Management"], "summary": "Create delete store", "description": "Create delete store in the store service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create delete store successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/store/v1/get-store-details/:id": {"get": {"tags": ["Get Store Details Management"], "summary": "Get get store details", "description": "Retrieve get store details in the store service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get store details successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/store/v1/change-store-status/:id": {"put": {"tags": ["Change Store Status Management"], "summary": "Update change store status", "description": "Update change store status in the store service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update change store status successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/store/v1/update-passcode": {"put": {"tags": ["Update Passcode Management"], "summary": "Update update passcode", "description": "Update update passcode in the store service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update passcode successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/store/v1/update-bin-ranges": {"post": {"tags": ["Update Bin Ranges Management"], "summary": "Create update bin ranges", "description": "Create update bin ranges in the store service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create update bin ranges successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/store/v1/batch-report-setting/:id": {"put": {"tags": ["Batch Report Setting Management"], "summary": "Update batch report setting", "description": "Update batch report setting in the store service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update batch report setting successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/store/v1/bin-range": {"get": {"tags": ["Bin Range Management"], "summary": "Get bin range", "description": "Retrieve bin range in the store service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get bin range successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "put": {"tags": ["Bin Range Management"], "summary": "Update bin range", "description": "Update bin range in the store service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update bin range successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/store/v1/check-bin-range": {"post": {"tags": ["Check Bin Range Management"], "summary": "Create check bin range", "description": "Create check bin range in the store service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create check bin range successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}}}