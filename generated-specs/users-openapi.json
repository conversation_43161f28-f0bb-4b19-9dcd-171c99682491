{"openapi": "3.1.0", "info": {"title": "USERS Service API", "description": "User management and authentication service\n\nGenerated on: 2025-06-13T08:12:20.771Z", "version": "1.0.0", "contact": {"name": "PSP Development Team", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://pspservicesco.com/license"}}, "servers": [{"url": "http://localhost:4004/users", "description": "Local development server"}, {"url": "https://tms-dev.pspservicesco.com/users", "description": "Development server"}, {"url": "https://tms.pspservicesco.com/users", "description": "Production server"}], "components": {"schemas": {"SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200, "description": "HTTP status code"}, "success": {"type": "boolean", "example": true, "description": "Operation success status"}, "message": {"type": "string", "example": "Operation completed successfully", "description": "Response message"}, "data": {"type": "object", "description": "Response data payload"}}, "required": ["code", "success", "message"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400, "description": "HTTP status code"}, "success": {"type": "boolean", "example": false, "description": "Operation success status"}, "message": {"type": "string", "example": "Validation error", "description": "Error message"}, "error": {"type": "boolean", "example": true, "description": "Error flag"}}, "required": ["code", "success", "message"]}, "PaginationQuery": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number"}, "itemsPerPage": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Items per page"}, "search": {"type": "string", "description": "Search query"}, "sortBy": {"type": "string", "description": "Field to sort by"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "Sort order"}}}, "PaginatedResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Data retrieved successfully"}, "data": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object", "properties": {"currentPage": {"type": "integer", "example": 1}, "totalPages": {"type": "integer", "example": 10}, "totalItems": {"type": "integer", "example": 100}, "itemsPerPage": {"type": "integer", "example": 10}}}}}, "ObjectId": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$", "example": "507f1f77bcf86cd799439011", "description": "MongoDB ObjectId"}, "Timestamp": {"type": "string", "format": "date-time", "example": "2023-12-01T10:30:00.000Z", "description": "ISO 8601 timestamp"}, "Status": {"type": "string", "enum": ["Active", "Inactive", "Pending", "Suspended"], "description": "Entity status"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "example": "password123", "description": "User password"}, "token": {"type": "string", "example": "123456", "description": "2FA token"}}, "required": ["email", "password"]}, "AuthResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful!"}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "description": "JWT authentication token"}, "admin": {"type": "object", "description": "User information"}}}, "Users": {"type": "object", "properties": {"_id": {"$ref": "#/components/schemas/ObjectId"}, "first_name": {"type": "string", "example": "<PERSON>", "description": "User first name"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>", "description": "User last name"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "User email address"}, "phone": {"type": "string", "example": "+1234567890", "description": "User phone number"}, "employee_id": {"type": "string", "example": "EMP001", "description": "Employee ID"}, "store_id": {"$ref": "#/components/schemas/ObjectId", "description": "Associated store ID"}, "business_id": {"$ref": "#/components/schemas/ObjectId", "description": "Associated business ID"}, "role": {"type": "string", "enum": ["cashier", "manager", "supervisor"], "example": "cashier", "description": "User role"}, "status": {"$ref": "#/components/schemas/Status"}, "created_at": {"$ref": "#/components/schemas/Timestamp"}, "updated_at": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["first_name", "last_name", "email", "employee_id", "store_id", "role"]}, "CreateUsersRequest": {"type": "object", "properties": {"first_name": {"type": "string", "minLength": 2, "example": "<PERSON>", "description": "User first name"}, "last_name": {"type": "string", "minLength": 2, "example": "<PERSON>", "description": "User last name"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "User email address"}, "phone": {"type": "string", "example": "+1234567890", "description": "User phone number"}, "employee_id": {"type": "string", "example": "EMP002", "description": "Employee ID"}, "store_id": {"$ref": "#/components/schemas/ObjectId", "description": "Store ID to assign user to"}, "business_id": {"$ref": "#/components/schemas/ObjectId", "description": "Business ID to assign user to"}, "role": {"type": "string", "enum": ["cashier", "manager", "supervisor"], "example": "cashier", "description": "User role"}, "password": {"type": "string", "minLength": 6, "example": "securePassword123", "description": "User password"}}, "required": ["first_name", "last_name", "email", "employee_id", "store_id", "role", "password"]}, "UpdateUsersRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "Updated Users", "description": "Users name"}, "description": {"type": "string", "example": "Updated description", "description": "Users description"}, "status": {"$ref": "#/components/schemas/Status"}}}, "UsersListResponse": {"allOf": [{"$ref": "#/components/schemas/PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Users"}}}}]}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for admin authentication"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key", "description": "API key for device authentication"}, "AccessKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "Access key for internal service communication"}}, "responses": {"UnauthorizedError": {"description": "Authentication information is missing or invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 401, "success": false, "message": "Unauthorized access"}}}}, "ForbiddenError": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 403, "success": false, "message": "Access forbidden"}}}}, "NotFoundError": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "success": false, "message": "Resource not found"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "Validation failed"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "success": false, "message": "Internal server error"}}}}}, "parameters": {"PageQuery": {"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, "ItemsPerPageQuery": {"name": "itemsPerPage", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, "SearchQuery": {"name": "search", "in": "query", "description": "Search query string", "required": false, "schema": {"type": "string"}}, "ObjectIdPath": {"name": "id", "in": "path", "description": "MongoDB ObjectId", "required": true, "schema": {"$ref": "#/components/schemas/ObjectId"}}}}, "tags": [{"name": "Health", "description": "Service health and status endpoints"}, {"name": "Authentication", "description": "Authentication and authorization endpoints"}], "paths": {"/users/v1/create-user": {"post": {"tags": ["Create User Management"], "summary": "Create create user", "description": "Create create user in the users service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUsersRequest", "type": "object"}}}}, "responses": {"200": {"description": "Create create user successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "201": {"description": "User created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/users/v1/get-all-users": {"get": {"tags": ["Get All Users Management"], "summary": "Get get all users", "description": "Retrieve get all users in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}, {"name": "storeId", "in": "query", "description": "Filter by store ID", "schema": {"$ref": "#/components/schemas/ObjectId"}}, {"name": "status", "in": "query", "description": "Filter by user status", "schema": {"$ref": "#/components/schemas/Status"}}], "responses": {"200": {"description": "Get get all users successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/users/v1/delete-user/:id": {"delete": {"tags": ["Delete User Management"], "summary": "Delete delete user", "description": "Delete delete user in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete delete user successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/users/v1/change-user-status/:id": {"put": {"tags": ["Change User Status Management"], "summary": "Update change user status", "description": "Update change user status in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update change user status successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/users/v1/transfer-user/:id": {"put": {"tags": ["Transfer User Management"], "summary": "Update transfer user", "description": "Update transfer user in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update transfer user successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/users/v1/update-user/:id": {"put": {"tags": ["Update User Management"], "summary": "Update update user", "description": "Update update user in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update user successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/users/v1/upload-csv": {"put": {"tags": ["Upload Csv Management"], "summary": "Update upload csv", "description": "Update upload csv in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update upload csv successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/users/v1/create-user-group": {"post": {"tags": ["Create User Group Management"], "summary": "Create create user group", "description": "Create create user group in the users service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create create user group successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/users/v1/delete-user-group/:id": {"delete": {"tags": ["Delete User Group Management"], "summary": "Delete delete user group", "description": "Delete delete user group in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete delete user group successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/users/v1/update-user-group/:id": {"put": {"tags": ["Update User Group Management"], "summary": "Update update user group", "description": "Update update user group in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update update user group successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/users/v1/get-group-details/:id": {"get": {"tags": ["Get Group Details Management"], "summary": "Get get group details", "description": "Retrieve get group details in the users service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get group details successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/users/v1/get-all-users-groups": {"get": {"tags": ["Get All Users Groups Management"], "summary": "Get get all users groups", "description": "Retrieve get all users groups in the users service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all users groups successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/users/v1/create-clerk-server": {"post": {"tags": ["Create Clerk Server Management"], "summary": "Create create clerk server", "description": "Create create clerk server in the users service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create create clerk server successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/users/v1/delete-user-from-device/:id": {"post": {"tags": ["Delete User From Device Management"], "summary": "Create delete user from device", "description": "Create delete user from device in the users service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create delete user from device successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}}}