{"openapi": "3.1.0", "info": {"title": "COMMON Service API", "description": "Common utilities and shared functionality service\n\nGenerated on: 2025-06-13T08:12:20.841Z", "version": "1.0.0", "contact": {"name": "PSP Development Team", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://pspservicesco.com/license"}}, "servers": [{"url": "http://localhost:4005/common", "description": "Local development server"}, {"url": "https://tms-dev.pspservicesco.com/common", "description": "Development server"}, {"url": "https://tms.pspservicesco.com/common", "description": "Production server"}], "components": {"schemas": {"SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200, "description": "HTTP status code"}, "success": {"type": "boolean", "example": true, "description": "Operation success status"}, "message": {"type": "string", "example": "Operation completed successfully", "description": "Response message"}, "data": {"type": "object", "description": "Response data payload"}}, "required": ["code", "success", "message"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400, "description": "HTTP status code"}, "success": {"type": "boolean", "example": false, "description": "Operation success status"}, "message": {"type": "string", "example": "Validation error", "description": "Error message"}, "error": {"type": "boolean", "example": true, "description": "Error flag"}}, "required": ["code", "success", "message"]}, "PaginationQuery": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number"}, "itemsPerPage": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Items per page"}, "search": {"type": "string", "description": "Search query"}, "sortBy": {"type": "string", "description": "Field to sort by"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "Sort order"}}}, "PaginatedResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Data retrieved successfully"}, "data": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object", "properties": {"currentPage": {"type": "integer", "example": 1}, "totalPages": {"type": "integer", "example": 10}, "totalItems": {"type": "integer", "example": 100}, "itemsPerPage": {"type": "integer", "example": 10}}}}}, "ObjectId": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$", "example": "507f1f77bcf86cd799439011", "description": "MongoDB ObjectId"}, "Timestamp": {"type": "string", "format": "date-time", "example": "2023-12-01T10:30:00.000Z", "description": "ISO 8601 timestamp"}, "Status": {"type": "string", "enum": ["Active", "Inactive", "Pending", "Suspended"], "description": "Entity status"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "example": "password123", "description": "User password"}, "token": {"type": "string", "example": "123456", "description": "2FA token"}}, "required": ["email", "password"]}, "AuthResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful!"}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "description": "JWT authentication token"}, "admin": {"type": "object", "description": "User information"}}}, "Common": {"type": "object", "properties": {"_id": {"$ref": "#/components/schemas/ObjectId"}, "name": {"type": "string", "example": "Sample Common", "description": "Common name"}, "status": {"$ref": "#/components/schemas/Status"}, "created_at": {"$ref": "#/components/schemas/Timestamp"}, "updated_at": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["name"]}, "CreateCommonRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "New Common", "description": "Common name"}, "description": {"type": "string", "example": "Common description", "description": "Common description"}}, "required": ["name"]}, "UpdateCommonRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "example": "Updated Common", "description": "Common name"}, "description": {"type": "string", "example": "Updated description", "description": "Common description"}, "status": {"$ref": "#/components/schemas/Status"}}}, "CommonListResponse": {"allOf": [{"$ref": "#/components/schemas/PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Common"}}}}]}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for admin authentication"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key", "description": "API key for device authentication"}, "AccessKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "Access key for internal service communication"}}, "responses": {"UnauthorizedError": {"description": "Authentication information is missing or invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 401, "success": false, "message": "Unauthorized access"}}}}, "ForbiddenError": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 403, "success": false, "message": "Access forbidden"}}}}, "NotFoundError": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "success": false, "message": "Resource not found"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "Validation failed"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "success": false, "message": "Internal server error"}}}}}, "parameters": {"PageQuery": {"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, "ItemsPerPageQuery": {"name": "itemsPerPage", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, "SearchQuery": {"name": "search", "in": "query", "description": "Search query string", "required": false, "schema": {"type": "string"}}, "ObjectIdPath": {"name": "id", "in": "path", "description": "MongoDB ObjectId", "required": true, "schema": {"$ref": "#/components/schemas/ObjectId"}}}}, "tags": [{"name": "Health", "description": "Service health and status endpoints"}, {"name": "Authentication", "description": "Authentication and authorization endpoints"}], "paths": {"/common/v1/remove-image": {"delete": {"tags": ["Remove Image Management"], "summary": "Delete remove image", "description": "Delete remove image in the common service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete remove image successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/common/v1/send-socket-notification": {"post": {"tags": ["Send Socket Notification Management"], "summary": "Create send socket notification", "description": "Create send socket notification in the common service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create send socket notification successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/common/v1/get-connected-terminals": {"post": {"tags": ["Get Connected Terminals Management"], "summary": "Create get connected terminals", "description": "Create get connected terminals in the common service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create get connected terminals successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/common/v1/send-device-data": {"post": {"tags": ["Send Device Data Management"], "summary": "Create send device data", "description": "Create send device data in the common service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create send device data successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/common/v1/get-device-data": {"get": {"tags": ["Get Device Data Management"], "summary": "Get get device data", "description": "Retrieve get device data in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get device data successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/push-data-queue": {"post": {"tags": ["Push Data Queue Management"], "summary": "Create push data queue", "description": "Create push data queue in the common service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create push data queue successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/common/v1/device-log": {"post": {"tags": ["Device Log Management"], "summary": "Create device log", "description": "Create device log in the common service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create device log successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/common/v1/device-logs": {"get": {"tags": ["Device Logs Management"], "summary": "Get device logs", "description": "Retrieve device logs in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get device logs successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/all-in-one-search": {"get": {"tags": ["All In One Search Management"], "summary": "Get all in one search", "description": "Retrieve all in one search in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get all in one search successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/card-data": {"post": {"tags": ["Card Data Management"], "summary": "Create card data", "description": "Create card data in the common service", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create card data successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}}}}, "/common/v1/card-data/:id": {"get": {"tags": ["Card Data Management"], "summary": "Get card data", "description": "Retrieve card data in the common service", "responses": {"200": {"description": "Get card data successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/common/v1/get-all-cards-data": {"get": {"tags": ["Get All Cards Data Management"], "summary": "Get get all cards data", "description": "Retrieve get all cards data in the common service", "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all cards data successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/common/v1/create-certificate": {"post": {"tags": ["Create Certificate Management"], "summary": "Create create certificate", "description": "Create create certificate in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create create certificate successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/get-all-certificates": {"get": {"tags": ["Get All Certificates Management"], "summary": "Get get all certificates", "description": "Retrieve get all certificates in the common service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageQuery"}, {"$ref": "#/components/parameters/ItemsPerPageQuery"}, {"$ref": "#/components/parameters/SearchQuery"}], "responses": {"200": {"description": "Get get all certificates successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/get-cert/:name": {"get": {"tags": ["Get Cert Management"], "summary": "Get get cert", "description": "Retrieve get cert in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get cert successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/capk-file": {"get": {"tags": ["Capk File Management"], "summary": "Get capk file", "description": "Retrieve capk file in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get capk file successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/redis/logs": {"get": {"tags": ["Redis Management"], "summary": "Get redis logs", "description": "Retrieve redis logs in the common service", "responses": {"200": {"description": "Get redis logs successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/common/v1/redis/clear": {"get": {"tags": ["Redis Management"], "summary": "Get redis clear", "description": "Retrieve redis clear in the common service", "responses": {"200": {"description": "Get redis clear successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/common/v1/redis-ping": {"get": {"tags": ["Redis Ping Management"], "summary": "Get redis ping", "description": "Retrieve redis ping in the common service", "responses": {"200": {"description": "Get redis ping successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/common/v1/webhook": {"post": {"tags": ["Webhook Management"], "summary": "Create webhook", "description": "Create webhook in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create webhook successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/points-data/:userId": {"get": {"tags": ["Points Data Management"], "summary": "Get points data", "description": "Retrieve points data in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get points data successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/verify-pin": {"post": {"tags": ["Verify Pin Management"], "summary": "Create verify pin", "description": "Create verify pin in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create verify pin successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/send-point-request": {"post": {"tags": ["Send Point Request Management"], "summary": "Create send point request", "description": "Create send point request in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create send point request successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/complete-point-request": {"post": {"tags": ["Complete Point Request Management"], "summary": "Create complete point request", "description": "Create complete point request in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create complete point request successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/search-validate-tag": {"get": {"tags": ["Search Validate Tag Management"], "summary": "Get search validate tag", "description": "Retrieve search validate tag in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get search validate tag successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/tag": {"post": {"tags": ["Tag Management"], "summary": "Create tag", "description": "Create tag in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create tag successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/tag/:id": {"delete": {"tags": ["Tag Management"], "summary": "Delete tag", "description": "Delete tag in the common service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete tag successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "put": {"tags": ["Tag Management"], "summary": "Update tag", "description": "Update tag in the common service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update tag successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/common/v1/tags": {"get": {"tags": ["Tags Management"], "summary": "Get tags", "description": "Retrieve tags in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get tags successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/tag-template": {"post": {"tags": ["Tag Template Management"], "summary": "Create tag template", "description": "Create tag template in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create tag template successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "get": {"tags": ["Tag Template Management"], "summary": "Get tag template", "description": "Retrieve tag template in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get tag template successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/tag-template/:id": {"put": {"tags": ["Tag Template Management"], "summary": "Update tag template", "description": "Update tag template in the common service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Update tag template successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "delete": {"tags": ["Tag Template Management"], "summary": "Delete tag template", "description": "Delete tag template in the common service", "security": [{"BearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/ObjectIdPath"}], "responses": {"200": {"description": "Delete tag template successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/common/v1/get-location/:id": {"get": {"tags": ["Get Location Management"], "summary": "Get get location", "description": "Retrieve get location in the common service", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Get get location successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/publish-to-mqtt": {"post": {"tags": ["Publish To Mqtt Management"], "summary": "Create publish to mqtt", "description": "Create publish to mqtt in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create publish to mqtt successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/upload-transaction": {"post": {"tags": ["Upload Transaction Management"], "summary": "Create upload transaction", "description": "Create upload transaction in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create upload transaction successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/common/v1/upload-settlements": {"post": {"tags": ["Upload Settlements Management"], "summary": "Create upload settlements", "description": "Create upload settlements in the common service", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Create upload settlements successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}}}