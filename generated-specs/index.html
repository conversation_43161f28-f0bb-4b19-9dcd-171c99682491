<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSP Backend - OpenAPI Specifications</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        .service { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
        .service h3 { margin-top: 0; color: #34495e; }
        .stats { display: flex; gap: 20px; margin: 10px 0; }
        .stat { background: #3498db; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; }
        .links { margin-top: 15px; }
        .links a { display: inline-block; margin-right: 10px; padding: 8px 15px; background: #2ecc71; color: white; text-decoration: none; border-radius: 3px; font-size: 14px; }
        .links a:hover { background: #27ae60; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #7f8c8d; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PSP Backend - OpenAPI 3.1 Specifications</h1>
        <p>Generated on: <strong>2025-06-13T08:12:20.871Z</strong></p>
        
        
        <div class="service">
            <h3>📋 ADMIN Service</h3>
            <div class="stats">
                <span class="stat">21 Endpoints</span>
                <span class="stat">18 Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="admin-openapi.json" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        
        <div class="service">
            <h3>📋 TERMINAL Service</h3>
            <div class="stats">
                <span class="stat">32 Endpoints</span>
                <span class="stat">13 Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="terminal-openapi.json" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        
        <div class="service">
            <h3>📋 STORE Service</h3>
            <div class="stats">
                <span class="stat">13 Endpoints</span>
                <span class="stat">13 Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="store-openapi.json" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        
        <div class="service">
            <h3>📋 BUSINESS Service</h3>
            <div class="stats">
                <span class="stat">19 Endpoints</span>
                <span class="stat">13 Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="business-openapi.json" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        
        <div class="service">
            <h3>📋 USERS Service</h3>
            <div class="stats">
                <span class="stat">14 Endpoints</span>
                <span class="stat">13 Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="users-openapi.json" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        
        <div class="service">
            <h3>📋 COMMON Service</h3>
            <div class="stats">
                <span class="stat">34 Endpoints</span>
                <span class="stat">13 Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="common-openapi.json" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        
        <div class="service">
            <h3>📋 CONFIG Service</h3>
            <div class="stats">
                <span class="stat">9 Endpoints</span>
                <span class="stat">13 Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="config-openapi.json" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        
        <div class="service">
            <h3>📋 AMEX Service</h3>
            <div class="stats">
                <span class="stat">2 Endpoints</span>
                <span class="stat">13 Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="amex-openapi.json" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        
        
        <div class="footer">
            <p>💡 <strong>How to use these files:</strong></p>
            <p>• Open JSON files in VS Code with OpenAPI extension</p>
            <p>• Import into Postman or Insomnia for API testing</p>
            <p>• Use online editors like Swagger Editor or Redoc</p>
            <p>• Generate client SDKs using OpenAPI Generator</p>
        </div>
    </div>
</body>
</html>