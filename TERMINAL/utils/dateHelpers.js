const { format } = require('date-fns');

const getFormattedDate = date => {
    if (!date) return format(new Date(), 'dd/MM/yyyy');
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'dd/MM/yyyy');
};

const getFormattedTime = date => {
    if (!date) return format(new Date(), 'hh:mm:ss a');
    try {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return format(dateObj, 'hh:mm:ss a');
    } catch (error) {
        console.error('Error formatting time:', error);
        return format(new Date(), 'hh:mm:ss a');
    }
};

const getTimezoneStr = () => {
    const date = new Date().toString();
    const timezone = date.substring(date.indexOf('(') + 1, date.indexOf(')'));
    return timezone;
};

module.exports = {
    getFormattedDate,
    getFormattedTime,
    getTimezoneStr,
};
