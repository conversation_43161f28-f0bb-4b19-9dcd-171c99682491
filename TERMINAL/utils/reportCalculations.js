const calculateTransactionTotals = async (transactions, tmsSettings) => {
  const approvedSales = transactions.filter(
    t => t.sale_amount > 0 && t.status === 'Sale' && t.transactionStatus === 'Approved',
  );

  const tips = approvedSales.filter(t => t.tip_amount > 0);
  const refunds = transactions.filter(
    t => t.sale_amount > 0 && t.status === 'Refunded' && t.transactionStatus === 'Approved',
  );
  const reversals = transactions.filter(
    t => t.sale_amount > 0 && t.status === 'Reversed' && t.transactionStatus === 'Approved',
  );

  const salesTotal = approvedSales.reduce(
    (sum, t) => sum + parseFloat(t.sale_amount) + parseFloat(t.surcharge_amount || 0),
    0,
  );
  const tipsTotal = tips.reduce((sum, t) => sum + parseFloat(t.tip_amount), 0);
  const refundsTotal = refunds.reduce((sum, t) => sum + parseFloat(t.sale_amount), 0);
  const reversalsTotal = reversals.reduce((sum, t) => sum + parseFloat(t.sale_amount), 0);

  const transactionTotals = {
    'No. of Transactions': approvedSales.length,
    Sales: `$${(salesTotal - reversalsTotal - refundsTotal).toFixed(2)}`,
    Tips: `$${tipsTotal.toFixed(2)}`,
    'No. of Refunds': refunds.length,
    Refunds: `$${refundsTotal.toFixed(2)}`,
    'No. of Reversals': reversals.length,
    'Total Reversals': `$${reversalsTotal.toFixed(2)}`,
  };

  // Add PreAuth data if enabled
  if (tmsSettings?.terminal_configuration?.pre_auth?.value) {
    const preAuthSales = transactions.filter(t => t.status === 'PreAuth Sale');
    const preAuthCompletions = transactions.filter(t => t.status === 'PreAuth Completion');

    transactionTotals['No. of PreAuth Sale'] = preAuthSales.length;
    transactionTotals['Total PreAuth Sale'] = `$${preAuthSales
      .reduce((sum, t) => sum + parseFloat(t.sale_amount), 0)
      .toFixed(2)}`;
    transactionTotals['No. of PreAuth Completion'] = preAuthCompletions.length;
    transactionTotals['Total PreAuth Completion'] = `$${preAuthCompletions
      .reduce((sum, t) => sum + parseFloat(t.sale_amount), 0)
      .toFixed(2)}`;
  }
  const getMaskedCardNumber = cardNumber => {
    if (!cardNumber) return '';
    const lastFour = cardNumber.slice(-4);
    return `************${lastFour}`;
  };

  return [
    {
      title: ['Transaction Totals'],
      data: [transactionTotals],
    },
    {
      title: ['Card Totals'],
      data: await calculateCardBrandsTotals(transactions, tmsSettings),
    },
    {
      title: ['Individual Transactions'],
      data: transactions
        .filter(
          t =>
            !tmsSettings?.terminal_configuration?.pre_auth?.value ||
            (t.status !== 'PreAuth Sale' && t.status !== 'PreAuth Completion'),
        )
        .map(d => {
          const totalAmount = (d?.sale_amount + d?.tip_amount + d?.surcharge_amount)?.toFixed(2);
          return {
            'Reference #': d?.reference_id,
            Type: d?.status,
            ...(d?.card_type && {
              [d?.card_type]: getMaskedCardNumber(d?.card_number),
            }),
            Date: d?.date,
            Time: d?.time,
            'Total Amount': `$${totalAmount} - ${d?.transactionStatus}`,
          };
        }),
    },
  ];
};

const calculateCardBrandsTotals = async (transactions, tmsSettings) => {
  const approvedSales = transactions.filter(
    t => t.sale_amount > 0 && t.status === 'Sale' && t.transactionStatus === 'Approved',
  );

  const cardTypes = ['interac', 'mastercard', 'visa', 'amex', 'jcb', 'psp gift card'];
  const cardData = [];

  cardTypes.forEach(cardType => {
    const cardSales = approvedSales.filter(t => t.card_type?.toLowerCase().includes(cardType.toLowerCase()));
    const cardRefunds = transactions.filter(
      t =>
        t.sale_amount > 0 &&
        t.status === 'Refunded' &&
        t.transactionStatus === 'Approved' &&
        t.card_type?.toLowerCase().includes(cardType.toLowerCase()),
    );
    const cardReversals = transactions.filter(
      t =>
        t.sale_amount > 0 &&
        t.status === 'Reversed' &&
        t.transactionStatus === 'Approved' &&
        t.card_type?.toLowerCase().includes(cardType.toLowerCase()),
    );

    const salesTotal = cardSales.reduce(
      (sum, t) => sum + parseFloat(t.sale_amount) + parseFloat(t.surcharge_amount || 0),
      0,
    );
    const tipsTotal = cardSales.reduce((sum, t) => sum + parseFloat(t.tip_amount || 0), 0);
    const refundsTotal = cardRefunds.reduce((sum, t) => sum + parseFloat(t.sale_amount), 0);
    const reversalsTotal = cardReversals.reduce((sum, t) => sum + parseFloat(t.sale_amount), 0);

    let preAuthCompletions = [];
    let preAuthCompletionsTotal = 0;

    if (tmsSettings?.terminal_configuration?.pre_auth?.value) {
      preAuthCompletions = transactions.filter(
        t => t.status === 'PreAuth Completion' && t.card_type?.toLowerCase().includes(cardType.toLowerCase()),
      );
      preAuthCompletionsTotal = preAuthCompletions.reduce((sum, t) => sum + parseFloat(t.sale_amount), 0);
    }

    if (cardSales.length > 0 || cardRefunds.length > 0 || cardReversals.length > 0 || preAuthCompletions.length > 0) {
      const cardName = cardType.charAt(0).toUpperCase() + cardType.slice(1);

      let cardTotalsData = [
        {
          [cardName]: 'Sales',
          Count: cardSales.length,
          Currency: '$',
          Totals: salesTotal.toFixed(2),
        },
        {
          [cardName]: 'Returns',
          Count: cardRefunds.length,
          Currency: '$',
          Totals: refundsTotal.toFixed(2),
        },
        {
          [cardName]: 'Reversed',
          Count: cardReversals.length,
          Currency: '$',
          Totals: reversalsTotal.toFixed(2),
        },
        {
          [cardName]: 'Tips',
          Count: cardSales.filter(t => t.tip_amount > 0).length,
          Currency: '$',
          Totals: tipsTotal.toFixed(2),
        },
      ];

      // Add PreAuth entries if enabled and have transactions
      if (tmsSettings?.terminal_configuration?.pre_auth?.value) {
        if (preAuthCompletions.length > 0) {
          cardTotalsData.push({
            [cardName]: 'PreAuth (C)',
            Count: preAuthCompletions.length,
            Currency: '$',
            Totals: preAuthCompletionsTotal.toFixed(2),
          });
        }
      }

      cardTotalsData.push({
        [cardName]: 'Sub Total',
        Count: '',
        Currency: '',
        Totals: (salesTotal + tipsTotal - refundsTotal - reversalsTotal).toFixed(2),
      });

      cardData.push({
        title: [`${cardName} Totals`],
        data: cardTotalsData,
      });
    }
  });

  return cardData;
};

const calculateTipTotals = async (transactions, clerkId = null, cardBrands = false) => {
  let approvedSales = transactions.filter(
    t => t.sale_amount > 0 && t.status === 'Sale' && t.transactionStatus === 'Approved',
  );

  if (clerkId) {
    approvedSales = approvedSales.filter(t => t.clerk_id === clerkId);
  }

  const tips = approvedSales.filter(t => t.tip_amount > 0);
  const totalTips = tips.reduce((sum, t) => sum + parseFloat(t.tip_amount), 0);

  const cardTypes = ['interac', 'visa', 'mastercard', 'amex', 'psp gift card'];
  const tipsByCard = {};

  cardTypes.forEach(cardType => {
    const cardTips = tips.filter(t => t.card_type?.toLowerCase().includes(cardType.toLowerCase()));
    const cardTipsTotal = cardTips.reduce((sum, t) => sum + parseFloat(t.tip_amount), 0);

    if (cardTips.length > 0) {
      tipsByCard[cardType] = {
        count: cardTips.length,
        total: cardTipsTotal.toFixed(2),
      };
    }
  });

  if (cardBrands) {
    const cardBrandTipData = [];

    const cardBrandTipTotals = cardTypes.map(cardType => {
      const cardTips = tips.filter(t => t.card_type?.toLowerCase().includes(cardType.toLowerCase()));
      const tipTotal = cardTips.reduce((sum, t) => sum + parseFloat(t.tip_amount), 0);

      const cardName =
        cardType === 'mastercard'
          ? 'MasterCard'
          : cardType === 'amex'
            ? 'American Express'
            : cardType === 'psp gift card'
              ? 'GiftCard'
              : cardType.charAt(0).toUpperCase() + cardType.slice(1);

      return {
        CardType: cardName,
        Count: cardTips.length,
        $: '$',
        Totals: tipTotal.toFixed(2),
      };
    });

    cardBrandTipData.push({
      title: ['Card Brand Totals'],
      data: [
        {
          CardType: 'Grand Total',
          Count: tips.length,
          $: '$',
          Totals: totalTips.toFixed(2),
        },
        ...cardBrandTipTotals,
      ],
    });

    cardTypes.forEach(cardType => {
      const cardTips = tips.filter(t => t.card_type?.toLowerCase().includes(cardType.toLowerCase()));

      const tipTotal = cardTips.reduce((sum, t) => sum + parseFloat(t.tip_amount), 0);

      const cardName =
        cardType === 'mastercard'
          ? 'MasterCard'
          : cardType === 'amex'
            ? 'American Express'
            : cardType === 'psp gift card'
              ? 'GiftCard'
              : cardType.charAt(0).toUpperCase() + cardType.slice(1);

      cardBrandTipData.push({
        title: [`${cardName} Totals`],
        data: [
          {
            [`${cardName}`]: 'Total Tips',
            Count: cardTips.length,
            $: '$',
            Totals: tipTotal.toFixed(2),
          },
        ],
      });
    });

    return cardBrandTipData;
  }

  return [
    {
      title: ['Grand Tip Total'],
      tips: [
        {
          'Card Type': '',
          Transaction: 'Sale',
          'Tip Count': tips.length,
          Currency: '$',
          Total: totalTips.toFixed(2),
        },
      ],
    },
    {
      title: ['Debit Tip Total'],
      tips: Object.entries(tipsByCard)
        .filter(([cardType]) => ['interac'].includes(cardType))
        .map(([cardType, data]) => ({
          'Card Type': cardType.charAt(0).toUpperCase() + cardType.slice(1),
          Transaction: 'Sale',
          'Tip Count': data.count,
          Currency: '$',
          Total: data.total,
        })),
    },
    {
      title: ['Credit Tip Total'],
      tips: Object.entries(tipsByCard)
        .filter(([cardType]) => !['interac'].includes(cardType))
        .map(([cardType, data]) => ({
          'Card Type': cardType.charAt(0).toUpperCase() + cardType.slice(1),
          Transaction: 'Sale',
          'Tip Count': data.count,
          Currency: '$',
          Total: data.total,
        })),
    },
  ];
};

const calculateClerkServerTotals = async (transactions, onlyIndividual = false, clerkId = null) => {
  let approvedSales = transactions.filter(
    t => t.sale_amount > 0 && t.status === 'Sale' && t.transactionStatus === 'Approved',
  );

  let refunds = transactions.filter(
    t => t.sale_amount > 0 && t.status === 'Refunded' && t.transactionStatus === 'Approved',
  );

  if (onlyIndividual && clerkId) {
    approvedSales = approvedSales.filter(t => t.clerk_id === clerkId);
    refunds = refunds.filter(t => t.clerk_id === clerkId);
  }

  const clerkData = {};

  approvedSales.forEach(transaction => {
    const clerkId = transaction.clerk_id || 'Unknown';
    if (!clerkData[clerkId]) {
      clerkData[clerkId] = {
        sales: 0,
        tips: 0,
        transactions: 0,
        refunds: 0,
        refundCount: 0,
      };
    }

    clerkData[clerkId].sales += parseFloat(transaction.sale_amount) + parseFloat(transaction.surcharge_amount || 0);
    clerkData[clerkId].tips += parseFloat(transaction.tip_amount || 0);
    clerkData[clerkId].transactions += 1;
  });

  refunds.forEach(transaction => {
    const clerkId = transaction.clerk_id || 'Unknown';
    if (!clerkData[clerkId]) {
      clerkData[clerkId] = {
        sales: 0,
        tips: 0,
        transactions: 0,
        refunds: 0,
        refundCount: 0,
      };
    }

    clerkData[clerkId].refunds += parseFloat(transaction.sale_amount);
    clerkData[clerkId].refundCount += 1;
  });

  const totalSales = Object.values(clerkData).reduce((sum, clerk) => sum + clerk.sales, 0);
  const totalTips = Object.values(clerkData).reduce((sum, clerk) => sum + clerk.tips, 0);
  const totalTransactions = Object.values(clerkData).reduce((sum, clerk) => sum + clerk.transactions, 0);
  const totalRefunds = Object.values(clerkData).reduce((sum, clerk) => sum + clerk.refunds, 0);
  const totalRefundCount = Object.values(clerkData).reduce((sum, clerk) => sum + clerk.refundCount, 0);

  const individualClerkTotals = Object.entries(clerkData).map(([clerkId, data]) => ({
    'Clerk/Server ID': clerkId,
    'Total Tips': `$${data.tips.toFixed(2)}`,
    'Total Sales': `$${data.sales.toFixed(2)}`,
    'Total Refunds': `$${data.refunds.toFixed(2)}`,
  }));
  let response = [];
  if (onlyIndividual) {
    response = [
      {
        title: ['Individual Clerk/Server Totals'],
        data: individualClerkTotals.map(item => ({
          'Clerk/Server ID': item['Clerk/Server ID'],
          'Total Tips': item['Total Tips'],
          'Total Sales': item['Total Sales'],
          'Total Refunds': item['Total Refunds'],
          'Grand Total': `$${(
            parseFloat(item['Total Sales'].replace(/[$,]/g, '')) +
            parseFloat(item['Total Tips'].replace(/[$,]/g, '')) -
            parseFloat(item['Total Refunds'].replace(/[$,]/g, ''))
          ).toFixed(2)}`,
        })),
      },
    ];
  } else {
    response = [
      {
        title: ['Clerk/Server Totals'],
        data: [
          {
            'No. of Clerk Servers': Object.keys(clerkData).length,
            'Total Sales': `$${totalSales.toFixed(2)}`,
            'Total Tips': `$${totalTips.toFixed(2)}`,
            'No. of Transactions': totalTransactions,
            'Total Refunds': `$${totalRefunds.toFixed(2)}`,
            'No. of Refunds': totalRefundCount,
          },
        ],
      },
      {
        title: ['Individual Clerk/Server Totals'],
        data: individualClerkTotals,
      },
    ];
  }
  return response;
};

module.exports = {
  calculateTransactionTotals,
  calculateCardBrandsTotals,
  calculateTipTotals,
  calculateClerkServerTotals,
};
