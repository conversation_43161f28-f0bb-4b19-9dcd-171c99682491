module.exports = data => {
  return `<!DOCTYPE html>
<html
  lang="en"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:v="urn:schemas-microsoft-com:vml"
>
  <head>
    <title>${data.header_2}</title>
    <meta property="og:title" content="Welcome to ${data.header_2}." />
    <meta name="twitter:title" content="Welcome to ${data.header_2}." />
    <meta name="x-apple-disable-message-reformatting" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!--[if mso]>
      <xml>
        <o:OfficeDocumentSettings>
          <o:PixelsPerInch>96</o:PixelsPerInch>
          <o:AllowPNG />
        </o:OfficeDocumentSettings>
      </xml>
    <![endif]-->
    <!--[if !mso]><!-->
    <link
      href="https://fonts.googleapis.com/css?family=Open+Sans"
      rel="stylesheet"
      type="text/css"
    />
    <style>
      * {
        box-sizing: border-box;
      }

      body {
        margin: 0;
        padding: 0;
        min-width: 375px;
        font: 500 16px/22px "Open Sans", "Arial", "Helvetica Neue", sans-serif;
        color: #24383f;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: #fff;
      }

      a[x-apple-data-detectors] {
        color: inherit !important;
        text-decoration: inherit !important;
      }

      #MessageViewBody a {
        color: inherit;
        text-decoration: none;
      }

      p {
        line-height: inherit;
      }

      .info-col {
        width: 100%;
      }

      .info-col .price,
      .info-col .date-time,
      .info-col .text {
        display: block;
        font-weight: 500;
      }

      .info-col .price {
        font-size: 18px;
        font-weight: 600;
        color: #c8102e;
      }

    </style>
  </head>

  <body
    style="
      font-family:'Open Sans';
      background-color: #ffffff;
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: none;
      text-size-adjust: none;
    "
  >
    <table border="0" cellpadding="0" cellspacing="0" class="nl-container" role="presentation" 
    style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #FFFFFF; color: #000000; margin: 0 auto; width: 800px;" width="800">
    <tbody>
      <tr>
        <td style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400;" width="100%">
          <table border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 15px 0;" width="100%">
            <tr>
           
              <td style="width: 50%; padding: 5px;">
                 <div class="logo" style="width: 150px;">
                  <img style="display: block; width: 100%; height: auto;" cross  src=${data.header_1} alt="logo">
                </div>
              </td>
            
            
              <td style="width: 50%; text-align: right; padding: 5px;">
                <div class="info-col">
                  <span class="price">${data?.totalAmount}</span>
                  <span class="date-time">${data?.d}, ${data?.t}</span>
                ${data.header_2 && `<span class="text">${data.header_2}</span>`}
                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td class="banner" style="width: 100%; text-align: center; padding: 15px 0; background-color: #c8102e;;">
          <strong class="heading" 
              style="
                display: block;
                margin: 0;
                color: #fff;
                font-size: 22px;
                line-height: 150%;
                font-family:'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
                text-align: center;
                direction: ltr;
                font-weight: 500;
                letter-spacing: normal;
                margin-top: 0;
                margin-bottom: 0;
              ">
            Your Payment has been Apporved
          </strong>
        </td>
      </tr>
      <tr>
        <td style="width: 100%; align-items: center; border-bottom: 1px solid #c7c7c7;">
          <table border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 15px 0; width: 600px; margin: 0 auto;" width="100%">
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Terminal ID:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.terminal_id}</span>
              </td>
            </tr>
         
             
              ${
                data?.clerk_id
                  ? `
                     <tr>
                   <td class="column" style="width: 50%; padding: 5px;">
                      <strong class="title">Clerk ID:</strong>
                    </td>
                  <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                    <span class="value">${data?.clerk_id}</span>
                  </td>
                    </tr>
                  `
                  : ''
              }

          
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Date:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.d}, ${data?.t}</span>
              </td>
            </tr>
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Invoice#</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.invoice_number}</span>
              </td>
            </tr>
            <tr>
              <td colspan="2" class="banner" style="display:none; width: 100%; text-align: center; padding: 10px 0; background-color: #c8102e;" width='100%'>
                <strong class="heading" 
                    style="
                      display: block;
                      margin: 0;
                      color: #fff;
                      font-size: 22px;
                      line-height: 150%;
                      font-family:'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
                      text-align: center;
                      direction: ltr;
                      font-weight: 500;
                      letter-spacing: normal;
                      margin-top: 0;
                      margin-bottom: 0;
                    ">
                  Sales
                </strong>
              </td>
            </tr>
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Entry Method:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
              
                ${
                  data?.entry_mode === 'C'
                    ? ' <span class="value">CHIP</span>'
                    : data?.entry_mode === 'T'
                    ? ' <span class="value">TAP</span>'
                    : data?.entry_mode === 'S'
                    ? '<span class="value">SWIPE</span>'
                    : data?.entry_mode === 'MANUAL'
                    ? '<span class="value">MANUAL</span>'
                    : ''
                }
              </td>
            </tr>
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Account Type:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.account_type}</span>
              </td>
            </tr>
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Amount:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.sale_amount}</span>
              </td>
            </tr>
           ${
             data?.tip_amount
               ? `
           <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Tip:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.tip_amount}</span>
              </td>
            </tr>
            `
               : ''
           }
           ${
             data?.surcharge_amount
               ? ` <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Surcharge:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.surcharge_amount}</span>
              </td>
            </tr>`
               : ''
           }
          </table>
        </td>
      </tr>
      <tr>
        <td style="width: 100%; align-items: center; border-bottom: 1px solid #c7c7c7;">
          <table border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 15px 0; width: 600px; margin: 0 auto;" width="100%">
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Total:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.totalAmount}</span>
              </td>
            </tr>
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Application Label:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.application_label}</span>
              </td>
            </tr>
            <tr>
              <td class="column" style="width: 50%; padding: 5px;">
                <strong class="title">Application Pref Name:</strong>
              </td>
              <td class="column" style="width: 50%; text-align: right; padding: 5px;">
                <span class="value">${data?.application_pref_name}</span>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td style="width: 100%; text-align: center; padding: 0;">
          <table border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 15px 0;" width="100%">
            <tr>
              <td style="width: 100%; text-align: center; padding: 5px;">
                <div class="info-col">
                  <span class="text">${data?.transaction_code}</span>
                  <span class="text">${data?.receipt_holder}</span>
                  ${data?.footer_1 ? `<span class="text">${data.footer_1}</span>` : ''}
                  ${data?.footer_2 ? `<span class="text">${data.footer_2}</span>` : ''}
                  ${data?.footer_3 ? `<span class="text">${data.footer_3}</span>` : ''}
                  ${data?.footer_4 ? `<span class="text">${data.footer_4}</span>` : ''}
                  ${data?.footer_5 ? `<span class="text">${data.footer_5}</span>` : ''}
                  ${data?.footer_6 ? `<span class="text">${data.footer_6}</span>` : ''}
                  
                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </tbody>
  </body>
</html>
`;
};
