module.exports = data => {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    html,
    body {
      padding: 0;
      margin: 0;
      font-size: 16px;
      line-height: 19px;
    }

    * {
      box-sizing: border-box;
    }

    a {
      color: #0070f3;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    img {
      display: block;
      max-width: 100%;
      height: auto;
    }

    #wrapper {
      overflow: hidden;
      position: relative;
      margin-bottom: 30px;
    }

    .content-wrap {
      max-width: 500px;
      padding: 15px 10px;
      margin: 0 auto;
    }

    .page-header {
      text-align: center;
      margin: 0 0 15px;
    }

    .heading {
      display: block;
      font-size: 24px;
      line-height: 28px;
      text-transform: capitalize;
      margin: 0 0 10px;
      text-align: center;
    }

    .subtitle {
      display: block;
      font-size: 18px;
      line-height: 22px;
      text-transform: capitalize;
      margin: 0 0 5px;
      text-align: center;
    }

    .page-logo {
      width: 150px;
      margin: 0 auto 15px;
      text-transform: uppercase;
    }

    .merchant-info {
      width: 100%;
      position: relative;
      margin-bottom: 60px;
    }

    .info-list {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .info-list li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 3px 0;
      font-weight: 600;
    }

    .info-list .title {
      display: block;
    }

    .info-list.has-border {
      padding: 10px;
      border-radius: 10px;
      border: 1px solid #000;
      margin-bottom: 10px;
    }

    table.info-table {
      width: 100%;
      text-align: left;
    }

    table.info-table th,
    table.info-table td {
      padding: 3px;
    }

    table.info-table th:last-child,
    table.info-table td:last-child {
      text-align: right;
    }

    table tr:last-child td {
      padding-top: 5px;
      border-top: 1px dashed #000;
    }
  </style>
</head>
<body>
  <div id="wrapper">
    ${data
      .map(
        item => `
        <div class="content-wrap">
          <div class="page-header">
            ${
              item.logo &&
              `<div class="page-logo"> 
                <img style="height: 100px; width: 100%; object-fit: contain; display: block;" src="${item.logo}" alt="${item.logo}">
              </div>`
            }
            <strong class="heading">${item?.store_name}</strong>
          </div>
          <div class="merchant-info">
            <ul class="info-list">
              <li>
                <span class="title">Merchant ID:</span>
                <span class="value">${item?.merchant_id}</span>
              </li>
              <li>
                <span class="title">Terminal ID:</span>
                <span class="value">${item?.terminal_id}</span>
              </li>
              <li>
                <span class="title">Start Date:</span>
                <span class="value">${item?.start_date}</span>
              </li>
              <li>
                <span class="title">Start Time:</span>
                <span class="value">${item?.start_time}</span>
              </li>
              <li>
                <span class="title">End Date:</span>
                <span class="value">${item?.end_date}</span>
              </li>
              <li>
                <span class="title">End Time:</span>
                <span class="value">${item?.end_time}</span>
              </li>
              <li>
                <span class="title">Last Transaction:</span>
                <span class="value">${item?.last_transaction}</span>
              </li>
            </ul>
          </div>
          <div class="merchant-info">
            <strong class="heading">Transaction Totals</strong>
            <ul class="info-list has-border">
              ${Object.entries(item.tr_total)
                .map(
                  ([key, value]) => `
                    <li>
                      <span class="title">${key}</span>
                      <span class="value">${value}</span>
                    </li>`,
                )
                .join('')}  
            </ul>
          </div>
          <div class="merchant-info">
            <strong class="heading">Card Totals</strong>
            ${item.card_total
              .map(
                itm => `
                  <strong class="subtitle">${itm.title}</strong>
                  <div class="info-list has-border">
                    <table class="info-table">
                      <thead>
                        <tr>
                          <th><span class="text">${itm.title.split(' ')[0]}</span></th>
                          <th><span class="text">Count</span></th>
                          <th><span class="text"></span></th>
                          <th><span class="text">Total</span></th>
                        </tr>
                      </thead>
                      <tbody>
                        ${itm.data
                          .map(
                            item => `
                              <tr>
                                <td><span class="text">${item[0]}</span></td>
                                <td><span class="text">${item[1]}</span></td>
                                <td><span class="text">$</span></td>
                                <td><span class="text">${item[3]}</span></td>
                              </tr>`,
                          )
                          .join('')}
                      </tbody>
                    </table>
                  </div>`,
              )
              .join('')}
          </div>
        </div>`,
      )
      .join('')}
  </div>
</body>
</html>`;
};
