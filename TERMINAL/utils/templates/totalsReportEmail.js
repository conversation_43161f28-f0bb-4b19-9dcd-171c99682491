module.exports = data => {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    html, body {
      padding: 0;
      margin: 0;
      font-size: 16px;
      line-height: 19px;
    }

    * {
      box-sizing: border-box;
    }

    a {
      color: #0070f3;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    img {
      display: block;
      max-width: 100%;
      height: auto;
    }

    #wrapper {
      overflow: hidden;
      position: relative;
      margin-bottom: 30px;
    }

    .content-wrap {
      max-width: 500px;
      padding: 15px 10px;
      margin: 0 auto;
    }

    .page-header {
      text-align: center;
      margin: 0 0 15px;
    }

    .heading, .subtitle {
      display: block;
      margin: 0 0 10px;
      text-align: center;
    }

    .page-logo {
      width: 150px;
      margin: 0 auto 15px;
      text-transform: uppercase;
    }

    .merchant-info {
      width: 100%;
      position: relative;
      margin-bottom: 60px;
    }

    .info-list {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .info-list li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 3px 0;
      font-weight: 600;
    }

    .info-list .title {
      display: block;
    }

    .info-list.has-border {
      padding: 10px;
      border-radius: 10px;
      border: 1px solid #000;
      margin-bottom: 10px;
    }

    table.info-table {
      width: 100%;
      text-align: left;
    }

    table.info-table th, table.info-table td {
      padding: 3px;
    }

    table.info-table th:last-child, table.info-table td:last-child {
      text-align: right;
    }

    table tr:last-child td {
      padding-top: 5px;
      border-top: 1px dashed #000;
    }
  </style>
</head>
<body>
  <div id="wrapper">
    <div class="content-wrap">
      <div class="page-header">
        ${
          data[0].logo
            ? `
          <div class="page-logo"> 
            <img style="height: 100px; width: 100%; object-fit: contain; display: block;" src="${data[0].logo}" alt="${data[0].logo}">
          </div>
        `
            : ''
        }
        <strong class="heading">${data[0]?.store_name}</strong>
      </div>
      <div class="merchant-info">
        <ul class="info-list">
          <li>
            <span class="title">Merchant ID:</span>
            <span class="value">${data[0]?.merchant_id}</span>
          </li>
          <li>
            <span class="title">Start Date:</span>
            <span class="value">${data[0]?.start_date}</span>
          </li>
          <li>
            <span class="title">Start Time:</span>
            <span class="value">${data[0]?.start_time}</span>
          </li>
          <li>
            <span class="title">End Date:</span>
            <span class="value">${data[0]?.end_date}</span>
          </li>
          <li>
            <span class="title">End Time:</span>
            <span class="value">${data[0]?.end_time}</span>
          </li>
        </ul>
      </div>
    </div>
    ${data
      .map(
        item => `
      <div class="content-wrap">
        <div class="merchant-info">
          <strong class="heading">
            <span class="title">Terminal ID:</span>
            <span class="value">${item?.terminal_id}</span>
          </strong>
          <ul class="info-list has-border">
            ${Object.entries(item.tr_total)
              .map(
                ([key, value]) => `
              <li>
                <span class="title">${key}</span>
                <span class="value">${value}</span>
              </li>
            `,
              )
              .join('')}
          </ul>
        </div>
      </div>
    `,
      )
      .join('')}
  </div>
</body>
</html>`;
};
