const bcryptjs = require('bcryptjs');
const { STORE, TERMINAL_TRANSACTION, TERMINAL } = require('../models');
const axios = require('axios');
const sgMail = require('@sendgrid/mail');
const nodemailer = require('nodemailer');
const twilio = require('twilio');
const crypto = require('crypto');
const fs = require('fs');
const csv = require('csv-parser');

sgMail.setApiKey(process.env.SENDGRID_API_KEY);

const {
  access_key,
  sendgrid_api_key,
  sendgrid_user_name,
  sendgrid_port,
  sendgrid_host,
  sendgrid_email_from,
  mail_host,
  mail_port,
  mail_username,
  mail_password,
  mail_from_address,
  mail_from_name,
  twillo_account_ssid,
  twillo_auth_token,
  twillo_number,
  base_url,
  merchant_tool_url,
  merchant_tool_api_key,
  rewards_backend_url,
} = require('../configs');
const { default: mongoose } = require('mongoose');
module.exports = {
  hashPassword: password => {
    const salt = bcryptjs.genSaltSync(10);
    const passwordHashed = bcryptjs.hashSync(password, salt);
    return passwordHashed;
  },
  pagination: (items = [], page = 1, totalItems = 0, itemsPerPage = 5) => {
    return {
      currentPage: page,
      hasNextPage: itemsPerPage * page < totalItems,
      hasPreviousPage: page > 1,
      nextPage: page + 1,
      previousPage: page - 1,
      lastPage: Math.ceil(totalItems / itemsPerPage),
      totalItems: totalItems,
      items: items,
    };
  },
  filterQuery: req => ({
    ...req.query,
    page: req.query.page ? Number(req.query.page) : 1,
    itemsPerPage: req.query.itemsPerPage
      ? Number(req.query.itemsPerPage)
      : req.query.perPage
        ? Number(req.query.perPage)
        : 10,
    searchText:
      req.query.searchText !== 'null' && req.query.searchText !== 'undefined' && req.query.searchText
        ? req.query.searchText
        : '',
    startDate:
      req.query.startDate !== 'null' && req.query.startDate !== 'undefined' && req.query.startDate
        ? req.query.startDate
        : '',
    endDate:
      req.query.endDate !== 'null' && req.query.endDate !== 'undefined' && req.query.endDate ? req.query.endDate : '',
    storeId:
      req.query.storeId !== 'null' && req.query.storeId !== 'undefined' && req.query.storeId ? req.query.storeId : '',
  }),
  readAllSchemaFiles: async () => {
    const path = require('path');
    const createCsvWriter = require('csv-writer').createObjectCsvWriter;

    const directoryPath = path.join(__dirname, '../confirmed_models');
    const csvPath = path.join(__dirname, '../utils/csv');

    const files = await new Promise((resolve, reject) => {
      fs.readdir(directoryPath, (err, files) => {
        if (err) {
          reject(err);
        } else {
          resolve(files);
        }
      });
    });

    files.forEach(async file => {
      let csvFileName = file.replace('.js', '.csv');
      const filePath = path.join(directoryPath, file);

      // Require each file
      let model = require(filePath);
      if (typeof model === 'function') {
        const newObject = Object.entries(model.schema.paths).reduce((acc, [key, payload]) => {
          acc.push({
            params: key,
            requred: payload?.isRequired ? 'Y' : 'O',
            unique: payload?.options?.unique ? 'Y' : 'N',
            data_type: payload?.instance,
            enum_values: payload?.enumValues?.length ? payload?.enumValues : '',
            default_values: payload?.options?.default ? payload?.options?.default : '',
          });
          return acc;
        }, []);

        let csvWritePath = path.join(csvPath, csvFileName);

        const csvWriter = createCsvWriter({
          path: csvWritePath,
          header: Object.keys(newObject[0]).map(key => ({
            id: key,
            title: key.toUpperCase(),
          })),
        });
        await csvWriter.writeRecords(newObject);
      }
    });
  },
  generate6digitOtp: () => {
    let otp = Math.floor(Math.random() * 1000000);
    let zeroCount = otp.toString().split('0').length - 1;
    if (otp < 100000 || zeroCount > 1) {
      return module.exports.generate6digitOtp();
    }
    return otp;
  },
  getUsers: async (array, storeId) => {
    const store = await STORE.findById(storeId);
    const data = array
      .filter(user => !user.is_deleted)
      .map(({ first_name, last_name, ...user }) => {
        const updatedUser = {
          ...user,
          name: `${first_name} ${last_name}`,
          passcode: store.passcode,
        };

        return updatedUser;
      });
    return data;
  },
  mergeTwoObjects: (obj1, obj2) => {
    const merged = { ...obj1 };

    for (let key in obj2) {
      if (obj2.hasOwnProperty(key)) {
        if (merged.hasOwnProperty(key) && typeof merged[key] === 'object' && typeof obj2[key] === 'object') {
          merged[key] = module.exports.mergeTwoObjects(merged[key], obj2[key]);
        } else {
          merged[key] = obj2[key];
        }
      }
    }

    return merged;
  },
  getTimeAndDate: string => {
    const dateAndTime = new Date(string);
    const time = dateAndTime.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });
    const date = dateAndTime.toLocaleDateString();
    return `${time} ${date}`;
  },
  sendSocketNotification: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };
      await axios.post(`${base_url}/common/send-socket-notification`, payload, {
        headers,
      });
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },
  getConnectedTerminals: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };
      const response = await axios.post(`${base_url}/common/get-connected-terminals`, payload, {
        headers,
      });
      return {
        terminals: response.data.data,
      };
    } catch (error) {
      console.error('Error calling common service:', error);
      return {
        terminals: [],
      };
    }
  },
  sendEmail: async ({ template, toList, subject, attachment = null }) => {
    try {
      const msg = {
        to: toList,
        from: sendgrid_email_from,
        subject: subject,
        html: template,
        tls: {
          ciphers: 'SSLv3',
          rejectUnauthorized: false,
        },
        host: sendgrid_host,
        port: sendgrid_port,
        auth: {
          user: sendgrid_user_name,
          pass: sendgrid_api_key,
        },
      };

      await sgMail.send(msg);
      return { code: 200, message: 'Email sent!', success: true };
    } catch (err) {
      return { code: err.code, message: err.message, success: false };
    }
  },
  sendEmailLocally: ({ template, toList, subject, attachment = null }) => {
    try {
      let transporter = nodemailer.createTransport({
        host: mail_host,
        port: mail_port,
        secure: false,
        auth: {
          user: mail_username,
          pass: mail_password,
        },
        tls: {
          rejectUnauthorized: false,
        },
      });
      transporter.sendMail({
        from: `${mail_from_name} <${mail_from_address}>`,
        to: toList,
        replyTo: null,
        subject,
        html: template,
        attachments: attachment,
      });
    } catch (error) {
      throw new Error('Somehing Went Wrong While Sending Email:400');
    }
  },
  sendSMS: async payload => {
    const { receipt, phone_number } = payload;
    try {
      const client = twilio(twillo_account_ssid, twillo_auth_token);

      const body = {
        body: receipt,
        from: twillo_number,
        to: phone_number,
      };

      await client.messages.create(body);

      return true;
    } catch (error) {
      const errorMessage = error.message ?? 'Something Went Wrong While Sending SMS';
      throw new Error(`${errorMessage}:400`);
    }
  },

  generateApiKey: key => {
    const salt = crypto.randomBytes(16).toString('base64');

    const apiKey = crypto
      .createHash('sha256')
      .update(key + salt)
      .digest('base64');

    return apiKey;
  },
  padFunction: number => {
    let string = String(number);
    let sliced = string.slice(-4);
    let mask = String(sliced).padStart(string.length, '*');
    return mask;
  },
  publishToMqtt: async (topic, message) => {
    axios
      .post(
        `${base_url}/common/publish-to-mqtt`,
        {
          topic,
          message,
        },
        {
          headers: {
            Authorization: `Bearer ${access_key}`,
          },
        },
      )
      .then(res => {
        console.log(res.data);
      })
      .catch(err => {
        console.log(err.message);
      });
  },

  generateSingleReport: async (terminal_id, startDate, endDate) => {
    const transactionList = await TERMINAL_TRANSACTION.find({
      created_at: {
        $gte: startDate,
        $lt: endDate,
      },
      terminal_id: terminal_id,
    });

    const allSales = transactionList?.filter(
      d => d?.sale_amount > 0 && d?.status === 'Sale' && d?.transactionStatus === 'Approved',
    );
    const allSalesTotal = allSales?.reduce(
      (partialSum, a) => partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );
    const allTips = allSales?.filter(d => d?.tip_amount > 0);
    const allTipsTotal = allTips?.reduce((partialSum, a) => partialSum + parseFloat(a?.tip_amount), 0);

    const allRefunds = transactionList?.filter(
      d => d?.sale_amount > 0 && d?.status === 'Refunded' && d?.transactionStatus === 'Approved',
    );

    const allRefundsTotal = allRefunds?.reduce((partialSum, a) => partialSum + parseFloat(a?.sale_amount), 0);
    const transactionTotalData = {
      'No. of Transactions': allSales?.length,
      'Total Sales': `$${allSalesTotal?.toFixed(2)}`,
      'Total Tips': `$${allTipsTotal?.toFixed(2)}`,
      'No. of Refunds': allRefunds?.length,
      'Total Refunds': `$${allRefundsTotal?.toFixed(2)}`,
    };

    const individualTotals = transactionList?.map(d => {
      const totalAmount = (d?.sale_amount + d?.tip_amount + d?.surcharge_amount)?.toFixed(2);
      return {
        [`Reference # ${d?.reference_id}`]: d?.status,
        ...(d?.card_type && {
          [d?.entry_mode == 'M' ? 'CREDIT' : d?.card_type]: this.getMarkedCardNumber(d?.card_number),
        }),
        Date: d?.date,
        Time: d?.time,
        'Total Amount': `$${totalAmount} - ${d?.transactionStatus}`,
      };
    });
    return [
      {
        title: 'Transaction Totals',
        data: [transactionTotalData],
      },
      {
        title: 'Card Totals',
        data: await this.getCardBrandsTotalsSettlementData(transactionList),
      },
      {
        title: 'Individual Transaction Totals',
        data: individualTotals,
      },
    ];
  },
  generateAllReports: async (storeId, startDate, endDate) => {
    // Get all transactions for the store
    const transactions = await TERMINAL_TRANSACTION.find({
      created_at: {
        $gte: startDate,
        $lt: endDate,
      },
      store_id: storeId,
    });

    // Get all unique terminal ids
    const terminals = transactions
      .map(transaction => transaction.terminal_id)
      .filter((value, index, self) => self.indexOf(value) === index);

    // Generate reports for each terminal
    const terminalReports = await Promise.all(
      terminals.map(async terminal => {
        const terminalTransactions = transactions.filter(transaction => transaction.terminal_id === terminal);
        return await this.generateSingleReportByTerminalId(terminalTransactions, startDate, endDate);
      }),
    );

    return terminalReports;
  },
  getCardBrandsTotalsSettlementData: async transactionList => {
    const allSales = transactionList?.filter(
      d => d?.sale_amount > 0 && d?.status === 'Sale' && d?.transactionStatus === 'Approved',
    );
    const allSaleTips = allSales?.filter(d => d?.tip_amount > 0);
    const allRefunds = transactionList?.filter(
      d => d?.sale_amount > 0 && d?.status === 'Refunded' && d?.transactionStatus === 'Approved',
    );
    const interacSales = allSales?.filter(d => d?.card_type?.toLowerCase() === 'interac');
    const interacRefunds = allRefunds?.filter(d => d?.card_type?.toLowerCase() === 'interac');
    const totalInteracRefund = interacRefunds?.reduce((partialSum, a) => partialSum + parseFloat(a?.sale_amount), 0);
    const visaSales = allSales?.filter(
      d =>
        d?.card_type?.toLowerCase() === 'visa debit' ||
        d?.card_type?.toLowerCase() === 'visa credit' ||
        d?.card_type?.toLowerCase() === 'visa',
    );
    const manualSales = allSales?.filter(d => d?.entry_mode === 'M');
    const manualRefunds = allRefunds?.filter(d => d?.entry_mode === 'M');
    const totalManualRefunds = manualRefunds?.reduce((partialSum, a) => partialSum + parseFloat(a?.sale_amount), 0);

    const visaRefunds = allRefunds?.filter(
      d =>
        d?.card_type?.toLowerCase() === 'visa debit' ||
        d?.card_type?.toLowerCase() === 'visa credit' ||
        d?.card_type?.toLowerCase() === 'visa',
    );

    const totalVisaRefund = visaRefunds?.reduce((partialSum, a) => partialSum + parseFloat(a?.sale_amount), 0);

    const amexSales = allSales?.filter(d => d?.card_type?.toLowerCase() === 'amex');
    const amexRefunds = allRefunds?.filter(d => d?.card_type?.toLowerCase() === 'amex');
    const totalAmexRefund = amexRefunds?.reduce((partialSum, a) => partialSum + parseFloat(a?.sale_amount), 0);

    const masterCardSales = allSales?.filter(
      d =>
        d?.card_type?.toLowerCase() === 'debit mastercard' ||
        d?.card_type?.toLowerCase() === 'credit mastercard' ||
        d?.card_type?.toLowerCase() === 'mastercard',
    );
    const masterCardRefunds = allRefunds?.filter(
      d =>
        d?.card_type?.toLowerCase() === 'debit mastercard' ||
        d?.card_type?.toLowerCase() === 'credit mastercard' ||
        d?.card_type?.toLowerCase() === 'mastercard',
    );
    const totalMasterCardRefund = masterCardRefunds?.reduce(
      (partialSum, a) => partialSum + parseFloat(a?.sale_amount),
      0,
    );

    const terminalInteracTotals = interacSales?.reduce(
      (partialSum, a) =>
        partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.tip_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );
    const terminalVisaTotals = visaSales?.reduce(
      (partialSum, a) =>
        partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.tip_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );
    const terminalAmexTotals = amexSales?.reduce(
      (partialSum, a) =>
        partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.tip_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );
    const terminalManualTotals = manualSales?.reduce(
      (partialSum, a) =>
        partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.tip_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );

    const terminalMasterCardTotals = masterCardSales?.reduce(
      (partialSum, a) =>
        partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.tip_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );

    const interacTips = allSaleTips?.filter(d => d?.card_type?.toLowerCase() === 'interac');
    const amexTips = allSaleTips?.filter(d => d?.card_type?.toLowerCase() === 'amex');

    const visaTips = allSaleTips?.filter(
      d =>
        d?.card_type?.toLowerCase() === 'visa debit' ||
        d?.card_type?.toLowerCase() === 'visa credit' ||
        d?.card_type?.toLowerCase() === 'visa',
    );
    const manualTips = allSaleTips?.filter(d => d?.entry_mode === 'M');

    const masterCardTips = allSaleTips?.filter(
      d =>
        d?.card_type?.toLowerCase() === 'debit mastercard' ||
        d?.card_type?.toLowerCase() === 'credit mastercard' ||
        d?.card_type?.toLowerCase() === 'mastercard',
    );
    const interacTipsTotal = interacTips?.reduce((partialSum, a) => partialSum + a?.tip_amount, 0);
    const visaTipsTotal = visaTips?.reduce((partialSum, a) => partialSum + a?.tip_amount, 0);
    const manualTipsTotal = manualTips?.reduce((partialSum, a) => partialSum + a?.tip_amount, 0);

    const amexTipsTotal = amexTips?.reduce((partialSum, a) => partialSum + a?.tip_amount, 0);

    const masterCardTipsTotal = masterCardTips?.reduce((partialSum, a) => partialSum + a?.tip_amount, 0);

    const interacSalesAndSurcharge = interacSales.reduce(
      (partialSum, a) => partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );
    const visaSalesAndSurcharge = visaSales.reduce(
      (partialSum, a) => partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );
    const manualSalesAndSurcharge = manualSales.reduce(
      (partialSum, a) => partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );

    const amexSalesAndSurcharge = amexSales.reduce(
      (partialSum, a) => partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );

    const masterCardSalesAndSurcharge = masterCardSales.reduce(
      (partialSum, a) => partialSum + (parseFloat(a?.sale_amount) + parseFloat(a?.surcharge_amount)),
      0,
    );
    const data = [];
    let cards = await BIN_RANGE.findOne({}).sort({ created_at: -1 });
    cards = cards.pins;
    cards = cards.filter(x => x.active);
    cards = cards.map(x => x.card);

    if (cards.includes('Interac')) {
      data.push({
        title: ['Interac Totals'],
        data: [
          {
            Interac: 'Total Sales',
            Count: interacSales?.length ?? 0,
            Currency: '$',
            Totals: interacSalesAndSurcharge?.toFixed(2),
          },
          {
            Interac: 'Total Returns',
            Count: interacRefunds?.length ?? 0,
            Currency: '$',
            Totals: totalInteracRefund?.toFixed(2),
          },
          {
            Interac: 'Total Tips',
            Count: interacTips?.length ?? 0,
            Currency: '$',
            Totals: interacTipsTotal?.toFixed(2),
          },
          {
            Interac: 'Sub Total',
            Count: interacSales?.length + interacTips?.length,
            Currency: '$',
            Totals: (terminalInteracTotals - totalInteracRefund).toFixed(2),
          },
        ],
      });
    }

    if (cards.includes('MasterCard')) {
      data.push({
        title: ['MasterCard Totals'],
        data: [
          {
            MasterCard: 'Total Sales',
            Count: masterCardSales?.length ?? 0,
            Currency: '$',
            Totals: masterCardSalesAndSurcharge?.toFixed(2),
          },
          {
            MasterCard: 'Total Returns',
            Count: masterCardRefunds?.length ?? 0,
            Currency: '$',
            Totals: totalMasterCardRefund?.toFixed(2),
          },
          {
            MasterCard: 'Total Tips',
            Count: masterCardTips?.length ?? 0,
            Currency: '$',
            Totals: masterCardTipsTotal?.toFixed(2),
          },
          {
            MasterCard: 'Sub Total',
            Count: masterCardSales?.length + masterCardTips?.length,
            Currency: '$',
            Totals: (terminalMasterCardTotals - totalMasterCardRefund)?.toFixed(2),
          },
        ],
      });
    }

    if (cards.includes('Visa')) {
      data.push({
        title: ['Visa Totals'],
        data: [
          {
            Visa: 'Total Sales',
            Count: visaSales?.length ?? 0,
            Currency: '$',
            Totals: visaSalesAndSurcharge?.toFixed(2),
          },
          {
            Visa: 'Total Returns',
            Count: visaRefunds?.length ?? 0,
            Currency: '$',
            Totals: totalVisaRefund?.toFixed(2),
          },
          {
            Visa: 'Total Tips',
            Count: visaTips?.length ?? 0,
            Currency: '$',
            Totals: visaTipsTotal?.toFixed(2),
          },
          {
            Visa: 'Sub Total',
            Count: visaSales?.length + visaTips?.length,
            Currency: '$',
            Totals: (terminalVisaTotals - totalVisaRefund).toFixed(2),
          },
        ],
      });
    }
    if (cards.includes('JCB')) {
      data.push({
        title: ['JCB Totals'],
        data: [
          {
            JCB: 'Total Sales',
            Count: 0,
            Currency: '$',
            Totals: '0.00',
          },
          {
            JCB: 'Total Returns',
            Count: 0,
            Currency: '$',
            Totals: ' 0.00',
          },
          {
            JCB: 'Total Tips',
            Count: 0,
            Currency: '$',
            Totals: '0.00',
          },
          {
            JCB: 'Sub Total',
            Count: 0,
            Currency: '$',
            Totals: '0.00',
          },
        ],
      });
    }

    if (cards.includes('American Express')) {
      data.push({
        title: ['American Express Totals'],
        data: [
          {
            'American Express': 'Total Sales',
            Count: amexSales?.length ?? 0,
            Currency: '$',
            Totals: amexSalesAndSurcharge?.toFixed(2),
          },
          {
            'American Express': 'Total Returns',
            Count: amexRefunds?.length ?? 0,
            Currency: '$',
            Totals: totalAmexRefund?.toFixed(2),
          },
          {
            'American Express': 'Total Tips',
            Count: amexTips?.length ?? 0,
            Currency: '$',
            Totals: amexTipsTotal?.toFixed(2),
          },
          {
            'American Express': 'Sub Total',
            Count: amexSales?.length + amexTips?.length,
            Currency: '$',
            Totals: (terminalAmexTotals - totalAmexRefund).toFixed(2),
          },
        ],
      });
    }
    data.push({
      title: ['Manual Totals'],
      data: [
        {
          Manual: 'Total Sales',
          Count: manualSales?.length ?? 0,
          Currency: '$',
          Totals: manualSalesAndSurcharge?.toFixed(2),
        },
        {
          Manual: 'Total Returns',
          Count: manualRefunds?.length ?? 0,
          Currency: '$',
          Totals: totalManualRefunds?.toFixed(2),
        },
        {
          Manual: 'Total Tips',
          Count: manualTips?.length ?? 0,
          Currency: '$',
          Totals: manualTipsTotal?.toFixed(2),
        },
        {
          Manual: 'Sub Total',
          Count: manualSales?.length + manualTips?.length,
          Currency: '$',
          Totals: (terminalManualTotals - totalManualRefunds).toFixed(2),
        },
      ],
    });

    return data;
  },
  getMarkedCardNumber: cardNumber => {
    let markedStr = '************';
    const last4Digits = cardNumber.substring(cardNumber?.length - 4);
    return `${markedStr}${last4Digits}`;
  },
  sendToMerchantTools: async payload => {
    try {
      await axios.post(
        `${merchant_tool_url}/api/add-transaction`,
        { data: payload, type: 'PSPAPP' },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + merchant_tool_api_key,
          },
        },
      );
      return '';
    } catch (err) {
      console.log(err.message);
      return '';
    }
  },
  sendToRewards: async payload => {
    try {
      if (!rewards_backend_url) {
        console.log('REWARDS_BACKEND_URL not configured. Skipping reward processing.');
        return;
      }

      console.log(`Sending payload to rewards service: ${rewards_backend_url}/reward_app/v1/process-transaction`);

      // Use access_key instead of hardcoded token
      const response = await axios.post(`${rewards_backend_url}/reward_app/v1/process-transaction`, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_key}`,
        },
      });

      console.log('Rewards service response:', response.status, response.statusText);
      return response.data;
    } catch (err) {
      console.error('Error sending to rewards service:', err.message);
      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);
      }
      return '';
    }
  },
  insertMissingTransactionsFromCSV: async res => {
    try {
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');

      const path = require('path');
      const csvFilePath = path.join(__dirname, './processed_transactions.csv');
      const results = [];

      res.write('Starting to process CSV file...\n');
      const collection = await mongoose.connection.db.collection('terminal_transactions');
      res.write('Connected to TERMINAL_TRANSACTION collection.\n');
      await collection.createIndex({ terminal_id: 1 });
      res.write('Created index on terminal_id.\n');
      await collection.createIndex({ reference_id: 1 });
      res.write('Created index on reference_id.\n');
      await collection.createIndex({ created_at: -1 });
      res.write('Created index on created_at.\n');
      await collection.createIndex({ transactionStatus: 1 });
      res.write('Created index on transactionStatus.\n');
      await collection.createIndex({ status: 1 });
      res.write('Created index on status.\n');
      await collection.createIndex({ store_id: 1 });
      res.write('Created index on store_id.\n');
      await collection.createIndex({ terminal_id: 1, reference_id: 1 });
      res.write('Created index on terminal_id and reference_id.\n');
      await collection.createIndex({ terminal_id: 1, created_at: -1 });
      res.write('Created index on terminal_id and created_at.\n');
      await collection.createIndex({ store_id: 1, created_at: -1 });
      res.write('Created index on store_id and created_at.\n');
      await collection.createIndex({ reference_id: 1, transactionStatus: 1 });
      res.write('Created index on reference_id and transactionStatus.\n');
      await collection.createIndex({ reference_id: 1, terminal_id: 1 });
      res.write('Created index on reference_id and terminal_id.\n');

      res.write('Indexes created for TERMINAL_TRANSACTION collection.\n');

      await new Promise((resolve, reject) => {
        fs.createReadStream(csvFilePath)
          .pipe(csv())
          .on('data', data => results.push(data))
          .on('end', resolve)
          .on('error', reject);
      });

      let uniqueTerminalIds = [...new Set(results.map(row => row.terminal_id))];

      uniqueTerminalIds = await Promise.all(
        uniqueTerminalIds.map(async terminalId => {
          let invoice_no = await TERMINAL_TRANSACTION.countDocuments({ terminal_id: terminalId });
          return {
            terminal_id: terminalId,
            invoice_no: invoice_no,
          };
        }),
      );

      uniqueTerminalIds = uniqueTerminalIds.reduce((acc, curr) => {
        acc[curr.terminal_id] = curr.invoice_no;
        return acc;
      }, {});

      res.write(`Found ${Object.keys(uniqueTerminalIds).length} unique terminal IDs.\n`);

      console.log(`Found ${results.length} transactions in CSV`);
      // const findType = async bin => {
      //   try {
      //     bin = bin.split('').slice(0, 6).join('');
      //     const response = await axios.post(
      //       `https://bin-ip-checker.p.rapidapi.com/?bin=${bin}`,
      //       {
      //         bin,
      //       },
      //       {
      //         headers: {
      //           'Content-Type': 'application/json',
      //           'x-rapidapi-host': 'bin-ip-checker.p.rapidapi.com',
      //           'x-rapidapi-key': '**************************************************',
      //         },
      //       },
      //     );
      //     return response.data.type;
      //   } catch (error) {
      //     return null;
      //   }
      // };
      let insertedCount = 0;
      let bulkWrite = [];
      for (const row of results) {
        // row.type = await findType(row?.card_number);
        row.invoice_no = uniqueTerminalIds[row?.terminal_id] + 1;
        uniqueTerminalIds[row?.terminal_id] = row.invoice_no;
        row.type = 'DEBIT';
        bulkWrite.push({
          updateOne: {
            filter: { reference_id: row?.reference_id },
            update: {
              $setOnInsert: {
                ...row,
                created_at: new Date(row?.created_at),
              },
            },
            upsert: true,
          },
        });
        insertedCount++;
      }
      res.write(`Prepared ${insertedCount} transactions for bulk write.\n`);
      if (bulkWrite.length > 0) {
        const batches = Math.ceil(bulkWrite.length / 100);
        for (let i = 0; i < batches; i++) {
          const batch = bulkWrite.slice(i * 100, (i + 1) * 100);
          await TERMINAL_TRANSACTION.bulkWrite(batch, { ordered: false });
          res.write(`Processed batch ${i + 1} of ${batches}\n`);
          console.log(`Processed batch ${i + 1} of ${batches}`);
        }
      }
      return res.end();
    } catch (error) {
      console.error('Error processing CSV file:', error);
      throw error;
    }
  },
};
