NODE_ENV=local
ECHO=l
WINDOW=30
MAX_LIMIT=10
PORT=4001
MONGO_URI="mongodb://localhost:27017/store"
ACCESS_KEY="qwertyuiop"
TWILLO_ACCOUNT_SSID=
TWILLO_NUMBER=
SENDGRID_API_KEY=
SENDGRID_USER_NAME=
SENDGRID_HOST='smtp.sendgrid.net'
SENDGRID_PORT=587
SENDGRID_FROM_EMAIL='<EMAIL>'
WEBSOCKET_PATH="/websockets"
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME='<EMAIL>'
MAIL_FROM_ADDRESS='<EMAIL>'
MAIL_FROM_NAME="PSP-POS Team"
BASE_URL='http://localhost:4005'
MQTT_URL = rabbitmq-dev.pspservicesco.com
MQTT_PORT = 8883
MQTT_CA = './utils/certs-dev/certs/ca_cert.pem'
MQTT_CERT = './utils/certs-dev/certs/client_cert.pem'
MQTT_KEY = './utils/certs-dev/certs/client_key.pem'
REWARDS_BACKEND_URL=http://localhost:5000