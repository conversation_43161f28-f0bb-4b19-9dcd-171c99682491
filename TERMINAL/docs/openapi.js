/**
 * @fileoverview OpenAPI configuration for TERMINAL service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const terminalSchemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for TERMINAL service
 * @param {Object} app - Express app instance
 */
function setupTerminalDocs(app) {
  const config = {
    serviceName: 'TERMINAL',
    serviceDescription: `
      Terminal management and operations service

      Key features:
      - Entity management
      - Authentication and authorization
      - Data validation and processing
    `,
    version: '1.0.0',
    port: 4001,
    basePath: '/terminal',
    additionalSchemas: terminalSchemas,
    docsPath: '/terminal/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  setupTerminalDocs
};