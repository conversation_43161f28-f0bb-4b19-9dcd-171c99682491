/**
 * @fileoverview OpenAPI schemas specific to TERMINAL service
 */

module.exports = {
  Terminal: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      serial_number: {
        type: 'string',
        example: 'TRM001234',
        description: 'Terminal serial number'
      },
      terminal_name: {
        type: 'string',
        example: 'Store Front Terminal',
        description: 'Terminal display name'
      },
      store_id: {
        $ref: '#/components/schemas/ObjectId',
        description: 'Associated store ID'
      },
      business_id: {
        $ref: '#/components/schemas/ObjectId',
        description: 'Associated business ID'
      },
      terminal_type: {
        type: 'string',
        enum: ['POS', 'ATM', 'KIOSK'],
        example: 'POS',
        description: 'Type of terminal'
      },
      api_key: {
        type: 'string',
        example: 'api_key_123456',
        description: 'Terminal API key'
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      last_ping: {
        $ref: '#/components/schemas/Timestamp',
        description: 'Last ping timestamp'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['serial_number', 'terminal_name', 'store_id', 'business_id']
  },

  CreateTerminalRequest: {
    type: 'object',
    properties: {
      serial_number: {
        type: 'string',
        minLength: 3,
        example: 'TRM001234',
        description: 'Terminal serial number'
      },
      terminal_name: {
        type: 'string',
        minLength: 2,
        example: 'Store Front Terminal',
        description: 'Terminal display name'
      },
      store_id: {
        $ref: '#/components/schemas/ObjectId',
        description: 'Store ID to assign terminal to'
      },
      business_id: {
        $ref: '#/components/schemas/ObjectId',
        description: 'Business ID to assign terminal to'
      },
      terminal_type: {
        type: 'string',
        enum: ['POS', 'ATM', 'KIOSK'],
        example: 'POS',
        description: 'Type of terminal'
      },
      location: {
        type: 'string',
        example: 'Front Counter',
        description: 'Terminal location description'
      }
    },
    required: ['serial_number', 'terminal_name', 'store_id', 'business_id', 'terminal_type']
  },

  UpdateTerminalRequest: {
    type: 'object',
    properties: {
      terminal_name: {
        type: 'string',
        minLength: 2,
        example: 'Updated Terminal Name',
        description: 'Terminal display name'
      },
      terminal_type: {
        type: 'string',
        enum: ['POS', 'ATM', 'KIOSK'],
        example: 'POS',
        description: 'Type of terminal'
      },
      location: {
        type: 'string',
        example: 'Back Counter',
        description: 'Terminal location description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  UpdateTerminalRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Updated Terminal',
        description: 'Terminal name'
      },
      description: {
        type: 'string',
        example: 'Updated description',
        description: 'Terminal description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  TerminalListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Terminal'
            }
          }
        }
      }
    ]
  }
};