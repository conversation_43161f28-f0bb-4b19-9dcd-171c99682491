const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const uniqueValidator = require('mongoose-unique-validator');

const terminalReportSchema = new Schema(
  {
    report: [],
    serial_number: { type: String, required: true },
    terminal_id: { type: Schema.Types.ObjectId, ref: 'terminal' },
    store_id: { type: Schema.Types.ObjectId, ref: 'store' },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  },
);

terminalReportSchema.plugin(uniqueValidator);

const TERMINAL_REPORT = model('terminal_report', terminalReportSchema);

module.exports = TERMINAL_REPORT;
