const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const certificatesSchema = new Schema(
  {
    ca: {
      type: String,
      required: true,
    },
    cert: {
      type: String,
      required: true,
    },
    key: {
      type: String,
      required: true,
    },
    passphrase: {
      type: String,
      required: true,
    },
    ip: {
      type: String,
      required: true,
    },
    port: {
      type: String,
      required: true,
    },
    status: { type: String, enum: ['Active', 'Expired'], default: 'Active' },
    expired: { Type: Date },
    version: { type: Number, default: 0 },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

certificatesSchema.plugin(uniqueValidator);

const CERTIFICATE = model('certificate', certificatesSchema);
module.exports = CERTIFICATE;
