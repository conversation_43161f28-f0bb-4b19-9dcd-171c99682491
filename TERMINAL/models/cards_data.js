const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const uniqueValidator = require('mongoose-unique-validator');

const cardsDataSchema = new Schema(
  {
    raw_data: Object,
    iso8583data: Object,
    verified: Object,
    cvm: Object,
    pan: String,
    type: String,
    response: Object,
    online: Object,
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  },
);

cardsDataSchema.plugin(uniqueValidator);

const CARDS_DATA = model('cards_data', cardsDataSchema);

module.exports = CARDS_DATA;
