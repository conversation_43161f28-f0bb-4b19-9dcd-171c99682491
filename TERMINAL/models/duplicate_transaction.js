const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const duplicateTransactionSchema = new Schema({
  card_type: {
    type: String,
  },
  card_number: {
    type: String,
  },
  type: {
    type: String,
  },
  date: {
    type: String,
  },
  time: {
    type: String,
  },
  account_type: {
    type: String,
  },
  sale_amount: {
    type: Number,
  },
  tip_amount: {
    type: Number,
  },
  surcharge_amount: {
    type: Number,
  },
  service_code: {
    type: String,
  },
  entry_mode: {
    type: String,
  },
  invoice_no: {
    type: String,
  },
  created_at: {
    type: Date,
  },
  transaction_no: {
    type: String,
  },
  auth_no: {
    type: String,
  },
  reference_id: {
    type: String,
  },
  batch_no: {
    type: String,
  },
  status: {
    type: String,
  },
  aid: {
    type: String,
  },
  tid: {
    type: String,
  },
  tsi: {
    type: String,
  },
  tvr: {
    type: String,
  },
  trace_id: {
    type: String,
  },
  transactionStatus: {
    type: String,
  },
  responseCode: {
    type: String,
  },
  terminal_id: {
    type: String,
  },
  merchant_id: {
    type: String,
  },
  serial_number: {
    type: String,
    required: true,
  },
  store_id: {
    type: String,
    required: true,
  },
  is_auto_invoice: {
    type: Boolean,
    default: false,
  },
  clerk_id: {
    type: String,
  },
  optional_fields: {
    type: Object,
  },
  created_at: {
    type: Date,
    required: true,
  },
});

module.exports = mongoose.model('duplicate_transactions', duplicateTransactionSchema);
