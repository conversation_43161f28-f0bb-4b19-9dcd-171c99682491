const mongoose = require('mongoose');
const sales_receipt = require('../utils/templates/sales_receipt');
const receipt = require('../utils/templates/receipt');
const {
  TERMINAL,
  USER,
  STORE,
  CONFIGURATION_TEMPLATE,
  CONFIGURATION,
  TERMINAL_GROUP,
  RESTRICTED_PINS,
  BUSINESS,
  CERTIFICATES,
  RECEIPTS_DATA,
  TAG_TEMPLATE,
  EMV_TAG,
  TERMINAL_REPORT,
  TERMINAL_TRANSACTION,
  BIN_RANGE,
  CARD_DETAILS,
  DUPLICATE_TRANSACTION,
} = require('../models');
const {
  pagination,
  filterQuery,
  generate6digitOtp,
  getUsers,
  sendSocketNotification,
  getConnectedTerminals,
  getTimeAndDate,
  mergeTwoObjects,
  generateApiKey,
  sendEmail,
  sendSMS,
  sendEmailLocally,
  generateAllReports,
  generateSingleReport,
  sendToMerchantTools,
  sendToRewards,
} = require('../utils/helper');
const { differenceInMinutes, formatISO, startOfDay, subDays, endOfDay } = require('date-fns');
const { env } = require('../configs');
const symmaryReportEmail = require('../utils/templates/summaryReportEmail');
const detailedReportEmail = require('../utils/templates/detailedReportEmail');
const totalsReportEmail = require('../utils/templates/totalsReportEmail');
const { decryptCBC, encryptCBC } = require('../utils/cbc');

exports.addTerminal = async (req, res) => {
  let { title, template_id, store_id, configuration, serial_number, group_id, logo, tid, original_tid } = req.body;

  const terminal = await TERMINAL.findOne({ serial_number, is_deleted: false });

  if (terminal) {
    throw new Error('Device Already Exists:409');
  }

  const store = await STORE.findById(store_id).populate({ path: 'tag_template', model: TAG_TEMPLATE });

  if (store?.status !== 'Active') {
    throw new Error("You can't do that because Store associated with this Device is not Active:400");
  }

  const [oldTerminal] = await TERMINAL.find({}).sort({ created_at: -1 }).limit(1);
  let id = oldTerminal?.device_id;
  let device_id = +id ? +id + 1 : 1;
  device_id = device_id.toString().padStart(6, '0');

  let newConfiguration = null;
  if (template_id) {
    newConfiguration = await CONFIGURATION_TEMPLATE.findById(template_id);
    const defaultTimes = {
      start_time: '09:00:00',
      end_time: '17:00:00',
    };
    const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    newConfiguration.configuration.terminal_configuration.default_dates = daysOfWeek.reduce((acc, day) => {
      acc[day] = defaultTimes;
      return acc;
    }, {});
    newConfiguration = await CONFIGURATION.create({
      configuration: newConfiguration.configuration,
    });
  } else {
    let store = await STORE.findById(store_id)
      .select(['title', 'address.formatted_address', 'report_print_time'])
      .lean();

    logo && (configuration.printer.receipt_header_lines.line_1 = logo);
    const address = store?.address?.formatted_address.split(',');
    configuration.printer.auto_print.fee = store.report_print_time;
    configuration.printer.receipt_header_lines.line_2 = store.title;
    configuration.printer.receipt_header_lines.line_3 = address?.[0];
    configuration.printer.receipt_header_lines.line_4 = address?.splice(1)?.join('').trim();
    const defaultTimes = {
      start_time: '09:00:00',
      end_time: '17:00:00',
    };
    const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    configuration.terminal_configuration.default_dates = daysOfWeek.reduce((acc, day) => {
      acc[day] = defaultTimes;
      return acc;
    }, {});
    newConfiguration = await CONFIGURATION.create({ configuration });
  }
  let userss;
  if (group_id) {
    const terminalGroup = await TERMINAL_GROUP.findById(group_id).select('user_group_id');
    userss = await USER.find({
      status: 'Active',
      $or: [
        { group_id: { $in: terminalGroup.user_group_id } },
        { type: { $in: ['manager', 'owner'] }, is_deleted: false, store_id },
      ],
    }).select('_id');
  } else {
    userss = await USER.find({ store_id: store_id, group_id: { $size: 0 }, status: 'Active' });
  }

  let user_id = userss.map(user => user?._id?.toString());
  let otp = generate6digitOtp();

  const created_time = formatISO(new Date());

  const dataToSave = {
    title,
    user_id,
    store_id,
    tagString: store.tag_template.tagString,
    tags: store.tag_template.tags,
    tagStringFormat: 'default',
    tag_template: store.tag_template._id,
    device_id,
    serial_number,
    tid,
    business_id: store?.business_id,
    configuration: newConfiguration?._id,
    activation_code: { otp, created_time },
    original_tid,
  };

  if (group_id) {
    dataToSave.group_id = group_id;
  }
  let newT = new TERMINAL({
    ...dataToSave,
  });

  await newT.save();
  let foundT = await TERMINAL.findById(newT._id).populate({ path: 'business_id', model: BUSINESS, select: 'mid' });

  sendSocketNotification({
    serial_number: newT?.serial_number,
    event: 'terminal_created',
    data: {
      store_id: newT.store_id,
      status: newT.status,
      serial_number: newT.serial_number,
      mid: foundT?.business_id?.mid,
    },
  });
  sendSocketNotification({
    serial_number: `terminals_${foundT?.business_id?.mid}`,
    event: `terminals_${foundT?.business_id?.mid}`,
    data: {
      store_id: newT.store_id,
      status: newT.status,
      serial_number: newT.serial_number,
      mid: foundT?.business_id?.mid,
      is_pos: newT.configuration?.configuration?.pos?.pos_terminal?.value,
      shouldRefetchTerminals: true,
      timestamp: new Date().toISOString(),
    },
  });

  return res.status(200).json({
    code: 200,
    success: true,
    message: 'Device created!',
  });
};

exports.getSingleTerminal = async (req, res) => {
  const { id } = req.params;

  const terminal = await TERMINAL.findById(id);

  if (!terminal) {
    throw new Error('Device not found:404');
  }

  res.status(200).json({
    code: 200,
    success: true,
    data: terminal,
  });
};

exports.getAll = async (req, res) => {
  const {
    page,
    itemsPerPage,
    searchText,
    startDate,
    endDate,
    storeId,
    status,
    group,
    groupPage,
    getDeleted,
    user_id,
    getStoreInfo,
    getBusinessInfo,
    getAllTerminalsCount,
    skip_extra = '',
  } = filterQuery(req);

  let query = {
    $and: [],
    $or: [],
  };

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [
      { title: regExp },
      { device_id: regExp },
      { serial_number: regExp },
      { app_version: regExp },
      { tid: regExp },
    ];
  }

  if (startDate && endDate) {
    let start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    let end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    query.$and.push({ created_at: { $gte: start, $lt: end } });
  }

  if (storeId && storeId !== '') {
    query.$and.push({ store_id: mongoose.Types.ObjectId(storeId) });
  }

  if (status && status !== '') {
    query.$and.push({
      status: { $eq: status },
    });
  }

  if (groupPage == 'true' || groupPage === true) {
    if (!group) {
      query.group_id = { $exists: false };
    } else {
      query.group_id = group;
    }
  }

  if (getDeleted && getDeleted !== '') {
    query.$and.push({ is_deleted: JSON.parse(getDeleted) });
  }

  if (user_id && user_id !== '') {
    query.$and.push({ user_id: { $in: [mongoose.Types.ObjectId(user_id)] } });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }

  if (!query.$or.length > 0) {
    delete query.$or;
  }
  const totalTerminals = await TERMINAL.find(query).countDocuments();

  let terminals = await TERMINAL.find(query)
    .populate({ path: 'user_id', model: USER })
    .populate({ path: 'group_id', model: TERMINAL_GROUP })
    .populate({ path: 'tags', model: EMV_TAG })
    .sort([
      ['is_deleted', 1],
      ['updated_at', -1],
    ])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .lean();

  terminals = terminals.map(_ => {
    return {
      ..._,
      last_printed: _?.last_printed ? getTimeAndDate(_?.last_printed) : null,
      configId: _.configuration._id,
    };
  });

  terminals = terminals.map(_ => ({
    ..._,
    version: _.app_version ? _.app_version : null,
  }));

  if (terminals[0]) {
    terminals[0] = {
      ...terminals[0],
      connectedTerminals: skip_extra
        ? []
        : (
            await getConnectedTerminals({
              room: terminals?.[0]?.store_id?.toString(),
            })
          ).terminals,
    };
  }

  if (getStoreInfo && getStoreInfo !== '' && getStoreInfo === 'true') {
    const terminalsWithStoreInfo = [];
    for (let item = 0; item <= terminals.length - 1; item++) {
      const store_id = await STORE.findById(terminals[item].store_id);
      terminalsWithStoreInfo.push({ ...terminals[item], store_id });
    }

    terminals = terminalsWithStoreInfo;
  }

  if (getBusinessInfo && getBusinessInfo !== '' && getBusinessInfo === 'true') {
    const terminalsWithBusinessInfo = [];
    for (let item = 0; item <= terminals.length - 1; item++) {
      const business_id = await BUSINESS.findById(terminals[item].business_id);
      terminalsWithBusinessInfo.push({ ...terminals[item], business_id });
    }

    terminals = terminalsWithBusinessInfo;
  }
  let statusCount = null;
  if (getAllTerminalsCount && getAllTerminalsCount !== '') {
    const countQuery = [
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
        },
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          _id: 0,
        },
      },
    ];
    const totalTerminals = await TERMINAL.aggregate(countQuery);
    statusCount = totalTerminals;
  }

  const data = pagination(terminals, page, totalTerminals, itemsPerPage);
  data.statusCount = statusCount;

  return res.status(200).json({
    code: 200,

    ...data,
  });
};

exports.updateTerminal = async (req, res) => {
  const { id } = req.params;
  const payload = req.body;

  await TERMINAL.findOneAndUpdate({ _id: id }, { ...payload });

  const updatedTerminal = await TERMINAL.findById(id)
    .populate({ path: 'business_id', model: BUSINESS, select: 'mid' })
    .lean();

  sendSocketNotification({
    serial_number: updatedTerminal?.serial_number,
    event: 'terminal_update',
    data: {
      store_id: updatedTerminal.store_id,
      store_title: updatedTerminal.title,
      serial_number: updatedTerminal.serial_number,
      status: updatedTerminal.status,
      mid: updatedTerminal?.business_id?.mid,
    },
  });
  sendSocketNotification({
    serial_number: `status_${updatedTerminal?.serial_number}`,
    event: `status_${updatedTerminal?.serial_number}`,
    data: {
      store_id: updatedTerminal.store_id,
      serial_number: updatedTerminal.serial_number,
      mid: updatedTerminal?.business_id?.mid,
      status: updatedTerminal.status,
      online: false,
      in_use: false,
      mobile: false,
      shouldRefetchTerminals: true,
      timestamp: new Date().toISOString(),
    },
  });

  return res.status(200).json({
    code: 200,
    success: true,
    message: 'Device updated!',
    data: updatedTerminal,
  });
};

exports.deleteTerminal = async (req, res) => {
  const { id } = req.params;

  const terminal = await TERMINAL.findById(id);

  if (!terminal) {
    throw new Error('Device not found or already deleted:404');
  }
  const tid = terminal?.tid;

  const regexPattern = new RegExp(`^${tid}_deleted_\\d+$`, 'i');

  const deleted_terminal = await TERMINAL.findOne({ tid: { $regex: regexPattern } })
    .sort([['created_at', -1]])
    .exec();

  let updatedPage = null;
  if (deleted_terminal?.tid.includes(terminal.tid)) {
    const last_counter = Number(deleted_terminal.tid.split('_').slice(-1)[0]);

    updatedPage = await TERMINAL.findByIdAndUpdate(id, {
      is_deleted: true,
      $unset: { api_key: 1, group_id: 1, mac_address: 1 },
      user_id: [],
      status: 'Deactivated',
      tid: `${terminal?.tid}_deleted_${last_counter + 1}`,
    });
  } else {
    updatedPage = await TERMINAL.findByIdAndUpdate(id, {
      is_deleted: true,
      $unset: { api_key: 1, group_id: 1, mac_address: 1 },
      user_id: [],
      status: 'Deactivated',
      tid: `${terminal?.tid}_deleted_1`,
    });
  }

  if (updatedPage) {
    updatedPage = await TERMINAL.findById(updatedPage.id).populate({
      path: 'business_id',
      model: BUSINESS,
      select: 'mid',
    });

    sendSocketNotification({
      serial_number: updatedPage.serial_number,
      data: {
        store_id: updatedPage.store_id,
        serial_number: updatedPage.serial_number,
        status: updatedPage.status,
        mid: updatedPage?.business_id?.mid,
      },
      event: 'device_deleted',
    });
    sendSocketNotification({
      serial_number: `status_${updatedPage.serial_number}`,
      event: `status_${updatedPage.serial_number}`,
      data: {
        store_id: updatedPage.store_id,
        serial_number: updatedPage.serial_number,
        mid: updatedPage?.business_id?.mid,
        status: updatedPage.status,
        online: false,
        in_use: false,
        mobile: false,
        shouldRefetchTerminals: true,
        timestamp: new Date().toISOString(),
      },
    });
  }
  return res.status(200).json({
    code: 200,
    success: true,
    message: 'Device Deactivated!',
  });
};

exports.changeTerminalStatus = async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  const terminal = await TERMINAL.findById(id)
    .populate({
      model: STORE,
      path: 'store_id',
      select: { status: 1 },
    })
    .populate({ path: 'business_id', model: BUSINESS, select: 'mid' });

  if (!terminal) {
    throw new Error('Device Does Not Exists:404');
  }

  if (terminal?.store_id?.status !== 'Active') {
    throw new Error("You can't do that because Store associated with this Device is not Active:400");
  }

  sendSocketNotification({
    serial_number: terminal?.serial_number,
    event: 'terminal_status_changed',
    data: { ...terminal, status },
  });

  await TERMINAL.findByIdAndUpdate(id, { status });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Device Status Changed Successfully',
  });
};

exports.activateTerminal = async (req, res) => {
  let { macAddress, configuration, serial_number, original_tid = '__', coordinates, ignoreOtp = false } = req.body;

  const mac = macAddress?.toLowerCase();
  serial_number = serial_number?.toLowerCase();
  let otp = req.body?.otp?.trim();
  if (ignoreOtp && !otp) {
    let terminal = await TERMINAL.findOne({
      serial_number,
      original_tid,
      status: {
        $in: ['PendingActivation', 'Active'],
      },
      is_deleted: {
        $ne: true,
      },
    }).sort({ created_at: -1 });
    if (!terminal) {
      let terminals = await TERMINAL.find(
        {
          serial_number,
        },
        { original_tid: 1, status: 1, is_deleted: 1, serial_number: 1, tid: 1 },
      );
      console.log(JSON.stringify(terminals, null, 2));
      return res.status(400).json({
        message: 'Terminal Does not exist , or the terminal is not Activated from TMS',
      });
    }
    otp = generate6digitOtp();
    const created_time = formatISO(new Date());
    await TERMINAL.findByIdAndUpdate(terminal._id, {
      status: 'PendingActivation',
      api_key: '',
      activation_code: {
        otp,
        created_time,
      },
    });
  }

  const isExists = await TERMINAL.findOne({
    'activation_code.otp': otp,
    status: 'PendingActivation',
    serial_number,
    is_deleted: {
      $ne: true,
    },
  }).sort({ created_at: -1 });

  const currentTime = new Date();
  const activationCodeCreatedTime = new Date(isExists?.activation_code.created_time);
  const diffInMinutes = differenceInMinutes(currentTime, activationCodeCreatedTime);
  console.log('diffInMinutes', diffInMinutes);
  console.log('isExists', isExists);
  if (!isExists || diffInMinutes >= 5) {
    throw new Error('Invalid OTP or already Activated or Serial Number is not valid:400');
  }

  const config = await CONFIGURATION.findById(isExists?.configuration);

  const mergedConfiguration = mergeTwoObjects(config.configuration, configuration.configuration);

  await CONFIGURATION.findByIdAndUpdate(isExists?.configuration, {
    $set: { configuration: mergedConfiguration },
  });

  let users = await USER.find({
    _id: { $in: isExists.user_id },
    status: 'Active',
    is_deleted: false,
  })
    .select(['first_name', 'last_name', 'email', 'type', 'status', 'clerk_id', 'is_deleted'])
    .lean();

  const hashedApiKey = generateApiKey(mac + serial_number);

  let store = await STORE.findById(isExists?.store_id).select(['title', 'address.formatted_address', 'passcode']);

  const businessMID = await BUSINESS.findById(isExists.business_id).select('mid is_giftcard is_report').lean();

  const restricted_pins = await RESTRICTED_PINS.findOne({
    store_id: isExists.store_id,
  }).select('pins');

  users = users.map(user => {
    const name = `${user?.first_name} ${user?.last_name}`;
    delete user?.first_name;
    delete user?.last_name;

    const updatedUser = {
      ...user,
      name,
      passcode: store.passcode,
    };

    return updatedUser;
  });

  const address = store?.address?.formatted_address.split(',');

  store = {
    name: store.title,
    address_1: address?.[0],
    address_2: address?.splice(1)?.join(''),
    passcode: store.passcode,
    bin_ranges: restricted_pins?.pins ?? [],
  };

  //Fetching the most recent certificate
  let ipConfig = await CERTIFICATES.findOne()
    .sort({
      created_at: -1,
    })
    .lean();
  ipConfig.ca = decryptCBC(ipConfig.ca);
  ipConfig.key = decryptCBC(ipConfig.key);
  ipConfig.cert = decryptCBC(ipConfig.cert);
  await TERMINAL.findByIdAndUpdate(isExists._id, {
    mac_address: mac,
    api_key: hashedApiKey,
    status: 'Active',
    cert_version: ipConfig?.version,
    'activation_code.otp': '',
    location: { type: 'Point', coordinates: coordinates },
  });

  sendSocketNotification({
    event: `status_${serial_number}`,
    serial_number: `status_${serial_number}`,
    data: {
      store_id: isExists?.store_id,
      serial_number,
      mid: businessMID?.mid ?? 'N?A',
      status: 'Active',
      online: true,
      in_use: false,
      is_pos: mergedConfiguration?.pos?.pos_terminal?.value,
      mobile: false,
      shouldRefetchTerminals: true,
      timestamp: new Date().toISOString(),
    },
  });
  const terminal_count = await TERMINAL_TRANSACTION.countDocuments({ terminal_id: isExists.tid });
  const invoice_count = await TERMINAL_TRANSACTION.countDocuments({ terminal_id: isExists.tid, is_auto_invoice: true });
  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Device Activated!',
    data: {
      store,
      users,
      apiKey: hashedApiKey,
      ipConfig,
      mid: businessMID.mid,
      tid: isExists.tid,
      tagString: isExists.tagString || '',
      device_staus: 'Active',
      store_id: isExists?.store_id,
      configuration: mergedConfiguration,
      serial_number: isExists?.serial_number,
      ping_time: isExists?.setting.ping_time,
      device_name: isExists.title ? isExists.title : '',
      terminal_count,
      invoice_count,
      is_giftcard: businessMID.is_giftcard,
      is_report: businessMID.is_report,
    },
  });
};

exports.revokeApi = async (req, res) => {
  const { id } = req.params;

  const terminal = await TERMINAL.findById(id)
    .populate({
      model: STORE,
      path: 'store_id',
      select: { status: 1 },
    })
    .populate({ path: 'business_id', model: BUSINESS, select: 'mid' });

  if (!terminal) {
    return res.status(400).send({
      code: 400,
      success: false,
      message: 'Not found!',
    });
  }

  if (terminal?.store_id?.status !== 'Active') {
    throw new Error("You can't do that because store is not Active:400");
  }

  let otp = generate6digitOtp();

  const created_time = formatISO(new Date());

  await TERMINAL.findByIdAndUpdate(terminal._id, {
    status: 'PendingActivation',
    api_key: '',
    activation_code: {
      otp,
      created_time,
    },
  });

  sendSocketNotification({
    serial_number: terminal?.serial_number,
    data: {
      store_id: terminal.store_id._id,
      serial_number: terminal?.serial_number,
      status: 'PendingActivation',
      mid: terminal?.business_id?.mid,
    },
    event: 'device_deleted',
  });
  sendSocketNotification({
    serial_number: `status_${terminal?.serial_number}`,
    event: `status_${terminal?.serial_number}`,
    data: {
      store_id: terminal.store_id._id,
      serial_number: terminal?.serial_number,
      mid: terminal?.business_id?.mid,
      status: 'PendingActivation',
      online: false,
      in_use: false,
      mobile: false,
      shouldRefetchTerminals: true,
      timestamp: new Date().toISOString(),
    },
  });
  return res.status(200).send({
    code: 200,
    success: true,
    message: `API key revoked`,
    data: { otp },
  });
};

exports.forceDeactivateDevice = async (req, res) => {
  const { id } = req.params;

  const terminal = await TERMINAL.findById(id)
    .populate({
      model: STORE,
      path: 'store_id',
      select: { status: 1, _id: 1 },
    })
    .populate({ path: 'business_id', model: BUSINESS, select: 'mid' });

  if (!terminal) {
    return res.status(400).send({
      code: 400,
      success: false,
      message: 'Not found!',
    });
  }

  if (terminal?.store_id?.status !== 'Active') {
    throw new Error("You can't do that because store is not Active:400");
  }

  await TERMINAL.findByIdAndUpdate(terminal._id, {
    status: 'ForcedDeactivated',
    api_key: '',
  });

  sendSocketNotification({
    serial_number: terminal?.serial_number,
    event: 'device_deactivated_forcefully',
    data: {
      message: 'Device Deactivated',
      serial_number: terminal?.serial_number,
      status: 'ForcedDeactivated',
      mid: terminal?.business_id?.mid,
    },
  });

  sendSocketNotification({
    serial_number: `status_${terminal?.serial_number}`,
    event: `status_${terminal?.serial_number}`,
    data: {
      store_id: terminal.store_id._id,
      serial_number: terminal?.serial_number,
      mid: terminal?.business_id?.mid,
      status: 'ForcedDeactivated',
      online: false,
      in_use: false,
      mobile: false,
      shouldRefetchTerminals: true,
      timestamp: new Date().toISOString(),
    },
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: `Device Deactivated`,
  });
};

exports.updatePingTime = async (req, res) => {
  const { id } = req.params;
  const payload = req.body;

  if (payload.ping_time == 0) {
    throw new Error('Ping time must be greater then 0:400');
  }

  const terminal = await TERMINAL.findById(id);

  if (!terminal) {
    throw new Error('Terminal not found!:404');
  }

  await TERMINAL.findByIdAndUpdate(id, {
    $set: { 'setting.ping_time': payload.ping_time },
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Updated Ping Time',
  });
};

exports.updatePingTimeOnAllDevices = async (req, res) => {
  const payload = req.body;

  if (payload.ping_time == 0) {
    throw new Error('Ping time must be greater then 0:400');
  }

  await TERMINAL.updateMany({}, { $set: { 'setting.ping_time': payload.ping_time } });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Updated Ping Time on all devices',
  });
};

exports.createDeviceGroup = async (req, res) => {
  const { name, devices, storeId, user_group } = req.body;

  const isGroupExists = await TERMINAL_GROUP.findOne({ name: name?.trim() });

  if (isGroupExists) {
    throw new Error('Device Group Exists!:409');
  }

  let users = await USER.find({
    $or: [
      { group_id: { $in: user_group } },
      {
        type: { $in: ['manager', 'owner'] },
        is_deleted: false,
        store_id: storeId,
      },
    ],
  }).lean();
  const data = await getUsers(users, storeId);
  users = users.map(user => user._id.toString());

  const newGroupCreated = await TERMINAL_GROUP.create({
    name,
    store_id: storeId,
    user_group_id: user_group,
  });

  await TERMINAL.updateMany({ _id: { $in: devices } }, { $set: { group_id: newGroupCreated?._id }, user_id: users });
  const termUsers = await TERMINAL.find({ _id: { $in: devices } });

  if (termUsers.length > 0) {
    termUsers.forEach(tUser => {
      sendSocketNotification({
        serial_number: tUser.serial_number,
        event: 'user_updated',
        data,
      });
    });
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Group Created Successfully!',
  });
};

exports.deleteDeviceGroup = async (req, res) => {
  const { id } = req.params;

  const isGroupExists = await TERMINAL_GROUP.findById(id);
  if (!isGroupExists) {
    throw new Error('Group Not exists!:404');
  }
  const storeId = isGroupExists?.store_id;

  let users = await USER.find({
    group_id: { $size: 0 },
    is_deleted: false,
    store_id: storeId,
  }).lean();
  const data = await getUsers(users, storeId);
  users = users.map(user => user._id.toString());

  await TERMINAL_GROUP.findByIdAndDelete(id);
  const termUsers = await TERMINAL.find({ group_id: id }).select('serial_number');
  await TERMINAL.updateMany({ group_id: id }, { $unset: { group_id: 1 }, user_id: users });

  if (termUsers.length > 0) {
    termUsers.forEach(tUser => {
      sendSocketNotification({
        serial_number: tUser.serial_number,
        event: 'user_updated',
        data,
      });
    });
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Group Deleted Successfully!',
  });
};

exports.updateDeviceGroup = async (req, res) => {
  const { id } = req.params;

  const { name, devices, user_group, storeId } = req.body;

  await TERMINAL_GROUP.findByIdAndUpdate(id, {
    name,
    user_group_id: user_group,
  });

  let users = await USER.find({
    $or: [
      { group_id: { $in: user_group } },
      {
        type: { $in: ['manager', 'owner'] },
        is_deleted: false,
        store_id: storeId,
      },
    ],
  }).lean();
  const userData = await getUsers(users, storeId);
  users = users.map(user => user._id.toString());

  await TERMINAL.updateMany({ _id: { $in: devices } }, { $set: { group_id: id }, user_id: users });

  const termUsers = await TERMINAL.find({ group_id: id }).lean();
  if (termUsers.length > 0) {
    termUsers.forEach(tUser => {
      sendSocketNotification({
        serial_number: tUser.serial_number,
        event: 'user_updated',
        data: userData,
      });
    });
  }

  let freeUsers = await USER.find({
    group_id: { $size: 0 },
    is_deleted: false,
    store_id: storeId,
  }).lean();
  const freeUsersData = await getUsers(users, storeId);
  freeUsers = freeUsers.map(user => user._id.toString());

  await TERMINAL.updateMany(
    { _id: { $nin: devices }, group_id: { $eq: id } },
    { $unset: { group_id: 1 }, user_id: freeUsers },
  );
  const freeTermUsers = await TERMINAL.find({
    _id: { $nin: devices },
    store_id: storeId,
  }).select('serial_number');

  if (freeTermUsers.length > 0) {
    freeTermUsers.forEach(tUser => {
      sendSocketNotification({
        serial_number: tUser.serial_number,
        event: 'user_updated',
        data: freeUsersData,
      });
    });
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Group Updated Successfully!',
  });
};

exports.getAllDevicesGroups = async (req, res) => {
  let { page, itemsPerPage, searchText, status, startDate, endDate, storeId, getDeleted } = filterQuery(req);

  const query = {
    $and: [],
    $or: [],
  };

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [{ name: regExp }];
  }

  if (status && status !== '') {
    query.$and.push({
      status: { $eq: status },
    });
  }

  if (startDate && endDate) {
    let start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    let end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    query.$and.push({ created_at: { $gte: start, $lt: end } });
  }

  if (getDeleted && getDeleted !== '') {
    query.$and.push({ is_deleted: JSON.parse(getDeleted) });
  }

  if (storeId && storeId !== '') {
    query.$and.push({ store_id: mongoose.Types.ObjectId(storeId) });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }

  if (!query.$or.length > 0) {
    delete query.$or;
  }

  const totalItems = await TERMINAL_GROUP.countDocuments(query);

  const device_groups = await TERMINAL_GROUP.aggregate([
    { $match: query },
    { $sort: { created_at: -1 } },
    { $skip: (page - 1) * itemsPerPage },
    { $limit: itemsPerPage },
    {
      $lookup: {
        from: 'users',
        localField: 'user_group_id',
        foreignField: 'group_id',
        as: 'users',
      },
    },
    {
      $lookup: {
        from: 'terminals',
        localField: '_id',
        foreignField: 'group_id',
        as: 'terminals',
      },
    },
    {
      $lookup: {
        from: 'terminals',
        localField: '_id',
        foreignField: 'group_id',
        as: 'terminals',
      },
    },
    {
      $lookup: {
        from: 'configurations',
        localField: 'terminals.configuration',
        foreignField: '_id',
        as: 'configurations',
      },
    },
    {
      $group: {
        _id: '$_id',
        users: { $first: '$users' },
        terminals: { $first: '$terminals' },
        configurations: { $first: '$configurations' },
        name: { $first: '$name' },
        store_id: { $first: '$store_id' },
        user_group_id: { $first: '$user_group_id' },
        updated_at: { $first: '$updated_at' },
        created_at: { $first: '$created_at' },
        __v: { $first: '$__v' },
      },
    },
  ]);

  const records = pagination(device_groups, page, totalItems, itemsPerPage);

  return res.status(200).send({
    ...records,
    code: 200,
    success: true,
    message: 'All Device Groups Fetched Successfully',
  });
};

exports.getGroupDetails = async (req, res) => {
  const { id } = req.params;

  const grp = await TERMINAL_GROUP.findById(id).select(['name', 'created_at', 'updated_at', '_id', 'user_group_id']);

  const detail = {
    group: grp,
  };

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Device Group details!',
    data: detail,
  });
};

exports.sendReceiptEmail = async (req, res) => {
  const { data, to, subject } = req.body;
  if (!subject || !to) {
    throw new Error('Payload Error!:400');
  }
  let template = receipt(data);
  const terminal = await TERMINAL.findOne({ tid: data.terminal_id });
  if (terminal) {
    const businessName = await BUSINESS.findById(terminal.business_id).select('title');

    if (businessName?.title?.toLowerCase().includes('lynx')) {
      template = sales_receipt(data);
    }
  }
  // sendEmailLocally({ template: template, toList: [to], subject: subject });

  const response = await sendEmail({
    template: template,
    toList: [to],
    subject: subject,
  });

  if (response.success) {
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'Email Sent',
    });
  } else {
    throw new Error(`${response?.message}:${response?.code}`);
  }
};

exports.sendReceiptNumber = async (req, res) => {
  const { data, phone_number } = req.body;
  if (!phone_number) {
    throw new Error('Payload Error!:400');
  }

  const receiptData = await RECEIPTS_DATA.create({ data: data });

  let url = `${
    env === 'production'
      ? 'https://tms.pspservicesco.com'
      : env === 'staging'
      ? 'https://tms-dev.pspservicesco.com'
      : 'http://localhost:4001'
  }/terminal/v1/receipt/${receiptData?._id}`;

  const response = sendSMS({
    receipt: `Please view your receipt here ${url}`,
    phone_number: phone_number,
  });

  if (response) {
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'Receipt Sent',
      url,
    });
  }
};

exports.receiptSMS = async (req, res) => {
  const { id } = req.params;

  if (mongoose.Types.ObjectId.isValid(id)) {
    const data = await RECEIPTS_DATA.findById(id);
    if (!data) {
      throw new Error('Receipts not found:404');
    }
    let template = receipt(data.data);
    const terminal = await TERMINAL.findOne({ tid: data.terminal_id });
    if (terminal) {
      const businessName = await BUSINESS.findById(terminal.business_id).select('title');
      if (businessName?.title?.toLowerCase().includes('lynx')) {
        template = sales_receipt(data.data);
      }
    }

    const response = template;
    return res.status(200).send(response);
  } else {
    throw new Error('Invalid Receipt ID:400');
  }
};

exports.editTagString = async (req, res) => {
  const { id } = req.params;
  const payload = req.body;

  const tags = await EMV_TAG.find({ _id: { $in: payload.tags } });

  if (tags.length < 1) {
    throw new Error('Data not Found:404');
  }

  const tagString = tags
    .map(tag => tag.tag)
    .filter(_ => _)
    .join('');

  const updatedTerminal = await TERMINAL.findByIdAndUpdate(
    id,
    {
      $set: {
        tags: payload.tags,
        tagStringFormat: 'updated',
        tagString,
      },
      $unset: {
        tag_template: 1,
      },
    },
    { new: true },
  );

  sendSocketNotification({
    serial_number: updatedTerminal?.serial_number,
    event: 'tag_updated',
    data: { tagString },
  });
  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Tag String Updated',
  });
};
exports.getDeviceLocation = async (req, res) => {
  const { id } = req.params;
  const terminal = await TERMINAL.findById(id).select('location').lean();

  if (!terminal?.location?.coordinates) {
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'location',
      data: { coordinates: [] },
    });
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'location',
    data: { coordinates: terminal.location.coordinates || [] },
  });
};

/**
 * @deprecated
 */
exports.saveDeviceReport = async (req, res) => {
  const { serial_number, report, store_id } = req.body;

  if (!report || report.length < 1 || !serial_number || !store_id) {
    throw new Error('Bad Request:400');
  }

  const terminal = await TERMINAL.findOne({ serial_number, is_deleted: false, status: 'Active', store_id });

  if (!terminal) {
    throw new Error('Device Not Found or Not Active:404');
  }

  process.env.TZ = 'UTC';
  const today = startOfDay(new Date());
  const existingReportToday = await TERMINAL_REPORT.findOne({ serial_number, created_at: { $gte: today } });
  if (existingReportToday) {
    await TERMINAL_REPORT.updateOne(
      { serial_number, created_at: { $gte: today } },
      { $set: { report, terminal_id: terminal._id, store_id } },
    );
  } else {
    await TERMINAL_REPORT.create({
      report,
      serial_number,
      terminal_id: terminal._id,
      store_id,
      created_at: new Date(),
    });
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Operation Successful',
  });
};

exports.getReports = async (req, res) => {
  const { store_id } = req.params;

  if (!store_id) {
    throw new Error('Bad Request:400');
  }
  process.env.TZ = 'UTC';
  const startDate = startOfDay(new Date());
  const endDate = endOfDay(new Date());
  let terminalReports = await generateAllReports(store_id, startDate, endDate);

  const reports = [];

  for (const report of terminalReports) {
    const terminal = await TERMINAL.findById(report.terminal_id)
      .populate({
        path: 'configuration',
        model: CONFIGURATION,
      })
      .lean();
    reports.push({
      _id: report._id, // this is generated on runtime so we don`t have an id anymore.
      report: report.report,
      logo: terminal.configuration.configuration.printer.receipt_header_lines.line_1,
      tid: terminal.tid,
    });
  }

  if (reports.length < 1) {
    throw new Error('No Reports Found:404');
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Operation Successful',
    data: reports,
  });
};

exports.sendReportEmail = async (req, res) => {
  const { store_name, merchant_id, start_date, start_time, end_date, end_time, to, serial_number, store_id } = req.body;
  let reportData = null;

  process.env.TZ = 'UTC';
  const today = startOfDay(new Date());
  const endToday = endOfDay(new Date());

  if (serial_number) {
    const { tid: terminal_id } = await TERMINAL.findOne({ serial_number, is_deleted: false, status: 'Active' });
    reportData = await generateSingleReport(terminal_id, today, endToday);
  } else if (store_id) {
    reportData = await generateAllReports(store_id, today, endToday);
  } else {
    throw new Error('Invalid request');
  }

  const templateData = [];
  let email_type = null;

  if (Array.isArray(reportData)) {
    for (const report of reportData) {
      const terminal = await TERMINAL.findOne({
        serial_number: report.serial_number,
        is_deleted: false,
        status: 'Active',
      })
        .populate({ path: 'configuration', model: 'configuration' })
        .select(['tid', 'configuration']);

      if (!terminal) continue;
      email_type = terminal.configuration?.configuration?.printer?.report_type?.fee;
      const logo = terminal.configuration?.configuration?.printer?.receipt_header_lines?.line_1;
      const last_transaction = report.report[2]?.data[0]?.Time;
      const card_total = report.report[1].data.map(itm => ({
        title: itm.title[0],
        data: itm.data.map(_ => Object.values(_)),
      }));

      templateData.push({
        logo,
        store_name,
        merchant_id,
        terminal_id: terminal.tid,
        start_date,
        start_time,
        end_date,
        end_time,
        last_transaction,
        tr_total: report.report[0].data[0],
        card_total,
        total_transactions: report.report[2].data || [],
      });
    }
  } else if (reportData) {
    const terminal = await TERMINAL.findOne({ serial_number, is_deleted: false, status: 'Active' })
      .populate({ path: 'configuration', model: 'configuration' })
      .select(['tid', 'configuration']);

    if (!terminal) throw new Error('Terminal not found');

    email_type = terminal.configuration?.configuration?.printer?.report_type?.fee;
    const logo = terminal.configuration?.configuration?.printer?.receipt_header_lines?.line_1;
    const last_transaction = reportData?.report[2]?.data[0]?.Time;
    const card_total = reportData?.report[1].data.map(itm => ({
      title: itm.title[0],
      data: itm.data.map(_ => Object.values(_)),
    }));

    templateData.push({
      logo,
      store_name,
      merchant_id,
      terminal_id: terminal.tid,
      start_date,
      start_time,
      end_date,
      end_time,
      last_transaction,
      tr_total: reportData?.report[0].data[0],
      card_total,
      total_transactions: reportData?.report[2].data || [],
    });
  } else {
    throw new Error('No report data found');
  }

  let response = null;
  if (email_type === 'summary') {
    if (env === 'development' || env === 'local') {
      sendEmailLocally({ template: symmaryReportEmail(templateData), toList: [to], subject: 'Report Email' });
    } else {
      response = await sendEmail({ template: symmaryReportEmail(templateData), toList: [to], subject: 'Report Email' });
    }
  } else if (email_type === 'detailed') {
    if (env === 'development' || env === 'local') {
      sendEmailLocally({ template: detailedReportEmail(templateData), toList: [to], subject: 'Report Email' });
    } else {
      response = await sendEmail({
        template: detailedReportEmail(templateData),
        toList: [to],
        subject: 'Report Email',
      });
    }
  } else {
    if (env === 'development' || env === 'local') {
      sendEmailLocally({ template: totalsReportEmail(templateData), toList: [to], subject: 'Report Email' });
    } else {
      response = await sendEmail({ template: totalsReportEmail(templateData), toList: [to], subject: 'Report Email' });
    }
  }

  if (response.success) {
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'Email Sent',
    });
  } else {
    throw new Error(`${response?.message}:${response?.code}`);
  }
};
exports.createTransaction = async (req, res) => {
  let { transaction, startDate, endDate } = req.body;

  if (!transaction) {
    throw new Error('No Transaction Found: Bad Request:400');
  }

  if (transaction.reference_id) {
    await TERMINAL_TRANSACTION.deleteMany({ reference_id: transaction.reference_id });
  }

  function first6andLast4RestHidden(str) {
    return str.slice(0, 6) + '*'.repeat(str.length - 10) + str.slice(-4);
  }
  transaction.card_number = first6andLast4RestHidden(transaction.card_number);

  let count = await TERMINAL_TRANSACTION.countDocuments({
    terminal_id: transaction.terminal_id,
  });

  let invoice_no = 0;
  count = count + 1;
  if (transaction.is_auto_invoice) {
    let countA = await TERMINAL_TRANSACTION.countDocuments({
      terminal_id: transaction.terminal_id,
      is_auto_invoice: true,
    });
    invoice_no = countA + 1;
  } else {
    invoice_no = transaction.invoice_no;
  }

  let trans = await TERMINAL_TRANSACTION.create({ ...transaction, transaction_no: count, invoice_no: invoice_no });
  trans.merchant_name = (await BUSINESS.findOne({ mid: trans.merchant_id }).select('title')).title;
  try {
    trans.terminal_name = (await TERMINAL.findOne({ tid: transaction.terminal_id }).select('title')).title;
  } catch (ex) {
    console.log('error', ex);
  }
  sendToMerchantTools(trans);
  sendToRewards(trans);
  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Transaction Created',
    data: {
      invoice_no: invoice_no,
      transaction_count: count,
    },
  });
};

exports.getTerminalTransactions = async (req, res) => {
  const query = req.body;

  if (query.created_at) {
    if (query.created_at.$gte) {
      query.created_at.$gte = new Date(query.created_at.$gte);
    }
    if (query.created_at.$lte) {
      query.created_at.$lte = new Date(query.created_at.$lte);
    }
  }

  const transactions = await TERMINAL_TRANSACTION.find(query, {
    card_type: 1,
    card_number: 1,
    type: 1,
    date: 1,
    time: 1,
    account_type: 1,
    sale_amount: 1,
    tip_amount: 1,
    surcharge_amount: 1,
    entry_mode: 1,
    invoice_no: 1,
    created_at: 1,
    transaction_no: 1,
    auth_no: 1,
    reference_id: 1,
    status: 1,
    transactionStatus: 1,
    terminal_id: 1,
    merchant_id: 1,
    clerk_id: 1,
    created_at: 1,
  }).sort({ created_at: -1 });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Transactions Fetched',
    data: transactions,
  });
};

exports.getTransactionsMerchantTools = async (req, res) => {
  let merchants = await BUSINESS.find({}).select('mid title').lean();
  merchants = merchants.reduce((acc, curr) => {
    acc[curr.mid] = curr.title;
    return acc;
  }, {});

  let transactions = await TERMINAL_TRANSACTION.find({}).sort({ created_at: -1 }).lean();
  transactions = transactions.map(transaction => {
    return {
      ...transaction,
      merchant_name: merchants[transaction.merchant_id],
    };
  });
  return res.status(200).json(transactions);
};

exports.saveCardDetails = async (req, res) => {
  try {
    const payload = req.body;
    const { rrn, ...rest } = payload;
    if (!rrn) {
      return res.status(400).json({
        message: 'RRN is required',
      });
    }
    const encrypted_data = encryptCBC(JSON.stringify(rest));
    await CARD_DETAILS.create({ rrn: rrn, encrypted_data });
    return res.status(200).json({
      message: 'Card details saved',
    });
  } catch (ex) {
    return res.status(500).json({
      message: ex.message,
    });
  }
};
exports.getCardDetails = async (req, res) => {
  try {
    const rrn = req.params.rrn;
    if (!rrn) {
      return res.status(400).json({
        message: 'RRN in params is required',
      });
    }
    const card = await CARD_DETAILS.findOne({ rrn });
    console.log({ card, rrn });
    if (!card) {
      return res.status(404).json({
        message: 'Card not found',
      });
    }
    const decrypted_data = decryptCBC(card.encrypted_data);
    return res.status(200).json({
      message: 'Card details fetched',
      data: JSON.parse(decrypted_data),
    });
  } catch (ex) {
    return res.status(500).json({
      message: ex.message,
    });
  }
};

exports.getTransactionDetails = async (req, res) => {
  const { rrn } = req.params;
  if (!rrn) {
    return res.status(400).json({
      message: 'RRN is required',
    });
  }
  const transaction = await TERMINAL_TRANSACTION.find({ reference_id: String(rrn) });
  return res.status(200).json({
    message: 'Transaction details fetched',
    data: transaction,
  });
};

exports.toggleDeactivateDevice = async (req, res) => {
  try {
    const { id } = req.params;

    const terminal = await TERMINAL.findById(id)
      .populate({
        model: STORE,
        path: 'store_id',
        select: { status: 1, _id: 1 },
      })
      .populate({ path: 'business_id', model: BUSINESS, select: 'mid' });

    if (!terminal) {
      return res.status(400).send({
        code: 400,
        success: false,
        message: 'Not found!',
      });
    }
    if (terminal?.store_id?.status !== 'Active') {
      throw new Error("You can't do that because store is not Active:400");
    }
    await TERMINAL.findByIdAndUpdate(id, {
      status: terminal.status !== 'Active' ? 'Active' : 'Deactivated',
    });
    sendSocketNotification({
      serial_number: terminal?.serial_number,
      event: terminal.status !== 'Active' ? 'device_activated' : 'device_deactivated',
      data: {
        message: terminal.status !== 'Active' ? 'Device Activated' : 'Device Deactivated',
        serial_number: terminal?.serial_number,
        status: terminal.status !== 'Active' ? 'Active' : 'Deactivated',
        mid: terminal?.business_id?.mid,
      },
    });
    sendSocketNotification({
      serial_number: `status_${terminal?.serial_number}`,
      event: `status_${terminal?.serial_number}`,
      data: {
        store_id: terminal.store_id._id,
        serial_number: terminal?.serial_number,
        mid: terminal?.business_id?.mid,
        status: terminal.status !== 'Active' ? 'Active' : 'Deactivated',
        online: false,
        in_use: false,
        mobile: false,
        shouldRefetchTerminals: true,
        timestamp: new Date().toISOString(),
      },
    });
    res.status(200).send({
      code: 200,
      success: true,
      message: terminal.status !== 'Active' ? 'Device Activated' : 'Device Deactivated',
    });
  } catch (error) {
    return res.status(500).send({
      code: 500,
      success: false,
    });
  }
};
exports.massUploadTids = async (req, res) => {
  try {
    const { serial_number_tids_map } = req.body;
    if (!serial_number_tids_map || Object.keys(serial_number_tids_map).length === 0) {
      return res.status(400).json({
        message: 'serial_number_tids_map is required',
      });
    }
    const serialNumbers = Object.keys(serial_number_tids_map);
    const terminals = await TERMINAL.find({ serial_number: { $in: serialNumbers } }).select(
      '_id original_tid serial_number',
    );
    if (terminals.length === 0) {
      return res.status(404).json({
        message: 'No terminals found for the provided serial numbers',
      });
    }
    const updates = terminals
      .map(terminal => {
        const originalTid = serial_number_tids_map[terminal.serial_number];
        if (!originalTid) {
          return null; // Skip if no original_tid is provided for this serial number
        }
        return TERMINAL.findByIdAndUpdate(terminal._id, { original_tid: originalTid }, { new: true });
      })
      .filter(update => update !== null);
    const updatedTerminals = await Promise.all(updates);
    return res.status(200).json({
      message: 'Terminals updated successfully',
      data: updatedTerminals.map(terminal => ({
        serial_number: terminal.serial_number,
        original_tid: terminal.original_tid,
      })),
    });
  } catch (error) {
    return res.status(500).json({
      message: error.message,
    });
  }
};
