const { getTransactionsByDateRange, getTerminalSettings } = require('../services/dataService');
const {
  calculateCardBrandsTotals,
  calculateTransactionTotals,
  calculateTipTotals,
  calculateClerkServerTotals,
} = require('../utils/reportCalculations');

const generateDailyReport = async (req, res) => {
  try {
    const { startDate, endDate, selectedFilter, terminalId, clerkId = null, cardBrands = false } = req.body;

    const { terminalSettings } = await getTerminalSettings(terminalId);

    const transactions = await getTransactionsByDateRange(startDate, endDate, terminalId);

    let reportData = [];

    switch (selectedFilter) {
      case 'Transactions':
        reportData = await calculateTransactionTotals(transactions, terminalSettings);
        break;
      case 'Clerk/Server':
        reportData = await calculateClerkServerTotals(transactions);
        break;
      case 'Individual Clerk/Server':
        reportData = await calculateClerkServerTotals(transactions, true, clerkId);
        break;
      case 'Card Brands':
        reportData = await calculateCardBrandsTotals(transactions, terminalSettings);
        break;
      case 'Tips':
        reportData = await calculateTipTotals(transactions, clerkId, cardBrands);
        break;
      default:
        reportData = await calculateTransactionTotals(transactions, terminalSettings);
    }

    const response = reportData;

    return res.status(200).json(response);
  } catch (error) {
    console.log(error);
    return res.status(500).json({ error: 'Failed to generate daily report' });
  }
};

module.exports = {
  generateDailyReport,
};
