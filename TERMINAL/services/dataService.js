const { TERMINAL_TRANSACTION, TERMINAL, USER } = require('../models/index');

const getTransactionsByDateRange = async (startDate, endDate, terminalId) => {
  try {
    startDate = new Date(startDate);
    endDate = new Date(endDate);
    const query = {
      created_at: {
        $gte: startDate,
        $lte: endDate,
      },
    };
    if (terminalId) {
      query.terminal_id = terminalId;
    }

    let transactions = await TERMINAL_TRANSACTION.find(query);
    return transactions;
  } catch (error) {
    throw error;
  }
};

const getClerkData = async (terminalId, store_id) => {
  try {
    const users = await USER.find({
      store_id,
      is_deleted: { $ne: true },
      type: {
        $in: ['clerk', 'server'],
      },
    });
    console.log('Fetched users:', users);
    return users.map(user => ({
      clerk_id: user.clerk_id,
      name: `${user.first_name} ${user.last_name}`,
      terminal_id: terminalId,
    }));
  } catch (error) {
    console.error('Error fetching clerk data:', error);
    throw error;
  }
};

const getTerminalSettings = async terminalId => {
  try {
    const terminal = await TERMINAL.findOne(
      { tid: terminalId, is_deleted: { $ne: true } },
      {
        configuration: 1,
        store_id: 1,
        business_id: 1,
        tid: 1,
      },
    ).populate('configuration');
    return {
      terminalSettings: terminal?.configuration?.configuration,
      store_id: terminal?.store_id,
      business_id: terminal?.business_id,
      tid: terminal?.tid,
    };
  } catch (error) {
    console.error('Error fetching terminal settings:', error);
    throw error;
  }
};

module.exports = {
  getTransactionsByDateRange,
  getClerkData,
  getTerminalSettings,
};
