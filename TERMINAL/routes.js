const express = require('express');
require('express-group-routes');
const router = express.Router();

const tryCatch = require('./utils/tryCatch');
const { terminalController } = require('./controllers');
const reportsController = require('./controllers/reportController');
const { IS_ADMIN, HAS_KEY, HAS_API_KEY } = require('./middlewares');

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */

router.group('/v1', router => {
  // PUBLIC ROUTES START

  /**
   * @swagger
   * /terminal/v1/activate-terminal:
   *   post:
   *     tags: [Terminal Management]
   *     summary: Activate terminal
   *     description: Activate a terminal device with provided credentials
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               serial_number:
   *                 type: string
   *                 example: "TRM001234"
   *               activation_code:
   *                 type: string
   *                 example: "ACT123456"
   *             required: [serial_number, activation_code]
   *     responses:
   *       200:
   *         description: Terminal activated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /terminal/v1/activate-terminal:
   *   post:
   *     tags: [Activate Terminal Management]
   *     summary: Create activate terminal
   *     description: Create activate terminal in the terminal service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create activate terminal successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/activate-terminal', tryCatch(terminalController.activateTerminal));

  /**
   * @swagger
   * /terminal/v1/receipt/{id}:
   *   get:
   *     tags: [Terminal Management]
   *     summary: Send receipt via SMS
   *     description: Send transaction receipt to customer via SMS
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         description: Transaction ID
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: Receipt sent successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */

  /**
   * @swagger
   * /terminal/v1/receipt/:id:
   *   get:
   *     tags: [Receipt Management]
   *     summary: Get receipt
   *     description: Retrieve receipt in the terminal service
   *     responses:
   *       200:
   *         description: Get receipt successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
  router.get('/receipt/:id', tryCatch(terminalController.receiptSMS));
  // PUBLIC ROUTES END

  /**
   * @swagger
   * /terminal/v1/create-terminal:
   *   post:
   *     tags: [Terminal Management]
   *     summary: Create new terminal
   *     description: Create a new terminal device in the system
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateTerminalRequest'
   *     responses:
   *       201:
   *         description: Terminal created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /terminal/v1/create-terminal:
   *   post:
   *     tags: [Create Terminal Management]
   *     summary: Create create terminal
   *     description: Create create terminal in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create terminal successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-terminal', IS_ADMIN, tryCatch(terminalController.addTerminal));
  /**
   * @swagger
   * /terminal/v1/update-terminal/{id}:
   *   patch:
   *     tags: [Terminal Management]
   *     summary: Update terminal
   *     description: Update terminal information
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateTerminalRequest'
   *     responses:
   *       200:
   *         description: Terminal updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */

  /**
   * @swagger
   * /terminal/v1/update-terminal/:id:
   *   put:
   *     tags: [Update Terminal Management]
   *     summary: Update update terminal
   *     description: Update update terminal in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update terminal successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.patch('/update-terminal/:id', IS_ADMIN, tryCatch(terminalController.updateTerminal));

  /**
   * @swagger
   * /terminal/v1/delete-terminal/{id}:
   *   delete:
   *     tags: [Terminal Management]
   *     summary: Delete terminal
   *     description: Delete a terminal from the system
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Terminal deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */

  /**
   * @swagger
   * /terminal/v1/delete-terminal/:id:
   *   delete:
   *     tags: [Delete Terminal Management]
   *     summary: Delete delete terminal
   *     description: Delete delete terminal in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete terminal successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-terminal/:id', IS_ADMIN, tryCatch(terminalController.deleteTerminal));

  /**
   * @swagger
   * /terminal/v1/get-all-terminals:
   *   get:
   *     tags: [Terminal Management]
   *     summary: Get all terminals
   *     description: Retrieve a paginated list of all terminals
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Terminals retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/TerminalListResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /terminal/v1/get-all-terminals:
   *   get:
   *     tags: [Get All Terminals Management]
   *     summary: Get get all terminals
   *     description: Retrieve get all terminals in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all terminals successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-terminals', IS_ADMIN, tryCatch(terminalController.getAll));

  /**
   * @swagger
   * /terminal/v1/get-single-terminal/{id}:
   *   get:
   *     tags: [Terminal Management]
   *     summary: Get single terminal
   *     description: Retrieve details of a specific terminal
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Terminal details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Terminal'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */

  /**
   * @swagger
   * /terminal/v1/get-single-terminal/:id:
   *   get:
   *     tags: [Get Single Terminal Management]
   *     summary: Get get single terminal
   *     description: Retrieve get single terminal in the terminal service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get single terminal successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-single-terminal/:id', IS_ADMIN, tryCatch(terminalController.getSingleTerminal));

  /**
   * @swagger
   * /terminal/v1/change-terminal-status/{id}:
   *   put:
   *     tags: [Terminal Management]
   *     summary: Change terminal status
   *     description: Change the status of a terminal (Active/Inactive)
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               status:
   *                 $ref: '#/components/schemas/Status'
   *             required: [status]
   *     responses:
   *       200:
   *         description: Terminal status changed successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */

  /**
   * @swagger
   * /terminal/v1/change-terminal-status/:id:
   *   put:
   *     tags: [Change Terminal Status Management]
   *     summary: Update change terminal status
   *     description: Update change terminal status in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update change terminal status successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/change-terminal-status/:id', IS_ADMIN, tryCatch(terminalController.changeTerminalStatus));

  /**
   * @swagger
   * /terminal/v1/revoke-api/:id:
   *   post:
   *     tags: [Revoke Api Management]
   *     summary: Create revoke api
   *     description: Create revoke api in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create revoke api successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/revoke-api/:id', IS_ADMIN, tryCatch(terminalController.revokeApi));

  /**
   * @swagger
   * /terminal/v1/update-ping-time/:id:
   *   post:
   *     tags: [Update Ping Time Management]
   *     summary: Create update ping time
   *     description: Create update ping time in the terminal service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create update ping time successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/update-ping-time/:id', HAS_KEY, tryCatch(terminalController.updatePingTime));

  /**
   * @swagger
   * /terminal/v1/update-ping-all-device:
   *   post:
   *     tags: [Update Ping All Device Management]
   *     summary: Create update ping all device
   *     description: Create update ping all device in the terminal service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create update ping all device successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/update-ping-all-device', HAS_KEY, tryCatch(terminalController.updatePingTimeOnAllDevices));

  /**
   * @swagger
   * /terminal/v1/force-deactivate-device/:id:
   *   put:
   *     tags: [Force Deactivate Device Management]
   *     summary: Update force deactivate device
   *     description: Update force deactivate device in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update force deactivate device successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/force-deactivate-device/:id', IS_ADMIN, tryCatch(terminalController.forceDeactivateDevice));

  /**
   * @swagger
   * /terminal/v1/tag-string/:id:
   *   put:
   *     tags: [Tag String Management]
   *     summary: Update tag string
   *     description: Update tag string in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update tag string successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/tag-string/:id', IS_ADMIN, tryCatch(terminalController.editTagString));

  /**
   * @swagger
   * /terminal/v1/get-location/:id:
   *   get:
   *     tags: [Get Location Management]
   *     summary: Get get location
   *     description: Retrieve get location in the terminal service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get location successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-location/:id', IS_ADMIN, tryCatch(terminalController.getDeviceLocation));


  /**
   * @swagger
   * /terminal/v1/report:
   *   post:
   *     tags: [Report Management]
   *     summary: Create report
   *     description: Create report in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create report successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/report', HAS_API_KEY, tryCatch(terminalController.saveDeviceReport));

  /**
   * @swagger
   * /terminal/v1/report/:store_id:
   *   get:
   *     tags: [Report Management]
   *     summary: Get report
   *     description: Retrieve report in the terminal service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get report successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/report/:store_id', HAS_API_KEY, tryCatch(terminalController.getReports));


  /**
   * @swagger
   * /terminal/v1/create-transaction:
   *   post:
   *     tags: [Create Transaction Management]
   *     summary: Create create transaction
   *     description: Create create transaction in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create transaction successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-transaction', HAS_API_KEY, tryCatch(terminalController.createTransaction));

  /**
   * @swagger
   * /terminal/v1/get-terminal-transactions:
   *   post:
   *     tags: [Get Terminal Transactions Management]
   *     summary: Create get terminal transactions
   *     description: Create get terminal transactions in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create get terminal transactions successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/get-terminal-transactions', HAS_API_KEY, tryCatch(terminalController.getTerminalTransactions));

  // used by device

  /**
   * @swagger
   * /terminal/v1/send-receipt-email:
   *   post:
   *     tags: [Send Receipt Email Management]
   *     summary: Create send receipt email
   *     description: Create send receipt email in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send receipt email successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/send-receipt-email', HAS_API_KEY, tryCatch(terminalController.sendReceiptEmail));

  /**
   * @swagger
   * /terminal/v1/send-receipt-sms:
   *   post:
   *     tags: [Send Receipt Sms Management]
   *     summary: Create send receipt sms
   *     description: Create send receipt sms in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send receipt sms successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/send-receipt-sms', HAS_API_KEY, tryCatch(terminalController.sendReceiptNumber));

  /**
   * @swagger
   * /terminal/v1/report-email:
   *   post:
   *     tags: [Report Email Management]
   *     summary: Create report email
   *     description: Create report email in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create report email successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/report-email', HAS_API_KEY, tryCatch(terminalController.sendReportEmail));

  // grouping

  /**
   * @swagger
   * /terminal/v1/create-device-group:
   *   post:
   *     tags: [Create Device Group Management]
   *     summary: Create create device group
   *     description: Create create device group in the terminal service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create device group successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-device-group', IS_ADMIN, tryCatch(terminalController.createDeviceGroup));

  /**
   * @swagger
   * /terminal/v1/delete-device-group/:id:
   *   delete:
   *     tags: [Delete Device Group Management]
   *     summary: Delete delete device group
   *     description: Delete delete device group in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete device group successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-device-group/:id', IS_ADMIN, tryCatch(terminalController.deleteDeviceGroup));

  /**
   * @swagger
   * /terminal/v1/get-group-details/:id:
   *   get:
   *     tags: [Get Group Details Management]
   *     summary: Get get group details
   *     description: Retrieve get group details in the terminal service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get group details successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-group-details/:id', IS_ADMIN, tryCatch(terminalController.getGroupDetails));

  /**
   * @swagger
   * /terminal/v1/update-device-group/:id:
   *   put:
   *     tags: [Update Device Group Management]
   *     summary: Update update device group
   *     description: Update update device group in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update device group successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-device-group/:id', IS_ADMIN, tryCatch(terminalController.updateDeviceGroup));

  /**
   * @swagger
   * /terminal/v1/get-all-device-groups:
   *   get:
   *     tags: [Get All Device Groups Management]
   *     summary: Get get all device groups
   *     description: Retrieve get all device groups in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all device groups successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-device-groups', IS_ADMIN, tryCatch(terminalController.getAllDevicesGroups));

  router.get('/transactions-merchant-tools', HAS_KEY, terminalController.getTransactionsMerchantTools);

  router.post('/save-card-details', HAS_API_KEY, terminalController.saveCardDetails);
  router.get('/get-card-details/:rrn', HAS_API_KEY, terminalController.getCardDetails);
  router.get('/get-transaction-details/:rrn', HAS_API_KEY, terminalController.getTransactionDetails);


  /**
   * @swagger
   * /terminal/v1/deactivate-device/:id:
   *   put:
   *     tags: [Deactivate Device Management]
   *     summary: Update deactivate device
   *     description: Update deactivate device in the terminal service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update deactivate device successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/deactivate-device/:id', IS_ADMIN, tryCatch(terminalController.toggleDeactivateDevice));
  router.post('/mass-upload-tids', terminalController.massUploadTids);

  router.post(
    '/reports/daily',
    // HAS_API_KEY,
    reportsController.generateDailyReport,
  );
});

module.exports = router;
