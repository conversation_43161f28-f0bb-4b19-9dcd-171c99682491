{"name": "STORE", "version": "1.0.1", "description": "PSP backend", "main": "server.js", "scripts": {"start": "node -r dotenv/config ./server.js dotenv_config_path=./.env", "start:local": "node --watch -r dotenv/config . dotenv_config_path=./.env.local", "start:dev": "node --watch -r dotenv/config . dotenv_config_path=./.env.dev", "start:staging": "node --watch -r dotenv/config . dotenv_config_path=./.env.staging", "start:prod": "node --watch -r dotenv/config . dotenv_config_path=./.env.prod"}, "license": "ISC", "dependencies": {"@sendgrid/mail": "^8.1.4", "axios": "^1.4.0", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-group-routes": "^1.1.0", "express-useragent": "^1.0.15", "helmet": "^6.0.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^3.1.2", "mongoose": "^6.8.0", "mongoose-unique-validator": "^3.1.0", "morgan": "^1.10.0", "nodemailer": "^6.8.0"}}