//this is a worker to send data to psp server
const net = require('net');
const { workerData, parentPort } = require('worker_threads');

const socket = net.createConnection(
  {
    host: '***************',
    port: 33666,
    timeout: 10000,
  },
  () => {
    console.log('Connected to server');
    console.log('on API 2 ', { type: typeof workerData }, { bytesData: workerData });
    socket.write(workerData, 'hex');
    console.log(`Bytes written to socket: ${socket.bytesWritten}`);
  },
);

socket.on('drain', () => {
  console.log('All data has been sent to server');
});

socket.on('timeout', () => {
  socket.destroy();
  parentPort.postMessage({
    message: 'Timeout server',
    error: true,
  });
});

socket.on('error', () => {
  parentPort.postMessage({
    message: 'Disconnected from server',
    error: true,
  });
  socket.destroy();
});

socket.on('data', data => {
  console.log('Received data from server', data);
  parentPort.postMessage({
    message: 'Disconnected Successfully',
    data: data,
    error: false,
  });
  socket.destroy();
});
