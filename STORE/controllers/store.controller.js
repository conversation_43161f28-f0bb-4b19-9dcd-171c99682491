const { mongoose } = require('mongoose');
const {
  STORE,
  USER,
  TERMINAL,
  BUSINESS,
  USER_GROUP,
  TERMINAL_GROUP,
  RESTRICTED_PINS,
  RESTRICTED_PINS_LOGS,
  CONFIGURATION,
  TAG_TEMPLATE,
  BIN_RANGE,
} = require('../models');
const {
  filterQuery,
  pagination,
  sendEmail,
  generateRandomClerkId,
  createBINRanges,
  sendSocketNotification,
  sendEmailLocally,
  pushDataToQueue,
} = require('../utils/helper');
const { ownerCreated } = require('../utils/templates/user-created');
const { env } = require('../configs');
const { default: axios } = require('axios');

exports.addStore = async (req, res) => {
  let { title, business_id, first_name, last_name, email, passcode, tag_template, ...rest } = req.body;
  email = email.toLowerCase().trim();

  const store = await STORE.findOne({ title, business_id });

  if (store) {
    throw new Error('Store Already Exists!:409');
  }

  const business = await BUSINESS.findById(business_id);

  if (business?.status !== 'Active') {
    throw new Error('Cannot create Store because Business associated with the Store is not active!:400');
  }

  const ownerName = `${first_name} ${last_name}, ${email}`;
  const template_id = tag_template.split(',')[0];
  const createdStore = await STORE.create({
    title,
    business_id,
    owner: ownerName,
    passcode: passcode,
    tag_template: template_id,
    ...rest,
  });

  let usertoSave = {};
  usertoSave.clerk_id = await generateRandomClerkId(4);

  usertoSave = {
    ...usertoSave,
    first_name,
    last_name,
    email,
    type: 'owner',
    business_id: createdStore?.business_id,
    store_id: createdStore?._id,
  };

  usertoSave = await USER.create(usertoSave);

  const dataForEmail = {
    owner_id: usertoSave.clerk_id,
    name: usertoSave.first_name + usertoSave.last_name,
    email: usertoSave.email,
  };

  if (env === 'development' || env === 'local') {
    sendEmailLocally({
      template: ownerCreated(dataForEmail),
      toList: [usertoSave.email],
      subject: 'Owner Created',
    });
  } else {
    sendEmail({
      template: ownerCreated(dataForEmail),
      toList: [usertoSave.email],
      subject: 'Owner Created',
    });
  }

  await createBINRanges({
    store_id: createdStore._id,
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Store Created!',
    createdStore,
  });
};

exports.getAllStores = async (req, res) => {
  const { page, itemsPerPage, searchText, status, startDate, endDate, businessId, getDevices, getDeleted } =
    filterQuery(req);

  if (getDevices === 'true') {
    const pipeline = [];

    if (businessId && businessId !== '') {
      pipeline.push({
        $match: {
          business_id: mongoose.Types.ObjectId(businessId),
        },
      });
    }

    pipeline.push(
      {
        $match: {
          is_deleted: false,
        },
      },
      {
        $lookup: {
          from: 'terminals',
          let: { storeId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$store_id', '$$storeId'] }, { $eq: ['$is_deleted', false] }],
                },
              },
            },
          ],
          as: 'terminals',
        },
      },
      {
        $lookup: {
          from: 'users',
          let: { storeId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$store_id', '$$storeId'] }, { $eq: ['$is_deleted', false] }],
                },
              },
            },
          ],
          as: 'users',
        },
      },
      {
        $skip: (page - 1) * itemsPerPage,
      },
      {
        $limit: itemsPerPage,
      },
    );

    const stores = await STORE.aggregate(pipeline);

    const records = pagination(stores, page, stores?.length, itemsPerPage);

    return res.status(200).send({
      ...records,
      code: 200,
      success: true,
      message: 'All Stores!',
    });
  }

  const query = {
    $and: [],
    $or: [],
  };

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [{ title: regExp }, { address: regExp }, { owner: regExp }];
  }

  if (status && status !== '') {
    query.$and.push({
      status: { $eq: status },
    });
  }

  if (startDate && endDate) {
    let start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    let end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    query.$and.push({ created_at: { $gte: start, $lt: end } });
  }

  if (businessId && businessId != '') {
    query.$and.push({
      business_id: mongoose.Types.ObjectId(businessId),
    });
  }

  if (getDeleted && getDeleted !== '') {
    query.$and.push({ is_deleted: JSON.parse(getDeleted) });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }

  if (!query.$or.length > 0) {
    delete query.$or;
  }

  const totalItems = await STORE.countDocuments(query);

  let stores = await STORE.find(query)
    .populate({ path: 'tag_template', model: TAG_TEMPLATE })
    .sort([
      ['is_deleted', 1],
      ['status', 1],
      ['created_at', -1],
    ])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .lean();

  stores = await Promise.all(
    stores.map(async ({ _id: store_id, ...rest }) => {
      let binRanges = await RESTRICTED_PINS.findOne({
        store_id,
      });
      const data = {
        binRanges,
        _id: store_id,
      };
      if (rest.report_print_device && rest.report_print_device !== '') {
        let reportDeviceDetail = await TERMINAL.findOne({ _id: rest.report_print_device }).select([
          'title',
          'serial_number',
        ]);
        data.reportDeviceDetail = reportDeviceDetail;
      }
      return { ...data, ...rest };
    }),
  );

  const records = pagination(stores, page, totalItems, itemsPerPage);

  return res.status(200).send({
    ...records,
    code: 200,
    success: true,
    message: 'All Stores!',
  });
};

exports.editStore = async (req, res) => {
  const { id } = req.params;
  let payload = req.body;

  const counter = await STORE.findById(id);

  if (!counter) {
    throw new Error('Store Does Not Exists!:404');
  }

  let store = {};

  Object.keys(payload).forEach(element => {
    store[element] = payload[element];
  });
  await STORE.findByIdAndUpdate(id, { $set: { ...store, tag_template: store.tag_template.split(',')[0] } });

  if (counter.report_print_time !== payload.report_print_time) {
    const terminals = await TERMINAL.find({ store_id: id, is_deleted: false })
      .select(['_id', 'configuration', 'serial_number'])
      .lean();
    for (const t of terminals) {
      const updatedConfig = await CONFIGURATION.findByIdAndUpdate(
        t.configuration,
        {
          $set: { 'configuration.printer.auto_print.fee': payload.report_print_time },
        },
        { new: true },
      );

      pushDataToQueue({ serial_number: t.serial_number, configuration: updatedConfig.configuration });
    }
  }
  if (counter.passcode && counter.passcode != store.passcode) {
    let _store = await STORE.findById(counter._id).select(['title', 'address', 'passcode']).lean();
    let users = await USER.find({ store_id: counter._id, status: 'Active' })
      .select(['first_name', 'last_name', 'email', 'type', 'status', 'clerk_id'])
      .lean();
    users = users.map(user => {
      const name = `${user?.first_name} ${user?.last_name}`;
      delete user?.first_name;
      delete user?.last_name;
      const updatedUser = {
        ...user,
        name,
        passcode: _store.passcode,
      };
      return updatedUser;
    });

    sendSocketNotification({
      store_id: counter._id,
      event: 'passcode_changed',
      data: { users },
    });
  } else if (counter && counter.title != store.title) {
    let _store = await STORE.findById(counter._id).select(['title', 'address', 'passcode']).lean();

    sendSocketNotification({
      store_id: counter._id,
      event: 'store_updated',
      data: { name: _store.title },
    });

    const terminals = await TERMINAL.find({ store_id: id, is_deleted: false })
      .select(['_id', 'configuration', 'serial_number'])
      .lean();
    for (const t of terminals) {
      await CONFIGURATION.findByIdAndUpdate(t.configuration, {
        $set: { 'configuration.printer.receipt_header_lines.line_2': store.title },
      });
    }
  } else if (counter && counter.tag_template != store.tag_template.split(',')[0]) {
    const updatedStore = await TAG_TEMPLATE.findById(store.tag_template.split(',')[0]);

    const query = {
      store_id: id,
      is_deleted: false,
      $or: [{ tagStringFormat: 'default' }, { tagStringFormat: { $exists: false } }],
    };

    const terminalsToSendSocketNotification = await TERMINAL.find(query).select('serial_number').lean();

    await TERMINAL.updateMany(query, {
      $set: {
        tagString: store.tag_template.split(',')[1],
        tag_template: store.tag_template.split(',')[0],
        tags: updatedStore.tags,
        tagStringFormat: 'default',
      },
    });
    terminalsToSendSocketNotification.forEach(_ => {
      sendSocketNotification({
        serial_number: _?.serial_number,
        event: 'tag_updated',
        data: { tagString: store.tag_template.split(',')[1] },
      });
    });
  }
  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Store Updated!',
  });
};

exports.deleteStore = async (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;

  const store = await STORE.findById(id);

  if (!store) {
    throw new Error('Store Does Not Exists Or Already Deleted:404');
  }

  await STORE.findByIdAndUpdate(id, {
    is_deleted: true,
    status: 'Deactivated',
    deactivation_reason: reason,
  });
  await USER.updateMany({ store_id: id }, { $set: { is_deleted: true, status: 'Deactivated' }, user_id: [] });
  await USER_GROUP.deleteMany({ store_id: id });
  await TERMINAL.updateMany(
    { store_id: id },
    {
      $set: { is_deleted: true, api_key: '', status: 'Deactivated' },
      $unset: { group_id: 1 },
    },
  );
  await TERMINAL_GROUP.deleteMany({ store_id: id });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Store Deactivated!',
  });
};

exports.getStoreDetails = async (req, res) => {
  const { id } = req.params;

  const store = await STORE.aggregate([
    { $match: { _id: mongoose.Types.ObjectId(id) } },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: 'store_id',
        as: 'users',
      },
    },
    {
      $lookup: {
        from: 'terminals',
        localField: '_id',
        foreignField: 'store_id',
        as: 'terminals',
      },
    },
  ]);

  if (!store) {
    throw new Error('Store Does Not Exists:404');
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Store Details!',
    store: store[0],
  });
};

exports.changeStoreStatus = async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  const store = await STORE.findById(id).populate({
    model: BUSINESS,
    path: 'business_id',
    select: { status: 1 },
  });

  if (!store) {
    throw new Error('Store Does Not Exists:404');
  }

  if (store?.business_id?.status !== 'Active') {
    throw new Error("You can't do that because Business associated with this Store is not Active:400");
  }

  await STORE.findByIdAndUpdate(id, { status });

  if (status !== 'Active') {
    await TERMINAL.updateMany({ store_id: id }, { status: status });
    await USER.updateMany({ store_id: id }, { status });
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Store Status Changed Successfully!',
  });
};

exports.updatePasscode = async (req, res) => {
  const { store_id } = req.terminal;
  const { passcode } = req.body;

  if (!passcode) {
    throw new Error('Payload empty:400');
  }

  const store = await STORE.findById(store_id);

  if (!store) {
    throw new Error('Store Does Not Exists!:404');
  }

  await STORE.findByIdAndUpdate(store_id, { passcode });
  let _store = await STORE.findById(store_id).select(['title', 'address', 'passcode']).lean();
  let users = await USER.find({ store_id: store_id, status: 'Active' })
    .select(['first_name', 'last_name', 'email', 'type', 'status', 'clerk_id'])
    .lean();
  users = users.map(user => {
    const name = `${user?.first_name} ${user?.last_name}`;
    delete user?.first_name;
    delete user?.last_name;
    const updatedUser = {
      ...user,
      name,
      passcode: _store.passcode,
    };
    return updatedUser;
  });

  sendSocketNotification({
    store_id: _store._id,
    event: 'passcode_changed',
    data: { users },
  });
  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Store Passcode Updated Successfully!',
  });
};

exports.updateBinRanges = async (req, res) => {
  const { binRanges, storeId } = req.body;

  let found = await RESTRICTED_PINS.find({
    created_at: {
      $lte: 1686548838359,
    },
  });

  let old_pin = await RESTRICTED_PINS.findOne({
    store_id: storeId,
  });

  const log = await RESTRICTED_PINS_LOGS.create({
    store_id: storeId,
    previous: old_pin?.pins,
    new: { pins: binRanges },
    email: req?.admin?.email,
  });

  console.log('%cSTOREcontrollersstore.controller.js:528 log', 'color: #007acc;', log);

  if (found.length > 0) {
    await RESTRICTED_PINS.collection.drop();
  }

  found = await RESTRICTED_PINS.findOne({
    store_id: storeId,
  });

  if (found) {
    await RESTRICTED_PINS.findOneAndUpdate(
      {
        store_id: storeId,
      },
      {
        $set: {
          pins: binRanges,
        },
      },
    );
  } else {
    await RESTRICTED_PINS.create({
      store_id: storeId,
      pins: binRanges,
    });
  }

  // send a socket event 'bin_ranges_updated' to all terminals related to this store
  let terminalSerialNumbers = await TERMINAL.find({
    store_id: storeId,
    is_deleted: false,
    api_key: { $ne: '' },
  }).select('serial_number');

  if (terminalSerialNumbers.length > 0) {
    const restricted_pins = await RESTRICTED_PINS.findOne({
      store_id: storeId,
    }).select('pins');

    terminalSerialNumbers = terminalSerialNumbers.map(_ => _.serial_number);

    terminalSerialNumbers.forEach(_ => {
      sendSocketNotification({
        serial_number: _,
        event: 'bin_ranges_updated',
        data: { bins: restricted_pins?.pins ?? [] },
      });
    });
  }
  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Bin Ranges Updated Successfully!',
  });
};
exports.updateBatchReportSetting = async (req, res) => {
  const { id } = req.params;

  const { report_receive_type, report_type, isMerged, selectedTerminal, isSeparate } = req.body;

  const store = await STORE.findById(id);

  if (!store) {
    throw new Error('Store Not Found!:404');
  }

  const data = { report_receive_type, report_type };

  if (isMerged) {
    data.report_print_device = selectedTerminal.split(',')[0];
    data.is_report_merged = isMerged;
  } else if (isSeparate) {
    data.report_print_device = '';
    data.is_report_merged = false;
  }
  await STORE.findByIdAndUpdate(id, {
    $set: data,
  });

  const storeTerminals = await TERMINAL.find({ store_id: store._id, is_deleted: false, status: 'Active' });

  let isAllPrintedByMe = false;

  for (const terminal of storeTerminals) {
    const deviceId = selectedTerminal ? selectedTerminal.split(',')[0] : '';

    if (deviceId === terminal._id.toString()) {
      isAllPrintedByMe = true;
    }
    const config = {
      'configuration.printer.receive_type.fee': report_receive_type,
      'configuration.printer.report_type.fee': report_type,
      'configuration.printer.auto_print_all.value': isAllPrintedByMe,
      'configuration.printer.is_printed_by_me.value': isAllPrintedByMe,
    };
    const updatedConfig = await CONFIGURATION.findByIdAndUpdate(
      terminal.configuration,
      {
        $set: config,
      },
      { new: true },
    );

    pushDataToQueue({ serial_number: terminal.serial_number, configuration: updatedConfig.configuration });
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Setting Updated Successfully!',
  });
};

exports.getGlobalBinRanges = async (req, res) => {
  const binRanges = await BIN_RANGE.findOne({}).sort({ created_at: -1 });

  if (!binRanges) {
    throw new Error('No Data Found!');
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Bin Ranges',
    data: binRanges,
  });
};

exports.updateGlobalBinRanges = async (req, res) => {
  let { pins } = req.body;
  pins = pins.map(pin => {
    return {
      ...pin,
      ranges: pin.ranges.map(range => {
        return range.toString();
      }),
    };
  });
  let binRanges = await BIN_RANGE.findOne({}).sort({ created_at: -1 });
  if (!binRanges) {
    binRanges = await BIN_RANGE.create({
      pins,
    });
  } else {
    binRanges = await BIN_RANGE.findByIdAndUpdate(binRanges._id, { pins }, { new: true });
  }

  const allStoresPins = await RESTRICTED_PINS.find({}).lean();
  const mapStorePins = {};

  const updateOperations = allStoresPins.map(async pin => {
    let newPins = [];

    for (let i = 0; i < binRanges.pins.length; i++) {
      const existingPin = pin.pins.find(p => p.card.toLowerCase() === binRanges.pins[i].card.toLowerCase());

      if (existingPin) {
        newPins.push({
          ...binRanges.pins[i],
          active: existingPin.active,
        });
      } else {
        newPins.push({
          ...binRanges.pins[i],
          active: false,
        });
      }
    }

    await RESTRICTED_PINS.findByIdAndUpdate(pin._id, { $set: { pins: newPins } });

    const terminalSerialNumbers = await TERMINAL.find({
      store_id: pin.store_id,
      is_deleted: false,
      api_key: { $ne: '' },
    }).select('serial_number');

    mapStorePins[pin.store_id] = {
      terminals: terminalSerialNumbers.map(terminal => terminal.serial_number),
      newPins,
    };
    return true;
  });

  await Promise.all(updateOperations);

  Object.entries(mapStorePins).forEach(([store_id, { terminals, newPins }]) => {
    terminals.forEach(serialNumber => {
      sendSocketNotification({
        serial_number: serialNumber,
        event: 'bin_ranges_updated',
        data: { bins: newPins.filter(_ => _.active) },
      });
    });
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Bin Ranges Updated!',
  });
};

exports.checkBinRange = async (req, res) => {
  try {
    const pan = req.body.pan;

    const response = await axios.post(
      `https://bin-ip-checker.p.rapidapi.com/?bin=${pan}`,
      {
        bin: pan,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-rapidapi-host': 'bin-ip-checker.p.rapidapi.com',
          'x-rapidapi-key': '**************************************************',
        },
      },
    );

    let valid = response?.data?.BIN?.valid && response?.data?.BIN?.country?.name?.toLowerCase() === 'canada';
    if (valid) {
      const [card, range] = [response?.data?.BIN?.brand?.toLowerCase(), response?.data?.BIN?.number];
      const binRanges = await BIN_RANGE.findOne({}).sort({ created_at: -1 });
      const newBinRanges = binRanges.pins.map((_, id) => {
        if (_.card?.toLowerCase() === card) {
          _.ranges.push(range.toString());
        }
        return _;
      });

      await BIN_RANGE.findByIdAndUpdate(binRanges._id, { pins: newBinRanges });
    }
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'Bin Range Checked!',
      data: { ...response?.data?.BIN, number: response?.data?.BIN?.number?.toString() },
      ...response?.data?.BIN,
      number: response?.data?.BIN?.number.toString(),
    });
  } catch (ex) {
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'Bin Range Checked!',
      data: { valid: false, err_message: ex.message },
    });
  }
};

exports.getBinRangesDetails = async (req, res) => {
  const binRangesNew = await BIN_RANGE.findOne({}).sort({ created_at: -1 }).lean();
  const binRangesOld = await BIN_RANGE.findOne({ createdAt: -1 }).lean();

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Bin Ranges',
    data: {
      binRangesNew,
      binRangesOld,
    },
  });
};
