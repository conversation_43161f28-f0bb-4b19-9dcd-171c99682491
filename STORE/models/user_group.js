const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const userGroupSchema = new Schema(
  {
    name: {
      type: String,
      unique: true,
    },
    is_deleted: {
      type: Boolean,
      default: false,
    },
    store_id: {
      type: Schema.Types.ObjectId,
      ref: 'store',
      required: true,
    },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

userGroupSchema.plugin(uniqueValidator);

const USER_GROUP = model('user_group', userGroupSchema);
module.exports = USER_GROUP;
