const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const userSchema = new Schema(
  {
    first_name: { type: String, required: true },
    last_name: { type: String, required: true },
    email: { type: String },
    type: {
      type: String,
      enum: {
        values: ['owner', 'manager', 'clerk', 'server'],
        message: 'Type must be one of owner, manager, clerk or server',
      },
      default: 'clerk',
    },
    status: {
      type: String,
      default: 'Active',
    },
    is_deleted: {
      type: Boolean,
      default: false,
    },
    clerk_id: {
      type: Number,
      default: 1000,
      unique: true,
    },

    store_id: { type: Schema.Types.ObjectId, ref: 'store', required: true },
    business_id: {
      type: Schema.Types.ObjectId,
      ref: 'business',
      required: true,
    },
    group_id: [{ type: Schema.Types.ObjectId, ref: 'user_group' }],
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

userSchema.plugin(uniqueValidator);

const USER = model('user', userSchema);
module.exports = USER;
