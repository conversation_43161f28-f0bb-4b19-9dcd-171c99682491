const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const emvTagSchema = new Schema(
  {
    tag_name: {
      type: String,
    },
    tag: {
      type: String,
      required: true,
      unique: true,
    },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

emvTagSchema.plugin(uniqueValidator);

const EMV_TAG = model('emv_tag', emvTagSchema);
module.exports = EMV_TAG;
