const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const restricted_pins_logs = new Schema(
  {
    email: { type: String, required: true },
    store_id: { type: String, required: true },
    new: [Object],
    previous: [Object],
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

restricted_pins_logs.plugin(uniqueValidator);

const RESTRICTED_PINS_LOGS = model('restricted_pins_logs', restricted_pins_logs);

module.exports = RESTRICTED_PINS_LOGS;
