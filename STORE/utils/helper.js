const sgMail = require('@sendgrid/mail');
sgMail.setApiKey(process.env.SENDGRID_API_KEY);
const axios = require('axios');
const { USER, RESTRICTED_PINS, BIN_RANGE } = require('../models');
const nodemailer = require('nodemailer');
const {
  sendgrid_api_key,
  sendgrid_user_name,
  sendgrid_port,
  sendgrid_host,
  sendgrid_email_from,
  access_key,
  mail_host,
  mail_port,
  mail_username,
  mail_password,
  mail_from_address,
  mail_from_name,

  base_url,
} = require('../configs');
module.exports = {
  sendEmail: async ({ template, toList, subject, attachment = null }) => {
    try {
      const msg = {
        to: toList,
        from: sendgrid_email_from,
        subject: subject,
        html: template,
        tls: {
          ciphers: 'SSLv3',
          rejectUnauthorized: false,
        },
        host: sendgrid_host,
        port: sendgrid_port,
        auth: {
          user: sendgrid_user_name,
          pass: sendgrid_api_key,
        },
      };

      await sgMail.send(msg);
      return { code: 200, message: 'email sent', success: true };
    } catch (err) {
      return { code: err.code, message: err.message, success: false };
    }
  },
  genRandom: length => {
    let zero = '';
    for (let index = 1; index < length; index++) {
      zero += '0';
    }

    let firstVal = 1 + zero;
    let secondVal = 9 + zero;

    return Math.floor(Number(firstVal) + Math.random() * Number(secondVal));
  },
  pagination: (items = [], page = 1, totalItems = 0, itemsPerPage = 5) => {
    return {
      currentPage: page,
      hasNextPage: itemsPerPage * page < totalItems,
      hasPreviousPage: page > 1,
      nextPage: page + 1,
      previousPage: page - 1,
      lastPage: Math.ceil(totalItems / itemsPerPage),
      totalItems: totalItems,
      items: items,
    };
  },
  filterQuery: req => ({
    ...req.query,
    page: req.query.page ? Number(req.query.page) : 1,
    itemsPerPage: req.query.itemsPerPage
      ? Number(req.query.itemsPerPage)
      : req.query.perPage
        ? Number(req.query.perPage)
        : 10,
    searchText:
      req.query.searchText !== 'null' && req.query.searchText !== 'undefined' && req.query.searchText
        ? req.query.searchText
        : '',
    startDate:
      req.query.startDate !== 'null' && req.query.startDate !== 'undefined' && req.query.startDate
        ? req.query.startDate
        : '',
    endDate:
      req.query.endDate !== 'null' && req.query.endDate !== 'undefined' && req.query.endDate ? req.query.endDate : '',
    storeId:
      req.query.storeId !== 'null' && req.query.storeId !== 'undefined' && req.query.storeId ? req.query.storeId : '',
  }),
  readAllSchemaFiles: async () => {
    const fs = require('fs');
    const path = require('path');
    const createCsvWriter = require('csv-writer').createObjectCsvWriter;

    const directoryPath = path.join(__dirname, '../confirmed_models');
    const csvPath = path.join(__dirname, '../utils/csv');

    const files = await new Promise((resolve, reject) => {
      fs.readdir(directoryPath, (err, files) => {
        if (err) {
          reject(err);
        } else {
          resolve(files);
        }
      });
    });

    files.forEach(async file => {
      let csvFileName = file.replace('.js', '.csv');
      const filePath = path.join(directoryPath, file);

      // Require each file
      let model = require(filePath);
      if (typeof model === 'function') {
        const newObject = Object.entries(model.schema.paths).reduce((acc, [key, payload]) => {
          acc.push({
            params: key,
            requred: payload?.isRequired ? 'Y' : 'O',
            unique: payload?.options?.unique ? 'Y' : 'N',
            data_type: payload?.instance,
            enum_values: payload?.enumValues?.length ? payload?.enumValues : '',
            default_values: payload?.options?.default ? payload?.options?.default : '',
          });
          return acc;
        }, []);

        let csvWritePath = path.join(csvPath, csvFileName);

        const csvWriter = createCsvWriter({
          path: csvWritePath,
          header: Object.keys(newObject[0]).map(key => ({
            id: key,
            title: key.toUpperCase(),
          })),
        });
        await csvWriter.writeRecords(newObject);
      }
    });
  },
  generateRandomClerkId: async length => {
    const number = module.exports.genRandom(length);
    const user = await USER.findOne({ clerk_id: number });

    if (user) {
      return module.exports.generateRandomClerkId(length);
    }
    return number;
  },
  createBINRanges: async ({ store_id }) => {
    try {
      const DefaultBinRanges = await BIN_RANGE.findOne({}).sort({ createdAt: -1 }).lean();
      const restricted_pins = DefaultBinRanges.pins.map(_ => {
        return { card: _.card, ranges: _.ranges, active: true };
      });

      await RESTRICTED_PINS.create({
        store_id,
        pins: restricted_pins,
      });
    } catch (Ex) {
      console.log({
        BIN_RANGES_EXCEPTION: Ex.message,
      });
    }
  },
  sendSocketNotification: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };

      await axios.post(`${base_url}/common/send-socket-notification`, payload, {
        headers,
      });
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },
  sendEmailLocally: ({ template, toList, subject, attachment = null }) => {
    try {
      let transporter = nodemailer.createTransport({
        host: mail_host,
        port: mail_port,
        secure: false,
        auth: {
          user: mail_username,
          pass: mail_password,
        },
        tls: {
          rejectUnauthorized: false,
        },
      });
      transporter.sendMail({
        from: `${mail_from_name} <${mail_from_address}>`,
        to: toList,
        replyTo: null,
        subject,
        html: template,
        attachments: attachment,
      });
    } catch (error) {
      throw new Error(`${error.message}:code`);
    }
  },
  pushDataToQueue: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };

      const response = await axios.post(`${base_url}/common/push-data-queue`, payload, {
        headers,
      });

      return response;
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },
  createGlobalBinRanges: async () => {
    try {
      const binRanges = await BIN_RANGE.findOne({}).sort({ created_at: -1 }).lean();

      if (binRanges) {
        console.log('Global Bin Ranges Exists');
        return;
      }

      const DefaultBinRanges = [
        { card: 'Visa', ranges: ['4'] },
        { card: 'JCB', ranges: ['3528 - 3589'] },
        { card: 'American Express', ranges: ['34', '37'] },
        { card: 'MasterCard', ranges: ['2221-2720', '50 - 55'] },
        { card: 'Interac', ranges: ['45', '47', '58'] },
      ];

      await BIN_RANGE.create({ pins: DefaultBinRanges });
    } catch (Ex) {
      console.log({
        BIN_RANGES_EXCEPTION: Ex.message,
      });
    }
  },
};
