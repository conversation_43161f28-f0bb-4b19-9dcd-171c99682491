/**
 * @fileoverview OpenAPI schemas specific to STORE service
 */

module.exports = {
  Store: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      name: {
        type: 'string',
        example: 'Sample Store',
        description: 'Store name'
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['name']
  },

  CreateStoreRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'New Store',
        description: 'Store name'
      },
      description: {
        type: 'string',
        example: 'Store description',
        description: 'Store description'
      }
    },
    required: ['name']
  },

  UpdateStoreRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Updated Store',
        description: 'Store name'
      },
      description: {
        type: 'string',
        example: 'Updated description',
        description: 'Store description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  StoreListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Store'
            }
          }
        }
      }
    ]
  }
};