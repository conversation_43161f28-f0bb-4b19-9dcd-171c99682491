/**
 * @fileoverview OpenAPI configuration for STORE service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const storeSchemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for STORE service
 * @param {Object} app - Express app instance
 */
function setupStoreDocs(app) {
  const config = {
    serviceName: 'STORE',
    serviceDescription: `
      Store management and configuration service

      Key features:
      - Entity management
      - Authentication and authorization
      - Data validation and processing
    `,
    version: '1.0.0',
    port: 4002,
    basePath: '/store',
    additionalSchemas: storeSchemas,
    docsPath: '/store/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  setupStoreDocs
};