const express = require('express');
require('express-group-routes');
const router = express.Router();

const tryCatch = require('./utils/tryCatch');
const { storeController } = require('./controllers');
const { IS_ADMIN, HAS_API_KEY } = require('./middlewares');

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 */


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */

router.group('/v1', router => {
  /**
   * @swagger
   * /store/v1/create-store:
   *   post:
   *     tags: [Store Management]
   *     summary: Create new store
   *     description: Create a new store in the system
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateStoreRequest'
   *     responses:
   *       201:
   *         description: Store created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /store/v1/create-store:
   *   post:
   *     tags: [Create Store Management]
   *     summary: Create create store
   *     description: Create create store in the store service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create store successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-store', IS_ADMIN, tryCatch(storeController.addStore));

  /**
   * @swagger
   * /store/v1/get-all-stores:
   *   get:
   *     tags: [Store Management]
   *     summary: Get all stores
   *     description: Retrieve a paginated list of all stores
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Stores retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/StoreListResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /store/v1/get-all-stores:
   *   get:
   *     tags: [Get All Stores Management]
   *     summary: Get get all stores
   *     description: Retrieve get all stores in the store service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all stores successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-stores', IS_ADMIN, tryCatch(storeController.getAllStores));

  /**
   * @swagger
   * /store/v1/update-store/{id}:
   *   put:
   *     tags: [Store Management]
   *     summary: Update store
   *     description: Update store information
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateStoreRequest'
   *     responses:
   *       200:
   *         description: Store updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */

  /**
   * @swagger
   * /store/v1/update-store/:id:
   *   put:
   *     tags: [Update Store Management]
   *     summary: Update update store
   *     description: Update update store in the store service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update store successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-store/:id', IS_ADMIN, tryCatch(storeController.editStore));

  /**
   * @swagger
   * /store/v1/delete-store/{id}:
   *   post:
   *     tags: [Store Management]
   *     summary: Delete store
   *     description: Delete a store from the system
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Store deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */

  /**
   * @swagger
   * /store/v1/delete-store/:id:
   *   post:
   *     tags: [Delete Store Management]
   *     summary: Create delete store
   *     description: Create delete store in the store service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create delete store successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/delete-store/:id', IS_ADMIN, tryCatch(storeController.deleteStore));

  /**
   * @swagger
   * /store/v1/get-store-details/:id:
   *   get:
   *     tags: [Get Store Details Management]
   *     summary: Get get store details
   *     description: Retrieve get store details in the store service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get store details successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-store-details/:id', IS_ADMIN, tryCatch(storeController.getStoreDetails));

  /**
   * @swagger
   * /store/v1/change-store-status/:id:
   *   put:
   *     tags: [Change Store Status Management]
   *     summary: Update change store status
   *     description: Update change store status in the store service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update change store status successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/change-store-status/:id', IS_ADMIN, tryCatch(storeController.changeStoreStatus));

  /**
   * @swagger
   * /store/v1/update-passcode:
   *   put:
   *     tags: [Update Passcode Management]
   *     summary: Update update passcode
   *     description: Update update passcode in the store service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update passcode successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-passcode', HAS_API_KEY, tryCatch(storeController.updatePasscode));

  /**
   * @swagger
   * /store/v1/update-bin-ranges:
   *   post:
   *     tags: [Update Bin Ranges Management]
   *     summary: Create update bin ranges
   *     description: Create update bin ranges in the store service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create update bin ranges successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/update-bin-ranges', IS_ADMIN, tryCatch(storeController.updateBinRanges));

  /**
   * @swagger
   * /store/v1/batch-report-setting/:id:
   *   put:
   *     tags: [Batch Report Setting Management]
   *     summary: Update batch report setting
   *     description: Update batch report setting in the store service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update batch report setting successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/batch-report-setting/:id', IS_ADMIN, tryCatch(storeController.updateBatchReportSetting));

  /**
   * @swagger
   * /store/v1/bin-range:
   *   get:
   *     tags: [Bin Range Management]
   *     summary: Get bin range
   *     description: Retrieve bin range in the store service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get bin range successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/bin-range', IS_ADMIN, tryCatch(storeController.getGlobalBinRanges));

  /**
   * @swagger
   * /store/v1/bin-range:
   *   put:
   *     tags: [Bin Range Management]
   *     summary: Update bin range
   *     description: Update bin range in the store service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update bin range successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/bin-range', IS_ADMIN, tryCatch(storeController.updateGlobalBinRanges));

  /**
   * @swagger
   * /store/v1/check-bin-range:
   *   post:
   *     tags: [Check Bin Range Management]
   *     summary: Create check bin range
   *     description: Create check bin range in the store service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create check bin range successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/check-bin-range', HAS_API_KEY, tryCatch(storeController.checkBinRange));
  router.get('/get-bin-ranges-details', storeController.getBinRangesDetails);
});

module.exports = router;
