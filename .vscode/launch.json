{"version": "0.2.0", "compounds": [{"name": "Start All Local Scripts", "configurations": ["Launch Local Admin", "Launch Local Business", "Launch Local Store", "Launch Local Config", "Launch Local Common", "Launch Local Users", "Launch Local Terminal"]}], "configurations": [{"name": "Run Backend (Windows)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "windows": {"runtimeExecutable": "npm.cmd"}, "linux": {"runtimeExecutable": "npm"}, "osx": {"runtimeExecutable": "npm"}, "preLaunchTask": "Run Backend (Windows)"}, {"name": "Run Backend (macOS/Linux)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "windows": {"runtimeExecutable": "npm.cmd"}, "linux": {"runtimeExecutable": "npm"}, "osx": {"runtimeExecutable": "npm"}, "preLaunchTask": "Run Backend (macOS/Linux)"}, {"name": "Launch Local Admin", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "local-admin"], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Launch Local Business", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "local-business"], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Launch Local Store", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "local-store"], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Launch Local Config", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "local-config"], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Launch Local Common", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "local-common"], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Launch Local Users", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "local-users"], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Launch Local Terminal", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "local-terminal"], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}]}