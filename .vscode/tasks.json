{"version": "2.0.0", "tasks": [{"label": "Run Backend (Windows)", "type": "shell", "command": "npm run local-admin ^& npm run local-business ^& npm run local-store ^& npm run local-config ^& npm run local-common ^& npm run local-users ^& npm run local-terminal", "group": {"kind": "build", "isDefault": true}, "windows": {"suppressTaskName": true}}, {"label": "Run Backend (macOS/Linux)", "type": "shell", "dependsOn": ["admin", "business", "store", "config", "common", "users", "terminal", "maildev"]}, {"type": "npm", "script": "local-admin", "path": "/", "problemMatcher": [], "label": "admin", "detail": "node --watchserver.js"}, {"type": "npm", "script": "local-business", "path": "/", "problemMatcher": [], "label": "business", "detail": "node --watchserver.js"}, {"type": "npm", "script": "local-store", "path": "/", "problemMatcher": [], "label": "store", "detail": "node --watchserver.js"}, {"type": "npm", "script": "local-config", "path": "/", "problemMatcher": [], "label": "config", "detail": "node --watchserver.js"}, {"type": "npm", "script": "local-common", "path": "/", "problemMatcher": [], "label": "common", "detail": "node --watchserver.js"}, {"type": "npm", "script": "local-users", "path": "/", "problemMatcher": [], "label": "users", "detail": "node --watchserver.js"}, {"type": "npm", "script": "local-terminal", "path": "/", "problemMatcher": [], "label": "terminal", "detail": "node --watchserver.js"}, {"type": "npm", "script": "local-maildev", "label": "maildev"}]}