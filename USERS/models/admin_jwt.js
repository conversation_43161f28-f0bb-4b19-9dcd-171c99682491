const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const adminJwtSchema = new Schema(
  {
    admin_id: { type: Schema.Types.ObjectId, ref: 'admin', required: true },
    token: { type: String },
    iat: { type: Date },
    exp: { type: Date },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

const ADMIN_JWT = model('admin_jwt', adminJwtSchema);

module.exports = ADMIN_JWT;
