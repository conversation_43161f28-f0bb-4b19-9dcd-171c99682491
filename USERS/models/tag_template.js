const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const tagTemplatesSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
      unique: true,
    },
    tags: [{ type: Schema.Types.ObjectId, ref: 'tag' }],
    tagString: { type: String },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

tagTemplatesSchema.plugin(uniqueValidator);

const TAG_TEMPLATE = model('tag_template', tagTemplatesSchema);
module.exports = TAG_TEMPLATE;
