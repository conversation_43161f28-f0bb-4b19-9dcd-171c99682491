const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const terminalSchema = new Schema(
  {
    title: { type: String },
    device_id: { type: String, unique: true, required: true },
    mac_address: { type: String, lowercase: true },
    serial_number: { type: String, lowercase: true },
    original_tid: {
      type: String,
    },
    is_deleted: {
      type: Boolean,
      default: false,
    },
    first_login: {
      type: Boolean,
      default: false,
    },
    configuration: {
      type: Schema.Types.ObjectId,
      ref: 'configuration',
    },
    status: {
      type: String,
      default: 'PendingActivation',
    },
    activation_code: {
      otp: { type: String },
      created_time: { type: Date },
    },
    api_key: { type: String },
    last_printed: { type: Date },
    app_version: { type: String, default: '1.0' },
    tid: { type: String, required: true, unique: true },
    setting: {
      ping_time: {
        type: String,
        default: '5',
      },
      report_print: { timeZone: String, timeZoneName: { type: String, default: '' } },
    },
    tagString: {
      type: String,
      default: '5F2A5F348284959A9C9F029F039F099F109F1A9F1E9F269F279F339F349F359F369F37508A89',
    },
    tags: [{ type: Schema.Types.ObjectId, ref: 'tag' }],
    tagStringFormat: { type: String, default: 'default' },
    tag_template: { type: Schema.Types.ObjectId, ref: 'tag_template' },
    store_id: { type: Schema.Types.ObjectId, ref: 'store', required: true },
    business_id: {
      type: Schema.Types.ObjectId,
      ref: 'business',
      required: true,
    },
    user_id: { type: [{ type: Schema?.Types?.ObjectId, ref: 'user', required: true }], default: [] },
    group_id: { type: Schema.Types.ObjectId, ref: 'terminal_group' },
    cert_version: { type: Number, default: 0 },
    location: {
      type: {
        type: String,
        enum: ['Point'],
      },
      coordinates: {
        type: [Number],
      },
    },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

terminalSchema.plugin(uniqueValidator);

const TERMINAL = model('terminal', terminalSchema);

module.exports = TERMINAL;
