const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const messageQueue = new Schema(
  {
    configuration: Object,
    serial_number: String,
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

messageQueue.plugin(uniqueValidator);

const MESSAGE_QUEUE = model('message_queue', messageQueue);
module.exports = MESSAGE_QUEUE;
