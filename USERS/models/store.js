const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const storeSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'title is required'],
    },
    address: {
      type: String,
    },
    owner: {
      type: String,
    },
    status: {
      type: String,
      default: 'Active',
    },
    is_deleted: {
      type: Boolean,
      default: false,
    },
    report_print_time: {
      type: String,
      default: '',
    },
    report_type: {
      type: String,
      enum: ['summary', 'detailed', 'total'],
      default: 'summary',
    },
    report_receive_type: {
      type: String,
      enum: ['print', 'email'],
      default: 'print',
    },
    report_print_device: {
      type: String,
    },
    is_report_merged: {
      type: Boolean,
      default: false,
    },
    passcode: {
      type: String,
      default: '',
    },
    business_id: { type: Schema.Types.ObjectId, ref: 'business' },
    tag_template: { type: Schema.Types.ObjectId, ref: 'tag_template' },
    deactivation_reason: {
      type: String,
      default: '',
    },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

storeSchema.plugin(uniqueValidator);

const STORE = model('store', storeSchema);

module.exports = STORE;
