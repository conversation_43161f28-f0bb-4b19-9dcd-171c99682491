const mongoose = require('mongoose');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const useragent = require('express-useragent');
const indexRouter = require('./routes');
const morgan = require('morgan');
const { mongo_string, env } = require('./configs');


mongoose.Promise = global.Promise;
mongoose
  .connect(mongo_string, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then(() => {
    console.log('MongoDB connected');
  })
  .catch(err => console.log(err));

process.title = 'PSP-POS-USERS';

const app = express();
app.set('trust proxy', 1);
app.use(cors());
app.use(helmet());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(useragent.express());

// Enhanced Morgan logging - only in production
if (env === 'production') {
  // Define custom tokens for detailed logging
  morgan.token('real-ip', (req) => {
    return req.ip || req.socket.remoteAddress || 'unknown';
  });

  morgan.token('user-agent', (req) => {
    return req.get('User-Agent') || 'unknown';
  });

  morgan.token('request-body', (req) => {
    // Sanitize sensitive data from request body
    if (req.body && Object.keys(req.body).length > 0) {
      const sanitized = { ...req.body };
      const sensitiveFields = ['password', 'token', 'secret', 'key', 'pin', 'cvv', 'cvc'];

      sensitiveFields.forEach(field => {
        if (sanitized[field]) {
          sanitized[field] = '[REDACTED]';
        }
      });

      return JSON.stringify(sanitized);
    }
    return '-';
  });

  morgan.token('request-params', (req) => {
    return Object.keys(req.params).length > 0 ? JSON.stringify(req.params) : '-';
  });

  morgan.token('request-query', (req) => {
    return Object.keys(req.query).length > 0 ? JSON.stringify(req.query) : '-';
  });

  // Enhanced production format with all required details
  const productionFormat = ':date[iso] :method :url :status :response-time ms - IP: :real-ip - UA: :user-agent - Params: :request-params - Query: :request-query - Body: :request-body - Size: :res[content-length]';

  app.use(morgan(productionFormat, {
    skip: (req) => req.url === '/' // Skip health check endpoint
  }));
} else {
  // Development/staging logging - simple format
  app.use(morgan('dev', {
    skip: (req) => req.url === '/',
  }));
}

app.get('/', (req, res) => {
  res.status(200).send({ message: 'OK' });
});

const service = `/users`;



app.use(service, indexRouter);
app.get(`${service}/health`, (req, res) => {
  let mongo_db = `Connection state is ${mongoose.connection.readyState}`;
  res.status(200).send({ mongo_db });
});

app.use((req, res) => {
  res.status(404).send({ url: `${req.originalUrl} not found` });
});

app.listen(process.env.PORT ?? 4004);
console.log(`--------------------------------------------------------------`);
console.log(`Server started on port ${process.env.PORT}`);
console.log(`--------------------------------------------------------------`);
