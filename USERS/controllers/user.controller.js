const mongoose = require('mongoose');
const { USER, STOR<PERSON>, TERMINAL, USER_GROUP, TERMINAL_GROUP, BUSINESS } = require('../models');
const {
  filterQuery,
  pagination,
  sendEmail,
  generateRandomClerkId,
  validateCsvData,
  sentUsersToTerminals,
  mergeNames,
  sendDeviceData,
  sendSocketNotification,
  sendEmailLocally,
} = require('../utils/helper');
const bulkUsers = require('../utils/templates/bulk-users');
const { managerCreated, ownerCreated, clerkServerCreated } = require('../utils/templates/user-created');
const { env } = require('../configs');
const helper = require('../utils/helper');

/**
 *
 * @param {*} req
 * @param {*} res
 */
exports.createUser = async (req, res) => {
  let { first_name, last_name, email, store_id, type, group_id } = req.body;

  first_name = first_name.trim();
  last_name = last_name.trim();
  email = email.toLowerCase().trim();

  let usertoSave = {};

  usertoSave.clerk_id = await generateRandomClerkId(4);

  const store = await STORE.findById(store_id, { business_id: 1, status: 1 });

  if (store.status !== 'Active') {
    throw new Error(`Cannot create ${type} because store associated with the ${type} is not active:400`);
  }

  const isUserExixts = await USER.findOne({ email: email });
  if (isUserExixts) {
    throw new Error('Email is already used. Try a unique one!:409');
  }

  usertoSave = {
    ...usertoSave,
    type,
    email,
    store_id,
    last_name,
    first_name,
    business_id: store.business_id,
  };

  if (group_id) {
    usertoSave.group_id = group_id;
  }

  const newUser = await USER.create(usertoSave);

  const dataForEmail = {
    name: usertoSave.first_name + usertoSave.last_name,
    email: usertoSave.email,
    owner_id: usertoSave.clerk_id,
  };

  if (type === 'manager') {
    const userTypes = ['owner'];
    const userToSendEmail = await USER.find({
      store_id: store,
      type: { $in: userTypes },
      is_deleted: false,
    })
      .select(['email', 'type'])
      .lean();
    const userEmails = userToSendEmail.map(user => user.email);

    if (env === 'development' || env === 'local') {
      sendEmailLocally({
        template: managerCreated(dataForEmail.name),
        toList: [...userEmails, usertoSave.email],
        subject: 'Manager Created',
      });
    } else {
      sendEmail({
        template: managerCreated(dataForEmail.name),
        toList: [...userEmails, usertoSave.email],
        subject: 'Manager Created',
      });
    }
  } else if (type === 'owner') {
    if (env === 'development' || env === 'local') {
      sendEmailLocally({
        template: ownerCreated(dataForEmail),
        toList: [usertoSave.email],
        subject: 'Owner Created',
      });
    } else {
      sendEmail({
        template: ownerCreated(dataForEmail),
        toList: [usertoSave.email],
        subject: 'Owner Created',
      });
    }
  } else {
    const userTypes = ['owner', 'manager'];
    const userToSendEmail = await USER.find({
      store_id: store,
      type: { $in: userTypes },
      is_deleted: false,
    })
      .select(['email', 'type'])
      .lean();
    const userEmails = userToSendEmail.map(user => user.email);

    if (env === 'development' || env === 'local') {
      sendEmailLocally({
        template: clerkServerCreated(dataForEmail.name),
        toList: [...userEmails, usertoSave.email],
        subject: 'User Created',
      });
    } else {
      sendEmail({
        template: clerkServerCreated(dataForEmail.name),
        toList: [...userEmails, usertoSave.email],
        subject: 'User Created',
      });
    }
  }

  if (!group_id) {
    if (type === 'owner' || type === 'manager') {
      await TERMINAL.updateMany({ store_id: store_id, is_deleted: false }, { $push: { user_id: newUser?._id } });
      const termUsers = await TERMINAL.find({ store_id });
      sentUsersToTerminals(termUsers, store_id);
    } else {
      await TERMINAL.updateMany({ store_id, group_id: { $exists: false } }, { $push: { user_id: newUser?._id } });
      const termUsers = await TERMINAL.find({
        store_id,
        group_id: { $exists: false },
      });
      sentUsersToTerminals(termUsers, store_id);
    }
  } else {
    let terminalGroup = await TERMINAL_GROUP.find({
      user_group_id: group_id,
      store_id,
    }).select('_id');
    if (terminalGroup.length > 0) {
      let users = await USER.find({
        $or: [
          { group_id, store_id },
          { type: { $in: ['manager', 'owner'] }, is_deleted: false },
        ],
      }).lean();
      users = users.map(user => user._id.toString());
      terminalGroup = terminalGroup.map(t_grp => t_grp?._id);
      await TERMINAL.updateMany({ group_id: { $in: terminalGroup }, store_id }, { user_id: users });
      const termUsers = await TERMINAL.find({
        group_id: { $in: terminalGroup },
        store_id,
      });
      sentUsersToTerminals(termUsers, store_id);
    } else {
      console.log('Not in any device because this group is not in any device grp');
    }
  }

  res.status(200).send({
    code: 200,
    success: true,
    message: `${type[0].toUpperCase()}${type.slice(1)} Created!`,
  });
};

exports.getAllUsers = async (req, res) => {
  let {
    page,
    itemsPerPage,
    searchText,
    status,
    startDate,
    endDate,
    storeId,
    type,
    group,
    groupPage,
    getDeleted,
    getStoreInfo,
  } = filterQuery(req);

  const query = {
    $and: [],
    $or: [],
  };

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');
    query.$or = [{ first_name: regExp }, { last_name: regExp }, { email: regExp }];
    if (!isNaN(searchText)) {
      query.$or.push({ clerk_id: searchText });
    }
  }

  if (status && status !== '') {
    query.$and.push({
      status: { $eq: status },
    });
  }

  if (startDate && endDate) {
    let start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    let end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    query.$and.push({ created_at: { $gte: start, $lt: end } });
  }

  if (storeId && storeId != '') {
    query.$and.push({
      store_id: mongoose.Types.ObjectId(storeId),
    });
  }

  if (type && type != '') {
    query.$and.push({
      type: type,
    });
  }

  if (groupPage == 'true' || groupPage === true) {
    if (!group) {
      query.$and.push({
        type: { $in: ['clerk', 'server'] },
      });
    } else {
      query.group_id = {
        $in: group.includes(',') ? group.split(',') : [mongoose.Types.ObjectId(group)],
      };
    }
  }

  if (getDeleted && getDeleted !== '') {
    query.$and.push({ is_deleted: JSON.parse(getDeleted) });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }

  if (!query.$or.length > 0) {
    delete query.$or;
  }

  const totalItems = await USER.countDocuments(query);

  let users = await USER.find(query)
    .populate({ path: 'group_id', model: USER_GROUP, select: 'name' })
    .sort([
      ['is_deleted', 1],
      ['created_at', -1],
    ])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .lean();

  if (getStoreInfo && getStoreInfo !== '' && getStoreInfo === 'true') {
    const usersWithStoreInfo = [];
    for (let item = 0; item <= users.length - 1; item++) {
      const store_id = await STORE.findById(users[item].store_id);
      usersWithStoreInfo.push({ ...users[item], store_id });
    }

    users = usersWithStoreInfo;
  }

  const records = pagination(users, page, totalItems, itemsPerPage);

  return res.status(200).send({
    ...records,
    code: 200,
    success: true,
    message: 'All Users Fetched Successfully',
  });
};

exports.deleteUser = async (req, res) => {
  const { id } = req.params;

  const user = await USER.findById(id);

  if (!user) {
    throw new Error('User Does Not Exists Or Already Deactivated:404');
  }

  const store = await STORE.findById({ _id: user.store_id });

  if (store.status !== 'Active') {
    throw new Error("Can't Deactivate because store is not Active:400");
  }

  if (user.type === 'owner') {
    const users = await USER.find({
      store_id: user.store_id,
      type: 'owner',
      is_deleted: false,
    });

    if (users.length < 2) {
      throw new Error("Can't Deactivate because it is the only owner of this store:400");
    }
  }

  const email = user?.email;
  const escapedEmail = email.replace(/[.+*?^$[\]{}()|\\/]/g, '\\$&');
  const regexPattern = new RegExp(`^${escapedEmail}_deleted_\\d+$`, 'i');
  const deleted_user = await USER.findOne({ email: { $regex: regexPattern } })
    .sort([['created_at', -1]])
    .exec();

  const allTerminalHavingThisUser = await TERMINAL.find({ user_id: { $in: id } }).select('serial_number');

  if (deleted_user?.email.includes(user.email)) {
    const last_counter = Number(deleted_user.email.split('_').slice(-1)[0]);

    await USER.findByIdAndUpdate(id, {
      is_deleted: true,
      group_id: [],
      email: `${user?.email}_deleted_${last_counter + 1}`,
      status: 'Deactivated',
    });
    await TERMINAL.updateMany({ user_id: { $in: id } }, { $pull: { user_id: id } });
  } else {
    await USER.findByIdAndUpdate(id, {
      is_deleted: true,
      group_id: [],
      email: `${user?.email}_deleted_1`,
      status: 'Deactivated',
    });
    await TERMINAL.updateMany({ user_id: { $in: id } }, { $pull: { user_id: id } });
  }

  sendDeviceData({
    store_id: user.store_id,
  });
  allTerminalHavingThisUser.forEach(({ serial_number }) => {
    sendSocketNotification({
      event: 'user_deleted',
      serial_number: serial_number,
      data: { clerk_id: user.clerk_id },
    });
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'User Deactivate!',
  });
};

exports.changeUserStatus = async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  const user = await USER.findById(id).populate({
    model: STORE,
    path: 'store_id',
    select: { status: 1 },
  });

  if (!user) {
    throw new Error('User Does Not Exists:404');
  }

  if (user?.store_id?.status !== 'Active') {
    throw new Error(`You can't do that because Store associated with this ${user?.type ?? 'User'} is not Active:400`);
  }

  await USER.findByIdAndUpdate(id, { status });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'User Status Changed Successfully',
  });
};

exports.transferUser = async (req, res) => {
  const { id } = req.params;
  const { new_store_id } = req.body;

  const newStoreId = new_store_id;

  const user = await USER.findById(id);

  if (!user) {
    throw new Error('User Does Not Exists:404');
  }

  await TERMINAL.updateMany({ store_id: user?.store_id }, { $pull: { user_id: id } });

  const termUsers = await TERMINAL.find({ store_id: user?.store_id });
  sentUsersToTerminals(termUsers, user?.store_id);

  await USER.findByIdAndUpdate(id, { store_id: newStoreId, group_id: [] });

  await TERMINAL.updateMany({ store_id: newStoreId, group_id: { $exists: false } }, { $push: { user_id: id } });

  const destinationStoreTerminals = await TERMINAL.find({
    store_id: newStoreId,
    group_id: { $exists: false },
  });
  sentUsersToTerminals(destinationStoreTerminals, newStoreId);

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Transfer Successfull!',
  });
};

exports.updateUser = async (req, res) => {
  const { id } = req.params;
  const { first_name, last_name, group_id } = req.body;

  const alreadyExists = await USER.findById(id);

  if (!alreadyExists) {
    throw new Error('User Does Not Exists:404');
  }
  let detailsToUpdate = {
    first_name,
    last_name,
    group_id,
  };

  await USER.findByIdAndUpdate(id, detailsToUpdate);

  sendDeviceData({
    store_id: alreadyExists?.store_id,
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'User Updated Successfully',
  });
};

exports.createClerkServer = async (req, res) => {
  let { first_name, last_name, email, type } = req.body;

  first_name = first_name.trim();
  last_name = last_name.trim();
  email = email?.toLowerCase()?.trim();

  let usertoSave = {};

  usertoSave.clerk_id = await generateRandomClerkId(4);

  const isUserExixts = await USER.findOne({ email: email });

  if (isUserExixts) {
    throw new Error('Email is already used. Try a unique one!:409');
  }

  usertoSave = {
    ...usertoSave,
    first_name,
    last_name,
    email: email,
    store_id: req.terminal.store_id,
    business_id: req.terminal.business_id,
    type,
  };

  let newUser = await USER.create(usertoSave);

  await TERMINAL.findByIdAndUpdate(req.terminal._id, {
    $push: { user_id: newUser._id },
  });

  const userTypes = ['owner', 'manager'];
  const userToSendEmail = await USER.find({
    store_id: req.terminal.store_id,
    type: { $in: userTypes },
    is_deleted: false,
  })
    .select(['email', 'type'])
    .lean();
  const userEmails = userToSendEmail.map(user => user.email);

  const dataByEmail = { name: `${first_name} ${last_name}`, email, owner_id: usertoSave.clerk_id };

  if (env === 'development' || env === 'local') {
    sendEmailLocally({
      template: clerkServerCreated(dataByEmail.name),
      toList: [...userEmails, usertoSave.email],
      subject: 'User Created',
    });
  } else {
    sendEmail({
      template: clerkServerCreated(dataByEmail.name),
      toList: [...userEmails, usertoSave.email],
      subject: 'User Created',
    });
  }

  sendSocketNotification({
    event: `${type}_update`,
    serial_number: `${type}_update`,
    data: {
      shouldRefetchUsers: true,
    },
  });

  let users = await USER.find({
    group_id: { $size: 0 },

    is_deleted: false,

    store_id: req.terminal.store_id,
  }).lean();

  const data = await helper.getUsers(users, req.terminal.store_id);

  const termUsers = await TERMINAL.find({ store_id: req.terminal.store_id });

  if (termUsers.length > 0) {
    termUsers.forEach(tUser => {
      sendSocketNotification({
        serial_number: tUser.serial_number,

        event: 'user_updated',

        data,
      });
    });
  }

  res.status(200).send({
    code: 200,

    success: true,

    message: `${type[0].toUpperCase()}${type.slice(1)} Created!`,

    data: { ...usertoSave, _id: newUser._id, name: `${first_name} ${last_name}` },
  });
};

exports.uploadCsv = async (req, res) => {
  let { users, store } = req.body;

  users = users.filter(obj => {
    return Object.values(obj).some(val => val !== null);
  });

  Object.entries(users[0]).forEach(([key]) => {
    if (key === '') {
      throw new Error('Invalid CSV! Field name is missing:400');
    } else if (!['email', 'first_name', 'last_name', 'type'].includes(key)) {
      throw new Error(`Invalid CSV! Unknown field name-${key}:400`);
    }
  });

  if (!users.length) {
    throw new Error(`Error in uploading try again!:400`);
  }

  const { business_id } = await STORE.findById(store, { business_id: 1 });
  const { owner: business_owner } = (await BUSINESS.findById(business_id, { owner: 1 })) ?? { owner: {} };
  const { validatedUsers, existingUsers } = await validateCsvData(users, business_id, store);

  if (validatedUsers.length === 0) {
    throw new Error('Invalid CSV! No valid data found:400');
  } else {
    const updatedUsers = await USER.insertMany(validatedUsers);
    const ids = updatedUsers.map(_ => _.id);
    await TERMINAL.updateMany({ store_id: store, group_id: { $exists: false } }, { $push: { user_id: ids } });

    // const newlyAddedUsers = validatedUsers.map(_ => {
    //   return {
    //     name: mergeNames(_),
    //     email: _.email,
    //     type: _.type,
    //   };
    // });

    const oldOwners = (
      await USER.find({
        store_id: store,
        type: { $in: ['owner', 'manager'] },
        is_deleted: false,
        status: 'Active',
      }).select('email')
    ).map(_ => _.email);

    if (env === 'development' || env === 'local') {
      sendEmailLocally({
        template: bulkUsers(`${business_owner?.first_name} ${business_owner?.last_name}`),
        toList: oldOwners,
        subject: 'Bulk Users Uploaded',
      });
    } else {
      sendEmail({
        template: bulkUsers(`${business_owner?.first_name} ${business_owner?.last_name}`),
        toList: oldOwners,
        subject: 'Bulk Users Uploaded',
      });
    }

    // send users to terminals
    const termUsers = await TERMINAL.find({
      group_id: { $exists: false },
      store_id: store,
      status: { $ne: 'Deactivated' },
    });

    sentUsersToTerminals(termUsers, store);
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: `File Uploaded Successfully!`,
    data: existingUsers?.length > 0 ? existingUsers : [],
  });
};

exports.createUserGroup = async (req, res) => {
  const { name, users, storeId } = req.body;

  const isGroupExists = await USER_GROUP.findOne({ name: name?.trim() });

  if (isGroupExists) {
    throw new Error('User Group Exists!:409');
  }

  const newGroupCreated = await USER_GROUP.create({ name, store_id: storeId });

  if (users.includes('all')) {
    await USER.updateMany({ is_deleted: false, store_id: storeId }, { $push: { group_id: newGroupCreated?._id } });
    await TERMINAL.updateMany({ group_id: { $exists: false }, store_id: storeId }, { user_id: [] });
  } else {
    await USER.updateMany({ _id: { $in: users } }, { $push: { group_id: newGroupCreated?._id } });
    await TERMINAL.updateMany(
      { group_id: { $exists: false }, store_id: storeId },
      { $pull: { user_id: { $in: users } } },
    );
  }

  const termUsers = await TERMINAL.find({ group_id: { $exists: false }, store_id: storeId });

  sentUsersToTerminals(termUsers, storeId);

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Group Created Successfully!',
  });
};

exports.deleteUserGroup = async (req, res) => {
  const { id } = req.params;

  const isGroupExists = await USER_GROUP.findById(id);

  if (!isGroupExists) {
    throw new Error('Group Not exists!:404');
  }

  const isDevciceGroup = await TERMINAL_GROUP.findOne({ user_group_id: id });

  if (isDevciceGroup) {
    throw new Error(
      'User Group is Linked with device group, Delete that device group if you want to delete this user group!:409',
    );
  }

  await USER_GROUP.findByIdAndDelete({ _id: id });
  let users = await USER.find({ group_id: id }).lean();
  users = users.map(r => r._id.toString());
  await Promise.all(
    users.map(
      async user =>
        await TERMINAL.updateMany(
          { group_id: { $exists: false }, store_id: isGroupExists.store_id },
          { $push: { user_id: user } },
        ),
    ),
  );
  const termUsers = await TERMINAL.find({
    group_id: { $exists: false },
    store_id: isGroupExists.store_id,
  }).lean();
  sentUsersToTerminals(termUsers, termUsers[0]?.store_id);
  await USER.updateMany({ group_id: id }, { $pull: { group_id: id } });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Group Deleted Successfully!',
  });
};

exports.updateUserGroup = async (req, res) => {
  const { id } = req.params;
  const { name, users } = req.body;

  await USER_GROUP.findByIdAndUpdate(id, { name });

  await USER.updateMany({ _id: { $nin: users } }, { $pull: { group_id: id } });

  const user = await USER.find({ _id: { $nin: users } }).lean();
  user.forEach(
    async usr => await TERMINAL.updateMany({ group_id: { $exists: false } }, { $addToSet: { user_id: usr } }),
  );
  const termUsers = await TERMINAL.find({
    group_id: { $exists: false },
  }).lean();
  sentUsersToTerminals(termUsers, termUsers[0]?.store_id);

  await USER.updateMany({ _id: { $in: users } }, { $addToSet: { group_id: id } });
  const terminalGroup = await TERMINAL_GROUP.findOne({ user_group_id: id });
  const _user = await USER.find({ _id: { $in: users } }).lean();
  _user.forEach(
    async usr => await TERMINAL.updateMany({ group_id: terminalGroup?._id }, { $addToSet: { user_id: usr } }),
  );

  sentUsersToTerminals(termUsers, termUsers[0]?.store_id);

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Group Updated Successfully!',
  });
};

exports.getAllUsersGroups = async (req, res) => {
  let {
    page,
    itemsPerPage,
    searchText,
    status,
    startDate,
    endDate,
    update_device_group,
    device_group_id,
    store_id,
    getDeleted,
  } = filterQuery(req);

  const query = {
    $and: [],
    $or: [],
  };

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [{ name: regExp }];
  }

  if (status && status !== '') {
    query.$and.push({
      status: { $eq: status },
    });
  }

  if (startDate && endDate) {
    let start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    let end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    query.$and.push({ created_at: { $gte: start, $lt: end } });
  }

  if (update_device_group === true || update_device_group === 'true') {
    if (device_group_id) {
      const devicegrp = await TERMINAL_GROUP.findById(device_group_id);
      query.$and.push({
        _id: { $in: devicegrp?.user_group_id },
      });
    }
  }

  if (getDeleted && getDeleted !== '') {
    query.$and.push({ is_deleted: JSON.parse(getDeleted) });
  }

  if (store_id && store_id !== '') {
    query.$and.push({ store_id: mongoose.Types.ObjectId(store_id) });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }

  if (!query.$or.length > 0) {
    delete query.$or;
  }

  const totalItems = await USER_GROUP.countDocuments(query);

  const users_groups = await USER_GROUP.aggregate([
    { $match: query },
    { $sort: { created_at: -1 } },
    { $skip: (page - 1) * itemsPerPage },
    { $limit: itemsPerPage },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: 'group_id',
        as: 'users',
      },
    },
  ]);

  const records = pagination(users_groups, page, totalItems, itemsPerPage);

  return res.status(200).send({
    ...records,
    code: 200,
    success: true,
    message: 'All Users Groups Fetched Successfully',
  });
};

exports.getGroupDetails = async (req, res) => {
  const { id } = req.params;

  const grp = await USER_GROUP.findById(id).select(['name', 'created_at', 'updated_at', '_id']);

  const detail = {
    group: grp,
  };

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'User Group details!',
    data: detail,
  });
};

exports.deleteUserFromDevice = async (req, res) => {
  const { id } = req.params;
  const { group_id, store_id } = req.terminal;

  const user = await USER.findById(id);

  if (user?.type === 'owner') {
    const users = await USER.find({
      store_id,
      type: 'owner',
      is_deleted: false,
    });

    if (users.length < 2) {
      throw new Error("Can't Delete because it is the only owner of this store:400");
    }
  }

  await USER.findByIdAndUpdate(id, {
    is_deleted: true,
    group_id: [],
    status: 'Deactivated',
  });

  await TERMINAL.updateMany({ user_id: { $in: [id] } }, { $pull: { user_id: id } });

  const termUsers = await TERMINAL.find({ group_id, store_id });

  sentUsersToTerminals(termUsers, store_id);

  sendSocketNotification({
    event: `${user?.type}_update`,
    serial_number: `${user?.type}_update`,
    data: {
      shouldRefetchUsers: true,
    },
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'User Deleted!',
  });
};
