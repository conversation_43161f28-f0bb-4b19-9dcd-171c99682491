// const ownerCreated = data => {
//   return `<!DOCTYPE html>
// <html lang="en">
// <head>
//     <meta charset="UTF-8">
//     <meta http-equiv="X-UA-Compatible" content="IE=edge">
//     <meta name="viewport" content="width=device-width, initial-scale=1.0">
//     <title>Owner Created</title>
//     <style>
//         body {
//             margin: 0;
//             padding: 0;
//             background-color: #f5f5f5;
//         }

//         .container {
//             max-width: 600px;
//             margin: 0 auto;
//             padding: 20px;
//         }

//         .header {
//             text-align: center;
//             margin-bottom: 20px;
//         }

//         .header h1 {
//             color: #000;
//             font-size: 30px;
//             font-weight: 600;
//             margin: 0;
//         }

//         .details {
//             background-color: #ffffff;
//             border-radius: 10px;
//             padding: 20px;
//         }

//         .details h2 {
//             color: #000000;
//             font-size: 24px;
//             font-weight: 600;
//             margin-top: 0;
//             margin-bottom: 10px;
//         }

//         .details p {
//             color: #000000;
//             font-size: 18px;
//             font-weight: 400;
//             margin: 0 0 10px;
//         }
//     </style>
// </head>
// <body>
//     <table class="container" align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px;">
//         <tr>
//             <td>
//                 <table class="header" align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
//                     <tr>
//                         <td>
//                             <h1>Details of newly Created Owner</h1>
//                         </td>
//                     </tr>
//                 </table>
//                 <table class="details" align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
//                     <tr>
//                         <td>
//                             <p><strong>Name:</strong> ${data.name}</p>
//                             <p><strong>Email:</strong> ${data.email}</p>
//                             <p><strong>ID:</strong> ${data.id}</p>
//                         </td>
//                     </tr>
//                 </table>
//             </td>
//         </tr>
//     </table>
// </body>
// </html>
// `;
// };
// const managerCreated = data => {
//   return `<!DOCTYPE html>
//     <html lang="en">
//     <head>
//         <meta charset="UTF-8">
//         <meta http-equiv="X-UA-Compatible" content="IE=edge">
//         <meta name="viewport" content="width=device-width, initial-scale=1.0">
//         <title>Manager Created</title>
//         <style>
//             body {
//                 margin: 0;
//                 padding: 0;
//                 background-color: #f5f5f5;
//             }

//             .container {
//                 max-width: 600px;
//                 margin: 0 auto;
//                 padding: 20px;
//             }

//             .header {
//                 text-align: center;
//                 margin-bottom: 20px;
//             }

//             .header h1 {
//                 color: #000;
//                 font-size: 30px;
//                 font-weight: 600;
//                 margin: 0;
//             }

//             .details {
//                 background-color: #ffffff;
//                 border-radius: 10px;
//                 padding: 20px;
//             }

//             .details h2 {
//                 color: #000000;
//                 font-size: 24px;
//                 font-weight: 600;
//                 margin-top: 0;
//                 margin-bottom: 10px;
//             }

//             .details p {
//                 color: #000000;
//                 font-size: 18px;
//                 font-weight: 400;
//                 margin: 0 0 10px;
//             }
//         </style>
//     </head>
//     <body>
//         <table class="container" align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px;">
//             <tr>
//                 <td>
//                     <table class="header" align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
//                         <tr>
//                             <td>
//                                 <h1>Details of newly Created Manager</h1>
//                             </td>
//                         </tr>
//                     </table>
//                     <table class="details" align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
//                         <tr>
//                             <td>
//                                 <p><strong>Name:</strong> ${data.name}</p>
//                                 <p><strong>Email:</strong> ${data.email}</p>
//                                 <p><strong>ID:</strong> ${data.id}</p>
//                             </td>
//                         </tr>
//                     </table>
//                 </td>
//             </tr>
//         </table>
//     </body>
//     </html>`;
// };
// const clerkServerCreated = data => {
//   return `<!DOCTYPE html>
//     <html lang="en">
//     <head>
//         <meta charset="UTF-8">
//         <meta http-equiv="X-UA-Compatible" content="IE=edge">
//         <meta name="viewport" content="width=device-width, initial-scale=1.0">
//         <title>User Created</title>
//         <style>
//             body {
//                 margin: 0;
//                 padding: 0;
//                 background-color: #f5f5f5;
//             }

//             .container {
//                 max-width: 600px;
//                 margin: 0 auto;
//                 padding: 20px;
//             }

//             .header {
//                 text-align: center;
//                 margin-bottom: 20px;
//             }

//             .header h1 {
//                 color: #000;
//                 font-size: 30px;
//                 font-weight: 600;
//                 margin: 0;
//             }

//             .details {
//                 background-color: #ffffff;
//                 border-radius: 10px;
//                 padding: 20px;
//             }

//             .details h2 {
//                 color: #000000;
//                 font-size: 24px;
//                 font-weight: 600;
//                 margin-top: 0;
//                 margin-bottom: 10px;
//             }

//             .details p {
//                 color: #000000;
//                 font-size: 18px;
//                 font-weight: 400;
//                 margin: 0 0 10px;
//             }
//         </style>
//     </head>
//     <body>
//         <table class="container" align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px;">
//             <tr>
//                 <td>
//                     <table class="header" align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
//                         <tr>
//                             <td>
//                                 <h1>User Created</h1>
//                             </td>
//                         </tr>
//                     </table>
//                     <table class="details" align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
//                         <tr>
//                             <td>
//                                 <p><strong>Name:</strong> ${data.name}</p>
//                                 <p><strong>Email:</strong> ${data.email}</p>
//                                 <p><strong>ID:</strong> ${data.id}</p>
//                             </td>
//                         </tr>
//                     </table>
//                 </td>
//             </tr>
//         </table>
//     </body>
//     </html>`;
// };
const clerkServerCreated = require('./server-detail-update');
const managerCreated = require('./manager-update');
const ownerCreated = require('./choosing-psp');

module.exports = { clerkServerCreated, managerCreated, ownerCreated };
