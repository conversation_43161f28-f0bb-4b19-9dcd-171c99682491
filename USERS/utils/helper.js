const sgMail = require('@sendgrid/mail');
sgMail.setApiKey(process.env.SENDGRID_API_KEY);
const axios = require('axios');
const { USER, STORE } = require('../models');
const nodemailer = require('nodemailer');

const {
  env,
  sendgrid_api_key,
  sendgrid_user_name,
  sendgrid_port,
  sendgrid_host,
  sendgrid_email_from,
  access_key,
  mail_host,
  mail_port,
  mail_username,
  mail_password,
  mail_from_address,
  mail_from_name,
  base_url,
} = require('../configs');
module.exports = {
  sendEmail: async ({ template, toList, subject, attachment = null }) => {
    try {
      const msg = {
        to: toList,
        from: sendgrid_email_from,
        subject: subject,
        html: template,
        tls: {
          ciphers: 'SSLv3',
          rejectUnauthorized: false,
        },
        host: sendgrid_host,
        port: sendgrid_port,
        auth: {
          user: sendgrid_user_name,
          pass: sendgrid_api_key,
        },
      };

      await sgMail.send(msg);
      return { code: 200, message: 'email sent', success: true };
    } catch (err) {
      return { code: err.code, message: err.message, success: false };
    }
  },
  genRandom: length => {
    let zero = '';
    for (let index = 1; index < length; index++) {
      zero += '0';
    }

    let firstVal = 1 + zero;
    let secondVal = 9 + zero;

    return Math.floor(Number(firstVal) + Math.random() * Number(secondVal));
  },
  getUsers: async (array, storeId) => {
    const store = await STORE.findById(storeId);
    const data = array
      .filter(user => !user.is_deleted)
      .map(({ first_name, last_name, ...user }) => {
        const updatedUser = {
          ...user,
          name: `${first_name} ${last_name}`,
          passcode: store.passcode,
        };

        return updatedUser;
      });
    return data;
  },
  pagination: (items = [], page = 1, totalItems = 0, itemsPerPage = 5) => {
    return {
      currentPage: page,
      hasNextPage: itemsPerPage * page < totalItems,
      hasPreviousPage: page > 1,
      nextPage: page + 1,
      previousPage: page - 1,
      lastPage: Math.ceil(totalItems / itemsPerPage),
      totalItems: totalItems,
      items: items,
    };
  },
  filterQuery: req => ({
    ...req.query,
    page: req.query.page ? Number(req.query.page) : 1,
    itemsPerPage: req.query.itemsPerPage
      ? Number(req.query.itemsPerPage)
      : req.query.perPage
        ? Number(req.query.perPage)
        : 10,
    searchText:
      req.query.searchText !== 'null' && req.query.searchText !== 'undefined' && req.query.searchText
        ? req.query.searchText
        : '',
    startDate:
      req.query.startDate !== 'null' && req.query.startDate !== 'undefined' && req.query.startDate
        ? req.query.startDate
        : '',
    endDate:
      req.query.endDate !== 'null' && req.query.endDate !== 'undefined' && req.query.endDate ? req.query.endDate : '',
    storeId:
      req.query.storeId !== 'null' && req.query.storeId !== 'undefined' && req.query.storeId ? req.query.storeId : '',
  }),
  readAllSchemaFiles: async () => {
    const fs = require('fs');
    const path = require('path');
    const createCsvWriter = require('csv-writer').createObjectCsvWriter;

    const directoryPath = path.join(__dirname, '../confirmed_models');
    const csvPath = path.join(__dirname, '../utils/csv');

    const files = await new Promise((resolve, reject) => {
      fs.readdir(directoryPath, (err, files) => {
        if (err) {
          reject(err);
        } else {
          resolve(files);
        }
      });
    });

    files.forEach(async file => {
      let csvFileName = file.replace('.js', '.csv');
      const filePath = path.join(directoryPath, file);

      // Require each file
      let model = require(filePath);
      if (typeof model === 'function') {
        const newObject = Object.entries(model.schema.paths).reduce((acc, [key, payload]) => {
          acc.push({
            params: key,
            requred: payload?.isRequired ? 'Y' : 'O',
            unique: payload?.options?.unique ? 'Y' : 'N',
            data_type: payload?.instance,
            enum_values: payload?.enumValues?.length ? payload?.enumValues : '',
            default_values: payload?.options?.default ? payload?.options?.default : '',
          });
          return acc;
        }, []);

        let csvWritePath = path.join(csvPath, csvFileName);

        const csvWriter = createCsvWriter({
          path: csvWritePath,
          header: Object.keys(newObject[0]).map(key => ({
            id: key,
            title: key.toUpperCase(),
          })),
        });
        await csvWriter.writeRecords(newObject);
      }
    });
  },
  generateRandomClerkId: async length => {
    const number = module.exports.genRandom(length);
    const user = await USER.findOne({ clerk_id: number });

    if (user) {
      return module.exports.generateRandomClerkId(length);
    }
    return number;
  },

  validateCsvData: async (csvData, businessId, storeId) => {
    const validatedUsers = [];
    const existingUsers = [];
    for (const element of csvData) {
      const row = element;
      const user = {};

      Object.entries(row).forEach(([key, val]) => {
        if (val === null) {
          throw new Error(`${key} feild is missing!:400`);
        }
        user[key] = row[key];
      });

      user.first_name = user.first_name.trim();
      user.last_name = user.last_name.trim();
      user.email = user.email.toLowerCase();
      user.clerk_id = await module.exports.generateRandomClerkId(4);
      const isUserExixts = await USER.findOne({ email: user.email });
      if (isUserExixts) {
        existingUsers.push(user.email);
      } else {
        user.business_id = businessId;
        user.store_id = storeId;
        validatedUsers.push(user);
      }
    }

    return { validatedUsers, existingUsers };
  },
  sentUsersToTerminals: async (terminalUsers, storeId) => {
    let userToSend = terminalUsers?.map(_ => _.user_id);

    userToSend = [...new Set(userToSend.flat().map(id => id.toString()))];

    let usersIds = await USER.find({ _id: { $in: userToSend } }).lean();
    const userData = await module.exports.getUsers(usersIds, storeId);

    if (terminalUsers.length > 0) {
      terminalUsers.forEach(tUser => {
        module.exports.sendSocketNotification({
          serial_number: tUser.serial_number,
          event: 'user_updated',
          data: { userData },
        });
      });
    }
  },
  sendSocketNotification: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };
      const response = await axios.post(`${base_url}/common/send-socket-notification`, payload, {
        headers,
      });
      console.log(response.code);
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },

  sendDeviceData: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };
      const response = await axios.post(`${base_url}/common/send-device-data`, payload, {
        headers,
      });
      console.log(response.code);
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },
  mergeNames: _ => {
    let name = null;
    if (_.first_name) {
      name = `${_?.first_name} ${_?.last_name}`;
    }
    return name;
  },
  sendEmailLocally: ({ template, toList, subject, attachment = null }) => {
    try {
      let transporter = nodemailer.createTransport({
        host: mail_host,
        port: mail_port,
        secure: false,
        auth: {
          user: mail_username,
          pass: mail_password,
        },
        tls: {
          rejectUnauthorized: false,
        },
      });
      transporter.sendMail({
        from: `${mail_from_name} <${mail_from_address}>`,
        to: toList,
        replyTo: null,
        subject,
        html: template,
        attachments: attachment,
      });
    } catch (error) {
      throw new Error(`${error?.message ?? error ?? 'Somehing Went Wrong While Sending Email'}:400`);
    }
  },
  getUsers: async (array, storeId) => {
    const store = await STORE.findById(storeId);

    const data = array

      .filter(user => !user.is_deleted)

      .map(({ first_name, last_name, ...user }) => {
        const updatedUser = {
          ...user,

          name: `${first_name} ${last_name}`,

          passcode: store.passcode,
        };

        return updatedUser;
      });

    return data;
  },
};
