const express = require('express');
require('express-group-routes');
const router = express.Router();

const { IS_ADMIN, HAS_API_KEY } = require('./middlewares');
const tryCatch = require('./utils/tryCatch');
const { userController } = require('./controllers');

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 */


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */

router.group('/v1', router => {
  // Admin routes

  /**
   * @swagger
   * /users/v1/create-user:
   *   post:
   *     tags: [User Management]
   *     summary: Create new user
   *     description: Create a new user with specified details and assign to store/business
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateUsersRequest'
   *     responses:
   *       201:
   *         description: User created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /users/v1/create-user:
   *   post:
   *     tags: [Create User Management]
   *     summary: Create create user
   *     description: Create create user in the users service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create user successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-user', IS_ADMIN, tryCatch(userController.createUser));

  /**
   * @swagger
   * /users/v1/get-all-users:
   *   get:
   *     tags: [User Management]
   *     summary: Get all users
   *     description: Retrieve a paginated list of all users with filtering options
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *       - name: storeId
   *         in: query
   *         description: Filter by store ID
   *         schema:
   *           $ref: '#/components/schemas/ObjectId'
   *       - name: status
   *         in: query
   *         description: Filter by user status
   *         schema:
   *           $ref: '#/components/schemas/Status'
   *     responses:
   *       200:
   *         description: Users retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsersListResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /users/v1/get-all-users:
   *   get:
   *     tags: [Get All Users Management]
   *     summary: Get get all users
   *     description: Retrieve get all users in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all users successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-users', IS_ADMIN, tryCatch(userController.getAllUsers));

  /**
   * @swagger
   * /users/v1/delete-user/:id:
   *   delete:
   *     tags: [Delete User Management]
   *     summary: Delete delete user
   *     description: Delete delete user in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete user successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-user/:id', IS_ADMIN, tryCatch(userController.deleteUser));

  /**
   * @swagger
   * /users/v1/change-user-status/:id:
   *   put:
   *     tags: [Change User Status Management]
   *     summary: Update change user status
   *     description: Update change user status in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update change user status successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/change-user-status/:id', IS_ADMIN, tryCatch(userController.changeUserStatus));

  /**
   * @swagger
   * /users/v1/transfer-user/:id:
   *   put:
   *     tags: [Transfer User Management]
   *     summary: Update transfer user
   *     description: Update transfer user in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update transfer user successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.patch('/transfer-user/:id', IS_ADMIN, tryCatch(userController.transferUser));

  /**
   * @swagger
   * /users/v1/update-user/:id:
   *   put:
   *     tags: [Update User Management]
   *     summary: Update update user
   *     description: Update update user in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update user successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-user/:id', IS_ADMIN, tryCatch(userController.updateUser));

  /**
   * @swagger
   * /users/v1/upload-csv:
   *   put:
   *     tags: [Upload Csv Management]
   *     summary: Update upload csv
   *     description: Update upload csv in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update upload csv successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/upload-csv', IS_ADMIN, tryCatch(userController.uploadCsv));

  // Admin group routes

  /**
   * @swagger
   * /users/v1/create-user-group:
   *   post:
   *     tags: [Create User Group Management]
   *     summary: Create create user group
   *     description: Create create user group in the users service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create user group successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-user-group', IS_ADMIN, tryCatch(userController.createUserGroup));

  /**
   * @swagger
   * /users/v1/delete-user-group/:id:
   *   delete:
   *     tags: [Delete User Group Management]
   *     summary: Delete delete user group
   *     description: Delete delete user group in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete user group successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-user-group/:id', IS_ADMIN, tryCatch(userController.deleteUserGroup));

  /**
   * @swagger
   * /users/v1/update-user-group/:id:
   *   put:
   *     tags: [Update User Group Management]
   *     summary: Update update user group
   *     description: Update update user group in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update user group successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-user-group/:id', IS_ADMIN, tryCatch(userController.updateUserGroup));

  /**
   * @swagger
   * /users/v1/get-group-details/:id:
   *   get:
   *     tags: [Get Group Details Management]
   *     summary: Get get group details
   *     description: Retrieve get group details in the users service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get group details successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-group-details/:id', IS_ADMIN, tryCatch(userController.getGroupDetails));

  /**
   * @swagger
   * /users/v1/get-all-users-groups:
   *   get:
   *     tags: [Get All Users Groups Management]
   *     summary: Get get all users groups
   *     description: Retrieve get all users groups in the users service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all users groups successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-users-groups', IS_ADMIN, tryCatch(userController.getAllUsersGroups));

  // Device routes

  /**
   * @swagger
   * /users/v1/create-clerk-server:
   *   post:
   *     tags: [Create Clerk Server Management]
   *     summary: Create create clerk server
   *     description: Create create clerk server in the users service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create clerk server successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-clerk-server', HAS_API_KEY, tryCatch(userController.createClerkServer));

  /**
   * @swagger
   * /users/v1/delete-user-from-device/:id:
   *   post:
   *     tags: [Delete User From Device Management]
   *     summary: Create delete user from device
   *     description: Create delete user from device in the users service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create delete user from device successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/delete-user-from-device/:id', HAS_API_KEY, tryCatch(userController.deleteUserFromDevice));
});

module.exports = router;
