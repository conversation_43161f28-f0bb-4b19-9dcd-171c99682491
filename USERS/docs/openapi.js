/**
 * @fileoverview OpenAPI configuration for USERS service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const usersSchemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for USERS service
 * @param {Object} app - Express app instance
 */
function setupUsersDocs(app) {
  const config = {
    serviceName: 'USERS',
    serviceDescription: `
      User management and authentication service

      Key features:
      - Entity management
      - Authentication and authorization
      - Data validation and processing
    `,
    version: '1.0.0',
    port: 4004,
    basePath: '/users',
    additionalSchemas: usersSchemas,
    docsPath: '/users/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  setupUsersDocs
};