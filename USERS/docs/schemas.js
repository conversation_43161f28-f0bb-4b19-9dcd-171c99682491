/**
 * @fileoverview OpenAPI schemas specific to USERS service
 */

module.exports = {
  Users: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      first_name: {
        type: 'string',
        example: '<PERSON>',
        description: 'User first name'
      },
      last_name: {
        type: 'string',
        example: 'Do<PERSON>',
        description: 'User last name'
      },
      email: {
        type: 'string',
        format: 'email',
        example: '<EMAIL>',
        description: 'User email address'
      },
      phone: {
        type: 'string',
        example: '+1234567890',
        description: 'User phone number'
      },
      employee_id: {
        type: 'string',
        example: 'EMP001',
        description: 'Employee ID'
      },
      store_id: {
        $ref: '#/components/schemas/ObjectId',
        description: 'Associated store ID'
      },
      business_id: {
        $ref: '#/components/schemas/ObjectId',
        description: 'Associated business ID'
      },
      role: {
        type: 'string',
        enum: ['cashier', 'manager', 'supervisor'],
        example: 'cashier',
        description: 'User role'
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['first_name', 'last_name', 'email', 'employee_id', 'store_id', 'role']
  },

  CreateUsersRequest: {
    type: 'object',
    properties: {
      first_name: {
        type: 'string',
        minLength: 2,
        example: 'Jane',
        description: 'User first name'
      },
      last_name: {
        type: 'string',
        minLength: 2,
        example: 'Smith',
        description: 'User last name'
      },
      email: {
        type: 'string',
        format: 'email',
        example: '<EMAIL>',
        description: 'User email address'
      },
      phone: {
        type: 'string',
        example: '+1234567890',
        description: 'User phone number'
      },
      employee_id: {
        type: 'string',
        example: 'EMP002',
        description: 'Employee ID'
      },
      store_id: {
        $ref: '#/components/schemas/ObjectId',
        description: 'Store ID to assign user to'
      },
      business_id: {
        $ref: '#/components/schemas/ObjectId',
        description: 'Business ID to assign user to'
      },
      role: {
        type: 'string',
        enum: ['cashier', 'manager', 'supervisor'],
        example: 'cashier',
        description: 'User role'
      },
      password: {
        type: 'string',
        minLength: 6,
        example: 'securePassword123',
        description: 'User password'
      }
    },
    required: ['first_name', 'last_name', 'email', 'employee_id', 'store_id', 'role', 'password']
  },

  UpdateUsersRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Updated Users',
        description: 'Users name'
      },
      description: {
        type: 'string',
        example: 'Updated description',
        description: 'Users description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  UsersListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Users'
            }
          }
        }
      }
    ]
  }
};