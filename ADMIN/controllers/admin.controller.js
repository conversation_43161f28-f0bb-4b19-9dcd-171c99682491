const mongoose = require('mongoose');
const { ADMIN, ADMIN_JWT, ROLE, PERMISSIONS } = require('../models');
const {
  hashPassword,
  decryptToken,
  generateToken,
  comparePassword,
  pagination,
  filterQuery,
  rolesFilter,
} = require('../utils/helper');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');

const { env } = require('../configs');

exports.signup = async (req, res) => {
  const { email, password, role } = req.body;

  let admin = await ADMIN.findOne({ email });

  if (admin) {
    throw new Error('Admin with this email already exists:409');
  }

  const hashedPassword = hashPassword(password);

  let roles = await ROLE.find({ type: role });

  let permissions_find_array = roles.map(r => r.permissions.flat()).flat();

  permissions_find_array = permissions_find_array.map(permission => mongoose.Types.ObjectId(permission));

  let permissions = await PERMISSIONS.find({
    _id: { $in: permissions_find_array },
  });

  req.body.permissions = permissions.map(i => i.can);

  await ADMIN.create({
    ...req.body,
    password: hashedPassword,
    roles: [roles[0]._id],
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'SignUp Successfully',
  });
};

exports.login = async (req, res) => {
  const { email, password, token: faToken } = req.body;
  console.log({ email, password, faToken });
  let admin = await ADMIN.findOne({
    email,
  }).select('-passscode -otp -updated_at -created_at');

  if (!admin || !comparePassword(password, admin.password)) {
    throw new Error('Email or Password is Incorrect:401');
  }

  const verified = speakeasy.totp.verify({
    secret: admin?.token,
    encoding: 'base32',
    token: faToken?.toString(),
  });

  if (!verified) {
    return res.status(401).json({
      code: 401,
      success: false,
      message: 'Please try again with the correct token!',
    });
  }

  const totalSessions = await ADMIN_JWT.countDocuments({
    admin_id: admin._id,
  });

  if (totalSessions > 0) {
    await ADMIN_JWT.findOneAndRemove({ admin_id: admin._id });
  }

  const token = generateToken({
    id: admin._id,
    email,
  });

  if (!token) {
    throw new Error('Error generating token!:500');
  }

  const decryptedToken = decryptToken(token);

  await ADMIN_JWT.create({
    admin_id: admin._id,
    token: token,
    iat: decryptedToken.iat,
    exp: decryptedToken.exp,
  });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Login successful!',
    token,
    admin,
  });
};

exports.logout = async (req, res) => {
  await ADMIN_JWT.deleteOne({
    admin_id: req.admin.id,
  });

  return res.status(200).json({ code: 200, message: 'Admin session updated' });
};

exports.addAdmin = async (req, res) => {
  const { email } = req.body;

  let admin = await ADMIN.findOne({ email });

  if (admin) {
    throw new Error('Email address you provided is already in use:409');
  }

  let roles = await ROLE.find({
    _id: { $in: req.body.roles.map(i => mongoose.Types.ObjectId(i)) },
  });

  let permissions_find_array = roles.map(r => r.permissions.flat()).flat();
  permissions_find_array = permissions_find_array.map(permission => mongoose.Types.ObjectId(permission));

  let permissions = await PERMISSIONS.find({
    _id: { $in: permissions_find_array },
  });
  req.body.permissions = permissions.map(i => i.can);

  let new_admin_request = req.body;
  new_admin_request.password = hashPassword(new_admin_request.password);
  const newAdmin = await ADMIN.create(new_admin_request);

  return res.status(200).send({
    code: 200,
    message: 'Admin is created Sucessfully!',
    success: true,
    data: newAdmin,
  });
};

exports.deleteAdmin = async (req, res) => {
  let id = req.params.id;

  await ADMIN.deleteOne({
    _id: id,
  });

  return res.status(200).send({ code: 200, message: 'Admin is removed' });
};

exports.updateAdmin = async (req, res) => {
  const { id } = req.params;

  const payload = req.body;
  const counter = await ADMIN.findOne({ _id: id });

  if (counter) {
    let admin = {};
    Object.keys(payload).forEach(element => {
      if (element === 'password') {
        payload[element] = hashPassword(payload[element]);
        payload['token'] = '';
      }
      admin[element] = payload[element];
    });

    await ADMIN.findOneAndUpdate({ _id: id }, { $set: { ...admin } });
  }

  return res.status(200).send({ code: 200, message: 'Admin updated' });
};

exports.getAllAdmins = async (req, res) => {
  let { page, itemsPerPage, searchText, startDate, endDate } = filterQuery(req);

  let start = new Date(startDate);
  start.setHours(0, 0, 0, 0);
  let end = new Date(endDate);
  end.setHours(23, 59, 59, 999);
  let query = {
    $and: [],
    $or: [],
  };
  if (startDate && endDate) {
    query?.$and.push({ created_at: { $gte: start, $lt: end } });
  }
  if (req.query.filterRoles && req.query.filterRoles !== '') {
    query.$and.push({
      roles: {
        $in: (await rolesFilter(req.query.filterRoles)) ?? [],
      },
    });
  }
  if (searchText && searchText !== '') {
    query.$or = [
      {
        email: { $regex: '.*' + searchText + '.*', $options: 'i' },
      },
      {
        roles: {
          $in: (await rolesFilter(searchText)) ?? [],
        },
      },
    ];
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }
  if (!query.$or.length > 0) {
    delete query.$or;
  }

  let totalItems = await ADMIN.countDocuments(query);
  if (req.query.getAll === 'true') {
    page = 1;
    itemsPerPage = totalItems;
  }
  let admins = await ADMIN.find(query)
    .populate({ path: 'roles', model: ROLE, select: 'type' })
    .sort([['created_at', -1]])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .lean();

  let data = pagination(admins, page, totalItems, itemsPerPage);

  return res.status(200).json({ ...data, code: 200 });
};

exports.getMyPermissions = async (req, res) => {
  const adminMain = await ADMIN.findById(req.admin._id).select('-password -token').lean();

  const { permissions, ...rest } = adminMain;

  let admin = adminMain;

  let roles = await ROLE.find({ _id: { $in: admin.roles } }).select('_id type permissions');

  let filterPermissions = [...new Set(roles.map(_ => _.permissions).flat())];

  let permissions_comp = await PERMISSIONS.find({
    _id: { $in: filterPermissions },
  }).select('-_id can');

  permissions_comp = permissions_comp.map(e => e.can);

  let role_type = roles.map(_ => _.type);

  return res.status(200).json({
    code: 200,
    message: 'Permissions fetched successfully',
    ...rest,
    permissions: permissions_comp,
    role_type: role_type,
  });
};

exports.forceLogoutAdmin = async (req, res) => {
  const { id } = req.params;

  const deleted = await ADMIN_JWT.findOneAndDelete({ admin_id: id });

  res.status(200).json({
    code: 200,
    success: true,
    message: 'Admin loged out',
    data: deleted,
  });
};

exports.registerAuth = async (req, res) => {
  try {
    const { email, password } = req.body;
    let admin = await ADMIN.findOne({
      email,
    }).select('-passscode -otp -updated_at -created_at');

    if (!admin || !comparePassword(password, admin.password)) {
      throw new Error('Email or Password is Incorrect:401');
    }
    if (admin?.token && admin?.token !== '') {
      return res.status(400).json({
        code: 400,
        success: true,
        message: 'You are already registered!',
      });
    }
    const secret = speakeasy.generateSecret({
      name: `[${env}]PSP TMS (${email})`,
      email: email,
    });
    const base32secret = secret.base32;
    await ADMIN.findOneAndUpdate({ email }, { token: base32secret });
    QRCode.toDataURL(secret.otpauth_url, function (err, data_url) {
      return res.status(200).send({
        code: 200,
        success: true,
        message: 'Register Success!',
        token: data_url,
      });
    });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};
