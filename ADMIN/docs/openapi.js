/**
 * @fileoverview OpenAPI configuration for ADMIN service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const adminSchemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for ADMIN service
 * @param {Object} app - Express app instance
 */
function setupAdminDocs(app) {
  const config = {
    serviceName: 'Admin',
    serviceDescription: `
      The Admin service handles administrative operations including:
      - Admin user management
      - Role and permission management
      - Authentication and authorization
      - System configuration
    `,
    version: '1.0.0',
    port: 4000,
    basePath: '/admin',
    additionalSchemas: adminSchemas,
    docsPath: '/admin/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  setupAdminDocs
};
