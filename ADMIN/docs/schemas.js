/**
 * @fileoverview OpenAPI schemas specific to ADMIN service
 */

module.exports = {
  Admin: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      email: {
        type: 'string',
        format: 'email',
        example: '<EMAIL>',
        description: 'Admin email address'
      },
      first_name: {
        type: 'string',
        example: 'John',
        description: 'Admin first name'
      },
      last_name: {
        type: 'string',
        example: 'Doe',
        description: 'Admin last name'
      },
      roles: {
        type: 'array',
        items: {
          $ref: '#/components/schemas/ObjectId'
        },
        description: 'Admin role IDs'
      },
      permissions: {
        type: 'array',
        items: {
          type: 'string'
        },
        description: 'Admin permissions'
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['email', 'first_name', 'last_name']
  },

  CreateAdminRequest: {
    type: 'object',
    properties: {
      email: {
        type: 'string',
        format: 'email',
        example: '<EMAIL>',
        description: 'Admin email address'
      },
      password: {
        type: 'string',
        minLength: 8,
        example: 'SecurePassword123!',
        description: 'Admin password'
      },
      first_name: {
        type: 'string',
        minLength: 2,
        example: 'Jane',
        description: 'Admin first name'
      },
      last_name: {
        type: 'string',
        minLength: 2,
        example: 'Smith',
        description: 'Admin last name'
      },
      role: {
        type: 'string',
        enum: ['super_admin', 'admin', 'manager'],
        example: 'admin',
        description: 'Admin role type'
      }
    },
    required: ['email', 'password', 'first_name', 'last_name', 'role']
  },

  Role: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      name: {
        type: 'string',
        example: 'Administrator',
        description: 'Role name'
      },
      type: {
        type: 'string',
        example: 'admin',
        description: 'Role type'
      },
      permissions: {
        type: 'array',
        items: {
          $ref: '#/components/schemas/ObjectId'
        },
        description: 'Permission IDs associated with this role'
      },
      description: {
        type: 'string',
        example: 'Full system administrator access',
        description: 'Role description'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['name', 'type']
  },

  Permission: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      name: {
        type: 'string',
        example: 'Create User',
        description: 'Permission name'
      },
      can: {
        type: 'string',
        example: 'create_user',
        description: 'Permission identifier'
      },
      description: {
        type: 'string',
        example: 'Allows creating new users',
        description: 'Permission description'
      },
      module: {
        type: 'string',
        example: 'users',
        description: 'Module this permission belongs to'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['name', 'can', 'module']
  },

  CreateRoleRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Store Manager',
        description: 'Role name'
      },
      type: {
        type: 'string',
        example: 'manager',
        description: 'Role type'
      },
      permissions: {
        type: 'array',
        items: {
          $ref: '#/components/schemas/ObjectId'
        },
        description: 'Permission IDs to assign to this role'
      },
      description: {
        type: 'string',
        example: 'Manages store operations',
        description: 'Role description'
      }
    },
    required: ['name', 'type', 'permissions']
  },

  CreatePermissionRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Delete Terminal',
        description: 'Permission name'
      },
      can: {
        type: 'string',
        minLength: 2,
        example: 'delete_terminal',
        description: 'Permission identifier'
      },
      description: {
        type: 'string',
        example: 'Allows deleting terminals',
        description: 'Permission description'
      },
      module: {
        type: 'string',
        example: 'terminals',
        description: 'Module this permission belongs to'
      }
    },
    required: ['name', 'can', 'module']
  },

  AdminListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Admin'
            }
          }
        }
      }
    ]
  },

  RoleListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Role'
            }
          }
        }
      }
    ]
  },

  PermissionListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Permission'
            }
          }
        }
      }
    ]
  }
};
