const { default: axios } = require('axios');
const { TERMINAL } = require('../models');

module.exports = async (req, res, next) => {
  const authHeader = req.headers['x-api-key'];

  if (!authHeader) {
    return res.status(401).send({
      code: 401,
      success: false,
      message: 'x-api-key Header Missing!',
    });
  }

  const terminal = await TERMINAL.findOne({ api_key: authHeader, is_deleted: { $ne: true } });

  if (!terminal) {
    return res.status(401).send({
      code: 401,
      success: false,
      message: 'Device not found',
    });
  }
  if (terminal.status !== 'Active') {
    return res.status(403).send({
      code: 403,
      success: false,
      message: 'Device is not active',
    });
  }

  if (authHeader && authHeader === terminal.api_key) {
    req.terminal = terminal;
    next();
  } else {
    return res.status(401).json({
      code: 401,
      success: false,
      message: 'Please provide correct API key or generate a new one',
    });
  }
};
