const express = require('express');
require('express-group-routes');
const router = express.Router();

const { IS_ADMIN, HAS_KEY } = require('./middlewares');
const tryCatch = require('./utils/tryCatch');
const { admin<PERSON>ontroller, roleController, permissionController } = require('./controllers');

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */

router.group('/v1', router => {
  // ------------------------------PUBLIC START------------------------------------ //

  /**
   * @swagger
   * /admin/v1/login:
   *   post:
   *     tags: [Authentication]
   *     summary: Admin login
   *     description: Authenticate admin user with email, password, and 2FA token
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/LoginRequest'
   *           example:
   *             email: "<EMAIL>"
   *             password: "password123"
   *             token: "123456"
   *     responses:
   *       200:
   *         description: Login successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AuthResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /admin/v1/login:
   *   post:
   *     tags: [Login Management]
   *     summary: Create login
   *     description: Create login in the admin service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create login successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/login', tryCatch(adminController.login));

  /**
   * @swagger
   * /admin/v1/register:
   *   post:
   *     tags: [Authentication]
   *     summary: Register admin (Development only)
   *     description: Register a new admin user - typically used for initial setup
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateAdminRequest'
   *     responses:
   *       201:
   *         description: Admin registered successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       409:
   *         description: Admin already exists
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   */

  /**
   * @swagger
   * /admin/v1/register:
   *   post:
   *     tags: [Register Management]
   *     summary: Create register
   *     description: Create register in the admin service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create register successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/register', tryCatch(adminController.registerAuth));
  // ------------------------------PUBLIC END------------------------------------ //

  /**
   * @swagger
   * /admin/v1/logout:
   *   delete:
   *     tags: [Authentication]
   *     summary: Admin logout
   *     description: Logout current admin user and invalidate session
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Logout successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /admin/v1/logout:
   *   delete:
   *     tags: [Logout Management]
   *     summary: Delete logout
   *     description: Delete logout in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete logout successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/logout', IS_ADMIN, tryCatch(adminController.logout));

  /**
   * @swagger
   * /admin/v1/add-admin:
   *   post:
   *     tags: [Admin Management]
   *     summary: Create new admin
   *     description: Create a new admin user with specified role and permissions
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateAdminRequest'
   *     responses:
   *       201:
   *         description: Admin created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       409:
   *         description: Admin already exists
   */

  /**
   * @swagger
   * /admin/v1/add-admin:
   *   post:
   *     tags: [Add Admin Management]
   *     summary: Create add admin
   *     description: Create add admin in the admin service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create add admin successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/add-admin', IS_ADMIN, tryCatch(adminController.addAdmin));

  /**
   * @swagger
   * /admin/v1/delete-admin/{id}:
   *   delete:
   *     tags: [Admin Management]
   *     summary: Delete admin
   *     description: Delete an admin user by ID
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Admin deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   */

  /**
   * @swagger
   * /admin/v1/delete-admin/:id:
   *   delete:
   *     tags: [Delete Admin Management]
   *     summary: Delete delete admin
   *     description: Delete delete admin in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete admin successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-admin/:id', IS_ADMIN, tryCatch(adminController.deleteAdmin));

  /**
   * @swagger
   * /admin/v1/get-all-admins:
   *   get:
   *     tags: [Admin Management]
   *     summary: Get all admins
   *     description: Retrieve a paginated list of all admin users
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Admins retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AdminListResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /admin/v1/get-all-admins:
   *   get:
   *     tags: [Get All Admins Management]
   *     summary: Get get all admins
   *     description: Retrieve get all admins in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all admins successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-admins', IS_ADMIN, tryCatch(adminController.getAllAdmins));

  /**
   * @swagger
   * /admin/v1/get-perms:
   *   get:
   *     tags: [Admin Management]
   *     summary: Get my permissions
   *     description: Get permissions for the currently authenticated admin
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Permissions retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 permissions:
   *                   type: array
   *                   items:
   *                     type: string
   *                   example: ["create_user", "delete_user", "manage_roles"]
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /admin/v1/get-perms:
   *   get:
   *     tags: [Get Perms Management]
   *     summary: Get get perms
   *     description: Retrieve get perms in the admin service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get perms successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-perms', IS_ADMIN, tryCatch(adminController.getMyPermissions));


  /**
   * @swagger
   * /admin/v1/signup:
   *   post:
   *     tags: [Signup Management]
   *     summary: Create signup
   *     description: Create signup in the admin service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create signup successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/signup', HAS_KEY, tryCatch(adminController.signup));

  /**
   * @swagger
   * /admin/v1/update-admin/:id:
   *   put:
   *     tags: [Update Admin Management]
   *     summary: Update update admin
   *     description: Update update admin in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update admin successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.patch('/update-admin/:id', IS_ADMIN, tryCatch(adminController.updateAdmin));

  /**
   * @swagger
   * /admin/v1/force-logout-admin/:id:
   *   post:
   *     tags: [Force Logout Admin Management]
   *     summary: Create force logout admin
   *     description: Create force logout admin in the admin service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create force logout admin successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/force-logout-admin/:id', IS_ADMIN, tryCatch(adminController.forceLogoutAdmin));

  // ------------------------------Roles------------------------------------ //


  /**
   * @swagger
   * /admin/v1/create-role:
   *   post:
   *     tags: [Create Role Management]
   *     summary: Create create role
   *     description: Create create role in the admin service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create role successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-role', IS_ADMIN, tryCatch(roleController.createRole));

  /**
   * @swagger
   * /admin/v1/get-all-role:
   *   get:
   *     tags: [Get All Role Management]
   *     summary: Get get all role
   *     description: Retrieve get all role in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all role successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-role', IS_ADMIN, tryCatch(roleController.getAllRoles));


  /**
   * @swagger
   * /admin/v1/update-role/:id:
   *   put:
   *     tags: [Update Role Management]
   *     summary: Update update role
   *     description: Update update role in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update role successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-role/:id', IS_ADMIN, tryCatch(roleController.updateRole));

  /**
   * @swagger
   * /admin/v1/delete-role/:id:
   *   delete:
   *     tags: [Delete Role Management]
   *     summary: Delete delete role
   *     description: Delete delete role in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete role successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-role/:id', IS_ADMIN, tryCatch(roleController.deleteRole));

  // ------------------------------Permissions------------------------------------ //


  /**
   * @swagger
   * /admin/v1/create-permission:
   *   post:
   *     tags: [Create Permission Management]
   *     summary: Create create permission
   *     description: Create create permission in the admin service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create permission successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-permission', IS_ADMIN, tryCatch(permissionController.createPermission));

  /**
   * @swagger
   * /admin/v1/get-all-permission:
   *   get:
   *     tags: [Get All Permission Management]
   *     summary: Get get all permission
   *     description: Retrieve get all permission in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all permission successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-permission', IS_ADMIN, tryCatch(permissionController.getAllPermissions));

  /**
   * @swagger
   * /admin/v1/restore-permissions:
   *   post:
   *     tags: [Restore Permissions Management]
   *     summary: Create restore permissions
   *     description: Create restore permissions in the admin service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create restore permissions successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/restore-permissions', IS_ADMIN, tryCatch(permissionController.restorePermissions));

  /**
   * @swagger
   * /admin/v1/delete-permission/:id:
   *   delete:
   *     tags: [Delete Permission Management]
   *     summary: Delete delete permission
   *     description: Delete delete permission in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete permission successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-permission/:id', IS_ADMIN, tryCatch(permissionController.deletePermission));

  /**
   * @swagger
   * /admin/v1/update-permission/:id:
   *   put:
   *     tags: [Update Permission Management]
   *     summary: Update update permission
   *     description: Update update permission in the admin service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update permission successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-permission/:id', IS_ADMIN, tryCatch(permissionController.updatePermission));

  // first time create permissions

  /**
   * @swagger
   * /admin/v1/add-permission-roles:
   *   post:
   *     tags: [Add Permission Roles Management]
   *     summary: Create add permission roles
   *     description: Create add permission roles in the admin service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create add permission roles successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/add-permission-roles', HAS_KEY, tryCatch(permissionController.addPermissions));
});

module.exports = router;
