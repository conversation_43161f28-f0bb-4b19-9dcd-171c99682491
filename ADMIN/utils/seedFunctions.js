const { first_admin_email, first_admin_password } = require('../configs');
const { PERMISSIONS, ROLE, ADMIN } = require('../models');
const { hashPassword } = require('./helper');
const mongoose = require('mongoose');

async function seedRoles() {
  try {
    const defaultPermissions = require('./defaultPermission.json');
    const defaultRoles = require('./defaultRoles');

    const roles = defaultRoles();

    const oldPerms = await PERMISSIONS.find({});

    for (const perm of defaultPermissions) {
      const isPermissionExists = oldPerms.find(oldPerm => oldPerm.can === perm.can);
      if (!isPermissionExists) {
        await PERMISSIONS.create(perm);
      } else {
        await PERMISSIONS.findByIdAndUpdate(isPermissionExists._id, perm);
      }
    }

    await Promise.all(
      roles.map(async role => {
        let old_role = await ROLE.findOne({ type: role.type });
        let new_permissions = await PERMISSIONS.find({
          can: { $in: role.permissions.map(val => val.can) },
        })
          .select('_id can')
          .lean();

        let new_permissions_id = new_permissions.map(permission => permission._id);

        if (old_role) {
          await ROLE.findOneAndUpdate({ _id: old_role._id }, { $set: { permissions: new_permissions_id } });
        } else {
          await ROLE.create({ type: role.type, permissions: new_permissions_id });
        }
      }),
    );

    console.log('Roles and permissions updated successfully.');
  } catch (error) {
    console.error('Error seeding default roles:', error);
  }
}

async function createFirstAdmin() {
  const EMAIL = first_admin_email;
  const PASSWORD = first_admin_password;
  const ADMIN_ROLE = 'SUPER_ADMIN';

  try {
    const existingAdmin = await ADMIN.findOne();

    if (existingAdmin) {
      console.log('Admin already exists. Skipping creation.');
      return;
    }

    const hashedPassword = hashPassword(PASSWORD);

    let roles = await ROLE.find({ type: ADMIN_ROLE });

    let permissions_find_array = roles.map(r => r.permissions.flat()).flat();

    permissions_find_array = permissions_find_array.map(permission => mongoose.Types.ObjectId(permission));

    let permissions = await PERMISSIONS.find({
      _id: { $in: permissions_find_array },
    });

    permissions = permissions.map(i => i.can);

    const adminCreated = await ADMIN.create({
      email: EMAIL,
      permissions,
      roles: [roles[0]._id],
      password: hashedPassword,
    });
    console.log('Admin created with this email', adminCreated.email);
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

module.exports = {
  seedRoles,
  createFirstAdmin,
};
