[{"route": "/dashboard", "description": "Can View the dashboard page in side nav", "can": "dashboard.nav", "parent": ["$"]}, {"route": "/dashboard", "description": "Can View the dashboard page in side nav", "can": "dashboard.top-search", "parent": ["dashboard"]}, {"route": "/dashboard", "description": "Can update the version of apps", "can": "dashboard.versions.update", "parent": ["dashboard"]}, {"route": "/configuration-templates", "description": "Can View the configuration-templates page in side nav", "can": "configuration-templates.nav", "parent": ["$"]}, {"route": "/configuration-templates/create", "description": "Can create the configuration template", "can": "configuration-templates.create", "parent": ["configuration-templates"]}, {"route": "/configuration-templates/edit", "description": "Can edit the configuration template", "can": "configuration-templates.edit", "parent": ["configuration-templates"]}, {"route": "/admins", "description": "Can View the admins page in side nav", "can": "admins.nav", "parent": ["$"]}, {"route": "/admins/add-admin", "description": "Can add new admin", "can": "admins.create", "parent": ["admins"]}, {"route": "/admins/edit-admin", "description": "Can edit details of an admin", "can": "admins.edit", "parent": ["admins"]}, {"route": "/admins/delete-admin", "description": "Can delete an admin", "can": "admins.delete", "parent": ["admins"]}, {"route": "/admins/force-logout", "description": "Can toggle supervisor", "can": "admins.forcelogout", "parent": ["admins"]}, {"route": "/admins/update-password", "description": "Can Update his password", "can": "admins.updatepassword", "parent": ["admins"]}, {"route": "/summary", "description": "Can view device summary page in side nav", "can": "summary.nav", "parent": ["$"]}, {"route": "/logs", "description": "Can view device logs page in side nav", "can": "terminal-logs.nav", "parent": ["$"]}, {"route": "/permissions", "description": "Can view the permissions page in side nav", "can": "permissions.nav", "parent": ["$"]}, {"route": "/permissions/add-permission", "description": "Can add new permission", "can": "permissions.create", "parent": ["permissions"]}, {"route": "/permissions/edit-permission", "description": "Can edit details of permission", "can": "permissions.edit", "parent": ["permissions"]}, {"route": "/permissions/delete-permission", "description": "Can delete permission", "can": "permissions.delete", "parent": ["permissions"]}, {"route": "/permissions/restore-permission", "description": "Can restore permission", "can": "permissions.restore", "parent": ["permissions"]}, {"route": "/roles", "description": "Can view the roles pages in side nav", "can": "roles.nav", "parent": ["$"]}, {"route": "/roles/add-role", "description": "Can add new role", "can": "roles.create", "parent": ["roles"]}, {"route": "/roles/edit-role", "description": "Can edit details of role", "can": "roles.edit", "parent": ["roles"]}, {"route": "/roles/delete-role", "description": "Can delete role", "can": "roles.delete", "parent": ["roles"]}, {"route": "/roles/restore-role", "description": "Can restore role", "can": "roles.restore", "parent": ["roles"]}, {"route": "/store/add-store", "description": "Can Add New Stores", "can": "store.add-store", "parent": ["store"]}, {"route": "/store/delete-store", "description": "Can Delete Stores", "can": "store.delete-store", "parent": ["store"]}, {"route": "/store/edit-store", "description": "Can Edit Stores", "can": "store.edit-store", "parent": ["store"]}, {"route": "/store/get-store-details", "description": "Can View Details Of Stores", "can": "store.get-store-details", "parent": ["store"]}, {"route": "/store/change-store-status", "description": "Can Change The Status Of Stores", "can": "store.change-store-status", "parent": ["store"]}, {"route": "/store/update-bins", "description": "Can Update the bin ranges Of Stores", "can": "store.update-bins", "parent": ["store"]}, {"route": "/store/device-logs", "description": "Can view the device logs", "can": "store.device-logs", "parent": ["store"]}, {"route": "/store/batch-report", "description": "Can edit the Batch Report", "can": "store.batch-report", "parent": ["store"]}, {"route": "/user/create-user", "description": "Can Create New User", "can": "user.create-user", "parent": ["user"]}, {"route": "/user/delete-user", "description": "Can Delete User", "can": "user.delete-user", "parent": ["user"]}, {"route": "/user/edit-user", "description": "Can Edit User", "can": "user.edit-user", "parent": ["user"]}, {"route": "/user/change-user-status", "description": "Can Change User Status", "can": "user.change-user-status", "parent": ["user"]}, {"route": "/user/transfer-user", "description": "Can Transfer User to other Merchant", "can": "user.transfer-user", "parent": ["user"]}, {"route": "/user/update-user", "description": "Can Update User", "can": "user.update-user", "parent": ["user"]}, {"route": "/user/bulk-upload", "description": "Can bulk upload users", "can": "user.bulk-upload", "parent": ["user"]}, {"route": "/user/user-details", "description": "Can View Details Of User", "can": "user.user-details", "parent": ["user"]}, {"route": "/manager/create-manager", "description": "<PERSON> Create New manager", "can": "manager.create-manager", "parent": ["manager"]}, {"route": "/manager/delete-manager", "description": "Can Delete manager", "can": "manager.delete-manager", "parent": ["manager"]}, {"route": "/manager/edit-manager", "description": "Can Edit manager", "can": "manager.edit-manager", "parent": ["manager"]}, {"route": "/manager/change-manager-status", "description": "Can Change manager Status", "can": "manager.change-manager-status", "parent": ["manager"]}, {"route": "/manager/transfer-manager", "description": "Can Transfer manager to other Merchant", "can": "manager.transfer-manager", "parent": ["manager"]}, {"route": "/manager/update-manager", "description": "Can Update Info Of Manager", "can": "manager.update-manager", "parent": ["manager"]}, {"route": "/owner/delete-owner", "description": "Can Delete owner", "can": "owner.delete-owner", "parent": ["owner"]}, {"route": "/owner/update-owner", "description": "Can Edit owner", "can": "owner.update-owner", "parent": ["owner"]}, {"route": "/owner/change-owner-status", "description": "Can Change owner Status", "can": "owner.change-owner-status", "parent": ["owner"]}, {"route": "/owner/create-owner", "description": "Can create owner", "can": "owner.create-owner", "parent": ["owner"]}, {"route": "/device/delete-device", "description": "Can delete the device", "can": "device.delete", "parent": ["device"]}, {"route": "/device/locate-device", "description": "Can locate the device", "can": "device.locate-device", "parent": ["device"]}, {"route": "/device/edit-device", "description": "Can update the information of device", "can": "device.edit", "parent": ["device"]}, {"route": "/device/revoke-key", "description": "Can revoke the api key", "can": "device.revoke", "parent": ["device"]}, {"route": "/device/force-deactivate", "description": "Can forcefully Disable <PERSON><PERSON> the api key", "can": "device.force-deactivate", "parent": ["device"]}, {"route": "/device/create-device", "description": "Can create a device", "can": "device.create", "parent": ["device"]}, {"route": "/device/list-configuration", "description": "Can create a device", "can": "device.see-configurations", "parent": ["device"]}, {"route": "/device/change-device-status", "description": "Can Change The Status Of Device", "can": "device.change-device-status", "parent": ["device"]}, {"route": "/device/disable-device", "description": "Can Disable Device", "can": "device.disable", "parent": ["device"]}, {"route": "/device/update-tag-string", "description": "Can Update Tag String Device", "can": "device.update-tag-string", "parent": ["device"]}, {"route": "/business/create-business", "description": "Can Create New Business", "can": "business.create-business", "parent": ["business"]}, {"route": "/business/delete-business", "description": "Can Delete Businesss", "can": "business.delete-business", "parent": ["business"]}, {"route": "/business/edit-business", "description": "Can edit Businesss", "can": "business.edit-business", "parent": ["business"]}, {"route": "/business/get-business-details", "description": "Can View Details Of Businesss", "can": "business.get-business-details", "parent": ["business"]}, {"route": "/business/change-business-status", "description": "Can Change Status Of Businesss", "can": "business.change-business-status", "parent": ["business"]}, {"route": "/user-groups/create-user-group", "description": "Can create user group", "can": "user-groups.create", "parent": ["user-groups"]}, {"route": "/user-groups/edit", "description": "Can edit user group", "can": "user-groups.edit", "parent": ["user-groups"]}, {"route": "/user-groups/delete", "description": "Can delete user group", "can": "user-groups.delete", "parent": ["user-groups"]}, {"route": "/user-groups/details", "description": "Can view user group details", "can": "user-groups.details", "parent": ["user"]}, {"route": "/device-groups/create-device-group", "description": "Can create device group", "can": "device-groups.create", "parent": ["device-groups"]}, {"route": "/device-groups/edit", "description": "Can edit device group", "can": "device-groups.edit", "parent": ["device-groups"]}, {"route": "/device-groups/delete", "description": "Can delete device group", "can": "device-groups.delete", "parent": ["device-groups"]}, {"route": "/device-groups/details", "description": "Can view device group details", "can": "device-groups.details", "parent": ["device-groups"]}, {"route": "/device-groups/see-configurations", "description": "Can view device group configurations", "can": "device-groups.see-configurations", "parent": ["device-groups"]}, {"route": "/certificates", "description": "Can view the certificates pages in side nav", "can": "certificates.nav", "parent": ["$"]}, {"route": "/merchant-tool", "description": "Can view the merchant-tool pages in side nav", "can": "merchant-tool.nav", "parent": ["$"]}, {"route": "/certificate/add-certificate", "description": "Can create certificates", "can": "certificate.create", "parent": ["certificates"]}, {"route": "/tag-templates", "description": "Can View the tag-templates page in side nav", "can": "tag-templates.nav", "parent": ["$"]}, {"route": "/tag-templates/list-tags", "description": "Can View the tag modal", "can": "tag-templates.list-tags", "parent": ["tag-templates"]}, {"route": "/tag-templates/create-tags", "description": "Can View the tag create modal button", "can": "tag-templates.create-tag", "parent": ["tag-templates"]}, {"route": "/tag-templates/create", "description": "Can create the tag template", "can": "tag-templates.create", "parent": ["tag-templates"]}, {"route": "/tag-templates/edit", "description": "Can edit the tag template", "can": "tag-templates.edit", "parent": ["tag-templates"]}, {"route": "/tag-templates/delete", "description": "Can delete the tag template", "can": "tag-templates.delete", "parent": ["tag-templates"]}, {"route": "/bin-ranges", "description": "Can View the bin ranges page in side nav", "can": "bin-ranges.nav", "parent": ["$"]}, {"route": "/business/login-as-merchant", "description": "Can login as merchant", "can": "business.login-as-merchant", "parent": ["business"]}, {"route": "/iso", "description": "Can view the iso pages in side nav", "can": "iso.nav", "parent": ["$"]}, {"route": "/iso/add", "description": "Can add new iso", "can": "iso.add", "parent": ["iso"]}, {"route": "/iso/edit", "description": "Can edit iso", "can": "iso.edit", "parent": ["iso"]}, {"route": "/iso/delete", "description": "Can delete iso", "can": "iso.delete", "parent": ["iso"]}, {"route": "/iso/see-details", "description": "Can view details of iso", "can": "iso.see-details", "parent": ["iso"]}, {"route": "/merchant-fees", "description": "Can view the merchant fees page in side nav", "can": "merchant-fees.nav", "parent": ["$"]}, {"route": "/merchant-fees/upload", "description": "Can upload merchant fees", "can": "merchant-fees.upload", "parent": ["merchant-fees"]}, {"route": "/merchant-fees/download", "description": "Can download merchant fees", "can": "merchant-fees.download", "parent": ["merchant-fees"]}, {"route": "/merchant-fees/edit", "description": "Can edit merchant fees", "can": "merchant-fees.edit", "parent": ["merchant-fees"]}, {"route": "/upload-statements", "description": "Can upload the upload-statements page in side nav", "can": "merchant-fees.upload-statements", "parent": ["merchant-fees"]}, {"route": "/device-update-otid", "description": "Can update the OTID of terminal", "can": "device.update-otid", "parent": ["device"]}]