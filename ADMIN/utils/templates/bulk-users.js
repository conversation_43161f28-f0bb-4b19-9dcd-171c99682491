module.exports = data => {
  return `<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Bulk Users</title>
    </head>
    <body>
    <div style="text-align: center; padding: 20px; background-color: #f5f5f5; border-radius: 10px;">
    <h1 style="color: #000; font-size: 30px; font-weight: 600;">Bulk Users Uploaded</h1>

    <table style="width: 100%; border-collapse: collapse;">
    <thead>
        <tr>
            <th style="border: 1px solid #ddd; padding: 8px;">Name</th>
            <th style="border: 1px solid #ddd; padding: 8px;">Email</th>
            <th style="border: 1px solid #ddd; padding: 8px;">Type</th>
        </tr>
    </thead>
    <tbody>
        ${data
          .map(
            item => `
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">${item.name}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${item.email}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${item.type}</td>
                </tr>
            `,
          )
          .join('')}
    </tbody>
</table>
</div>
    </body>
</html>`;
};
