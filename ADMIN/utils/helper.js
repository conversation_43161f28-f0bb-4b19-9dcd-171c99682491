const jwt = require('jsonwebtoken');
const jwtDecode = require('jwt-decode');
const bcryptjs = require('bcryptjs');
const { ROLE } = require('../models');
const { secret } = require('../configs');

module.exports = {
  hashPassword: password => {
    const salt = bcryptjs.genSaltSync(10);
    const passwordHashed = bcryptjs.hashSync(password, salt);
    return passwordHashed;
  },
  generateToken: payload => {
    const token = jwt.sign(payload, secret, {
      expiresIn: '30 days', // 120 minutes (2 hours)
      algorithm: 'HS256',
    });
    return token;
  },
  decryptToken: token => {
    const decrypted = jwtDecode(token);
    const iat = new Date(decrypted.iat * 1000);
    const exp = new Date(iat.getTime() + 30 * 60000);
    return {
      iat: iat,
      exp: exp,
    };
  },
  comparePassword: (plainPassword, hashedPassword) => bcryptjs.compareSync(plainPassword, hashedPassword),
  pagination: (items = [], page = 1, totalItems = 0, itemsPerPage = 5) => {
    return {
      currentPage: page,
      hasNextPage: itemsPerPage * page < totalItems,
      hasPreviousPage: page > 1,
      nextPage: page + 1,
      previousPage: page - 1,
      lastPage: Math.ceil(totalItems / itemsPerPage),
      totalItems: totalItems,
      items: items,
    };
  },
  rolesFilter: async query => {
    const myQuery = {
      type: { $regex: '.*' + query + '.*', $options: 'i' },
    };

    const rolesFilter = await ROLE.find(myQuery).select('_id');
    return rolesFilter.map(e => e._id);
  },
  filterQuery: req => ({
    ...req.query,
    page: req.query.page ? Number(req.query.page) : 1,
    itemsPerPage: req.query.itemsPerPage
      ? Number(req.query.itemsPerPage)
      : req.query.perPage
      ? Number(req.query.perPage)
      : 10,
    searchText:
      req.query.searchText !== 'null' && req.query.searchText !== 'undefined' && req.query.searchText
        ? req.query.searchText
        : '',
    startDate:
      req.query.startDate !== 'null' && req.query.startDate !== 'undefined' && req.query.startDate
        ? req.query.startDate
        : '',
    endDate:
      req.query.endDate !== 'null' && req.query.endDate !== 'undefined' && req.query.endDate ? req.query.endDate : '',
    storeId:
      req.query.storeId !== 'null' && req.query.storeId !== 'undefined' && req.query.storeId ? req.query.storeId : '',
  }),
  readAllSchemaFiles: async () => {
    const fs = require('fs');
    const path = require('path');
    const createCsvWriter = require('csv-writer').createObjectCsvWriter;

    const directoryPath = path.join(__dirname, '../confirmed_models');
    const csvPath = path.join(__dirname, '../utils/csv');

    const files = await new Promise((resolve, reject) => {
      fs.readdir(directoryPath, (err, files) => {
        if (err) {
          reject(err);
        } else {
          resolve(files);
        }
      });
    });

    files.forEach(async file => {
      let csvFileName = file.replace('.js', '.csv');
      const filePath = path.join(directoryPath, file);

      // Require each file
      let model = require(filePath);
      if (typeof model === 'function') {
        const newObject = Object.entries(model.schema.paths).reduce((acc, [key, payload]) => {
          acc.push({
            params: key,
            requred: payload?.isRequired ? 'Y' : 'O',
            unique: payload?.options?.unique ? 'Y' : 'N',
            data_type: payload?.instance,
            enum_values: payload?.enumValues?.length ? payload?.enumValues : '',
            default_values: payload?.options?.default ? payload?.options?.default : '',
          });
          return acc;
        }, []);

        let csvWritePath = path.join(csvPath, csvFileName);

        const csvWriter = createCsvWriter({
          path: csvWritePath,
          header: Object.keys(newObject[0]).map(key => ({
            id: key,
            title: key.toUpperCase(),
          })),
        });
        await csvWriter.writeRecords(newObject);
      }
    });
  },
};
