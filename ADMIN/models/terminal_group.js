const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const terminalGroupSchema = new Schema(
  {
    name: {
      type: String,
      unique: true,
    },
    store_id: {
      type: Schema.Types.ObjectId,
      ref: 'store',
      required: true,
    },
    user_group_id: [
      {
        type: Schema.Types.ObjectId,
        ref: 'user_group',
      },
    ],
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

terminalGroupSchema.plugin(uniqueValidator);

const TERMINAL_GROUP = model('terminal_group', terminalGroupSchema);
module.exports = TERMINAL_GROUP;
