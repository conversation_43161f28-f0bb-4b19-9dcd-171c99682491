module.exports = {
  ADMIN: require('./admin'),
  ADMIN_JWT: require('./admin_jwt'),
  STORE: require('./store'),
  BUSINESS: require('./business'),
  PERMISSIONS: require('./permissions'),
  ROLE: require('./role'),
  TERMINAL: require('./terminal'),
  USER: require('./user'),
  USER_GROUP: require('./user_group'),
  TERMINAL_GROUP: require('./terminal_group'),
  CONFIGURATION_TEMPLATE: require('./configuration_template'),
  CONFIGURATION: require('./configuration'),
  RESTRICTED_PINS: require('./restricted_pins'),
  DEVICE_LOGS: require('./device_logs'),
  CARD_DATA: require('./cards_data'),
  MESSAGE_QUEUE: require('./message_queue'),
  CERTIFICATES: require('./certificates'),
  RECEIPTS_DATA: require('./recipts_data'),
  TAG_TEMPLATE: require('./tag_template'),
  EMV_TAG: require('./tag'),
  TERMINAL_REPORT: require('./terminal_report'),
  BIN_RANGE: require('./bin-range'),
};
