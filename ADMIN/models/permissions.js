const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const permissionsSchema = new Schema(
  {
    route: { type: String, index: true },
    description: { type: String },
    can: { type: String, unique: true, lowercase: true },
    parent: { type: [String] },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

permissionsSchema.plugin(uniqueValidator);

const PERMISSIONS = model('permissions', permissionsSchema);

module.exports = PERMISSIONS;
