/**
 * @fileoverview Base OpenAPI 3.1 configuration for PSP Backend microservices
 */

const commonSchemas = require('./schemas/common');

/**
 * Creates base OpenAPI configuration for a microservice
 * @param {Object} serviceConfig - Service-specific configuration
 * @returns {Object} OpenAPI configuration
 */
function createBaseConfig(serviceConfig) {
  const {
    serviceName,
    serviceDescription,
    version = '1.0.0',
    port,
    basePath = '',
    additionalSchemas = {}
  } = serviceConfig;

  return {
    openapi: '3.1.0',
    info: {
      title: `PSP Backend - ${serviceName} Service`,
      description: serviceDescription,
      version: version,
      contact: {
        name: 'PSP Development Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'Proprietary',
        url: 'https://pspservicesco.com/license'
      }
    },
    servers: [
      {
        url: `http://localhost:${port}${basePath}`,
        description: 'Local development server'
      },
      {
        url: `https://tms-dev.pspservicesco.com${basePath}`,
        description: 'Development server'
      },
      {
        url: `https://tms.pspservicesco.com${basePath}`,
        description: 'Production server'
      }
    ],
    components: {
      schemas: {
        ...commonSchemas,
        ...additionalSchemas
      },
      securitySchemes: {
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for admin authentication'
        },
        ApiKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'x-api-key',
          description: 'API key for device authentication'
        },
        AccessKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'Authorization',
          description: 'Access key for internal service communication'
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Authentication information is missing or invalid',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                code: 401,
                success: false,
                message: 'Unauthorized access'
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Access forbidden',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                code: 403,
                success: false,
                message: 'Access forbidden'
              }
            }
          }
        },
        NotFoundError: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                code: 404,
                success: false,
                message: 'Resource not found'
              }
            }
          }
        },
        ValidationError: {
          description: 'Validation error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                code: 400,
                success: false,
                message: 'Validation failed'
              }
            }
          }
        },
        InternalServerError: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                code: 500,
                success: false,
                message: 'Internal server error'
              }
            }
          }
        }
      },
      parameters: {
        PageQuery: {
          name: 'page',
          in: 'query',
          description: 'Page number for pagination',
          required: false,
          schema: {
            type: 'integer',
            minimum: 1,
            default: 1
          }
        },
        ItemsPerPageQuery: {
          name: 'itemsPerPage',
          in: 'query',
          description: 'Number of items per page',
          required: false,
          schema: {
            type: 'integer',
            minimum: 1,
            maximum: 100,
            default: 10
          }
        },
        SearchQuery: {
          name: 'search',
          in: 'query',
          description: 'Search query string',
          required: false,
          schema: {
            type: 'string'
          }
        },
        ObjectIdPath: {
          name: 'id',
          in: 'path',
          description: 'MongoDB ObjectId',
          required: true,
          schema: {
            $ref: '#/components/schemas/ObjectId'
          }
        }
      }
    },
    tags: [
      {
        name: 'Health',
        description: 'Service health and status endpoints'
      },
      {
        name: 'Authentication',
        description: 'Authentication and authorization endpoints'
      }
    ]
  };
}

module.exports = {
  createBaseConfig,
  commonSchemas
};
