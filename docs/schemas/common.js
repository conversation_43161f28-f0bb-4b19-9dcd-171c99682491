/**
 * @fileoverview Common OpenAPI 3.1 schemas shared across all microservices
 */

module.exports = {
  // Common response schemas
  SuccessResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        example: 200,
        description: 'HTTP status code'
      },
      success: {
        type: 'boolean',
        example: true,
        description: 'Operation success status'
      },
      message: {
        type: 'string',
        example: 'Operation completed successfully',
        description: 'Response message'
      },
      data: {
        type: 'object',
        description: 'Response data payload'
      }
    },
    required: ['code', 'success', 'message']
  },

  ErrorResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        example: 400,
        description: 'HTTP status code'
      },
      success: {
        type: 'boolean',
        example: false,
        description: 'Operation success status'
      },
      message: {
        type: 'string',
        example: 'Validation error',
        description: 'Error message'
      },
      error: {
        type: 'boolean',
        example: true,
        description: 'Error flag'
      }
    },
    required: ['code', 'success', 'message']
  },

  PaginationQuery: {
    type: 'object',
    properties: {
      page: {
        type: 'integer',
        minimum: 1,
        default: 1,
        description: 'Page number'
      },
      itemsPerPage: {
        type: 'integer',
        minimum: 1,
        maximum: 100,
        default: 10,
        description: 'Items per page'
      },
      search: {
        type: 'string',
        description: 'Search query'
      },
      sortBy: {
        type: 'string',
        description: 'Field to sort by'
      },
      sortOrder: {
        type: 'string',
        enum: ['asc', 'desc'],
        default: 'desc',
        description: 'Sort order'
      }
    }
  },

  PaginatedResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        example: 200
      },
      success: {
        type: 'boolean',
        example: true
      },
      message: {
        type: 'string',
        example: 'Data retrieved successfully'
      },
      data: {
        type: 'array',
        items: {
          type: 'object'
        }
      },
      pagination: {
        type: 'object',
        properties: {
          currentPage: {
            type: 'integer',
            example: 1
          },
          totalPages: {
            type: 'integer',
            example: 10
          },
          totalItems: {
            type: 'integer',
            example: 100
          },
          itemsPerPage: {
            type: 'integer',
            example: 10
          }
        }
      }
    }
  },

  // Common entity schemas
  ObjectId: {
    type: 'string',
    pattern: '^[0-9a-fA-F]{24}$',
    example: '507f1f77bcf86cd799439011',
    description: 'MongoDB ObjectId'
  },

  Timestamp: {
    type: 'string',
    format: 'date-time',
    example: '2023-12-01T10:30:00.000Z',
    description: 'ISO 8601 timestamp'
  },

  Status: {
    type: 'string',
    enum: ['Active', 'Inactive', 'Pending', 'Suspended'],
    description: 'Entity status'
  },

  // Authentication schemas
  LoginRequest: {
    type: 'object',
    properties: {
      email: {
        type: 'string',
        format: 'email',
        example: '<EMAIL>',
        description: 'User email address'
      },
      password: {
        type: 'string',
        minLength: 6,
        example: 'password123',
        description: 'User password'
      },
      token: {
        type: 'string',
        example: '123456',
        description: '2FA token'
      }
    },
    required: ['email', 'password']
  },

  AuthResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        example: 200
      },
      success: {
        type: 'boolean',
        example: true
      },
      message: {
        type: 'string',
        example: 'Login successful!'
      },
      token: {
        type: 'string',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        description: 'JWT authentication token'
      },
      admin: {
        type: 'object',
        description: 'User information'
      }
    }
  }
};
