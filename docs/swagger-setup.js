/**
 * @fileoverview Swagger setup utility for PSP Backend microservices
 */

const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const { createBaseConfig } = require('./openapi-base');

/**
 * Sets up Swagger documentation for a microservice
 * @param {Object} app - Express app instance
 * @param {Object} config - Service configuration
 */
function setupSwagger(app, config) {
  const {
    serviceName,
    serviceDescription,
    version,
    port,
    basePath,
    routeFiles = [],
    additionalSchemas = {},
    docsPath = '/api-docs'
  } = config;

  // Create base OpenAPI configuration
  const baseConfig = createBaseConfig({
    serviceName,
    serviceDescription,
    version,
    port,
    basePath,
    additionalSchemas
  });

  // Swagger JSDoc options
  const options = {
    definition: baseConfig,
    apis: [
      './routes.js',
      './routes/*.js',
      './controllers/*.js',
      './docs/schemas.js',
      ...routeFiles
    ]
  };

  // Generate OpenAPI specification
  const specs = swaggerJsdoc(options);

  // Custom CSS for better UI
  const customCss = `
    .swagger-ui .topbar { display: none; }
    .swagger-ui .info .title { color: #2c3e50; }
    .swagger-ui .scheme-container { background: #f8f9fa; padding: 10px; border-radius: 5px; }
  `;

  const swaggerOptions = {
    customCss,
    customSiteTitle: `${serviceName} API Documentation`,
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      docExpansion: 'none',
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      tryItOutEnabled: true
    }
  };

  // Serve Swagger UI
  app.use(docsPath, swaggerUi.serve);
  app.get(docsPath, swaggerUi.setup(specs, swaggerOptions));

  // Serve raw OpenAPI JSON
  app.get(`${docsPath}.json`, (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });

  // Health check endpoint with OpenAPI documentation
  /**
   * @swagger
   * /health:
   *   get:
   *     tags: [Health]
   *     summary: Service health check
   *     description: Returns the health status of the service
   *     responses:
   *       200:
   *         description: Service is healthy
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 mongo_db:
   *                   type: string
   *                   example: "Connection state is 1"
   *                   description: "MongoDB connection status"
   */

  console.log(`📚 Swagger documentation available at: http://localhost:${port}${docsPath}`);
  console.log(`📄 OpenAPI JSON available at: http://localhost:${port}${docsPath}.json`);

  return specs;
}

/**
 * Validates OpenAPI specification
 * @param {Object} specs - OpenAPI specification
 */
async function validateOpenAPISpec(specs) {
  try {
    const SwaggerParser = require('@apidevtools/swagger-parser');
    await SwaggerParser.validate(specs);
    console.log('✅ OpenAPI specification is valid');
    return true;
  } catch (error) {
    console.error('❌ OpenAPI specification validation failed:', error.message);
    return false;
  }
}

/**
 * Middleware to validate requests against OpenAPI schema
 * @param {Object} specs - OpenAPI specification
 */
function createValidationMiddleware(specs) {
  return (req, res, next) => {
    // Add request validation logic here if needed
    // This is a placeholder for future implementation
    next();
  };
}

/**
 * JSDoc template for common API responses
 */
const responseTemplates = {
  success: `
   *         200:
   *           description: Success
   *           content:
   *             application/json:
   *               schema:
   *                 $ref: '#/components/schemas/SuccessResponse'`,
  
  created: `
   *         201:
   *           description: Created successfully
   *           content:
   *             application/json:
   *               schema:
   *                 $ref: '#/components/schemas/SuccessResponse'`,
  
  badRequest: `
   *         400:
   *           $ref: '#/components/responses/ValidationError'`,
  
  unauthorized: `
   *         401:
   *           $ref: '#/components/responses/UnauthorizedError'`,
  
  forbidden: `
   *         403:
   *           $ref: '#/components/responses/ForbiddenError'`,
  
  notFound: `
   *         404:
   *           $ref: '#/components/responses/NotFoundError'`,
  
  serverError: `
   *         500:
   *           $ref: '#/components/responses/InternalServerError'`
};

module.exports = {
  setupSwagger,
  validateOpenAPISpec,
  createValidationMiddleware,
  responseTemplates
};
