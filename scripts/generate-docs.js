#!/usr/bin/env node

/**
 * @fileoverview Script to generate OpenAPI documentation for all microservices
 */

const fs = require('fs');
const path = require('path');

const services = [
  { name: 'ADMIN', port: 4000, description: 'Admin management and authentication service' },
  { name: 'TERMINAL', port: 4001, description: 'Terminal management and operations service' },
  { name: 'STORE', port: 4002, description: 'Store management and configuration service' },
  { name: 'BUSINESS', port: 4003, description: 'Business logic and operations service' },
  { name: 'USERS', port: 4004, description: 'User management and authentication service' },
  { name: 'COMMON', port: 4005, description: 'Common utilities and shared functionality service' },
  { name: 'CONFIG', port: 4006, description: 'Configuration management service' },
  { name: 'AMEX', port: 4008, description: 'American Express integration service' }
];

/**
 * Creates basic schemas file for a service
 */
function createSchemasFile(serviceName) {
  const entityName = serviceName.charAt(0).toUpperCase() + serviceName.slice(1).toLowerCase();
  
  return `/**
 * @fileoverview OpenAPI schemas specific to ${serviceName} service
 */

module.exports = {
  ${entityName}: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      name: {
        type: 'string',
        example: 'Sample ${entityName}',
        description: '${entityName} name'
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['name']
  },

  Create${entityName}Request: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'New ${entityName}',
        description: '${entityName} name'
      },
      description: {
        type: 'string',
        example: '${entityName} description',
        description: '${entityName} description'
      }
    },
    required: ['name']
  },

  Update${entityName}Request: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Updated ${entityName}',
        description: '${entityName} name'
      },
      description: {
        type: 'string',
        example: 'Updated description',
        description: '${entityName} description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  ${entityName}ListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/${entityName}'
            }
          }
        }
      }
    ]
  }
};`;
}

/**
 * Creates OpenAPI config file for a service
 */
function createOpenAPIFile(serviceName, port, description) {
  const functionName = `setup${serviceName.charAt(0).toUpperCase() + serviceName.slice(1).toLowerCase()}Docs`;

  return `/**
 * @fileoverview OpenAPI configuration for ${serviceName} service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const ${serviceName.toLowerCase()}Schemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for ${serviceName} service
 * @param {Object} app - Express app instance
 */
function ${functionName}(app) {
  const config = {
    serviceName: '${serviceName}',
    serviceDescription: \`
      ${description}

      Key features:
      - Entity management
      - Authentication and authorization
      - Data validation and processing
    \`,
    version: '1.0.0',
    port: ${port},
    basePath: '/${serviceName.toLowerCase()}',
    additionalSchemas: ${serviceName.toLowerCase()}Schemas,
    docsPath: '/${serviceName.toLowerCase()}/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  ${functionName}
};`;
}

/**
 * Creates documentation structure for all services
 */
function generateDocumentation() {
  console.log('Generating OpenAPI documentation structure for all services...\n');

  services.forEach(service => {
    const servicePath = path.join(__dirname, '..', service.name);
    const docsPath = path.join(servicePath, 'docs');

    // Create docs directory if it doesn't exist
    if (!fs.existsSync(docsPath)) {
      fs.mkdirSync(docsPath, { recursive: true });
      console.log(`Created docs directory for ${service.name}`);
    }

    // Create schemas.js file
    const schemasPath = path.join(docsPath, 'schemas.js');
    if (!fs.existsSync(schemasPath)) {
      fs.writeFileSync(schemasPath, createSchemasFile(service.name));
      console.log(`Created schemas.js for ${service.name}`);
    }

    // Create openapi.js file
    const openApiPath = path.join(docsPath, 'openapi.js');
    if (!fs.existsSync(openApiPath)) {
      fs.writeFileSync(openApiPath, createOpenAPIFile(service.name, service.port, service.description));
      console.log(`Created openapi.js for ${service.name}`);
    }
  });

  console.log('\nDocumentation structure generated successfully!');
  console.log('\nNext steps:');
  console.log('1. Update each service\'s server.js to include OpenAPI setup');
  console.log('2. Add JSDoc comments to route files');
  console.log('3. Customize schemas for each service');
  console.log('4. Test documentation at /service-name/api-docs');
  console.log('\nSee docs/implementation-guide.md for detailed instructions');
}

// Run the script
if (require.main === module) {
  generateDocumentation();
}

module.exports = {
  generateDocumentation,
  services
};
