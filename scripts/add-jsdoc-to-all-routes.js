#!/usr/bin/env node

/**
 * @fileoverview Script to add JSDoc comments to all routes across all microservices
 * This script analyzes route files and adds comprehensive OpenAPI documentation
 */

const fs = require('fs');
const path = require('path');

// Service configurations with their route patterns
const services = [
  { name: 'ADMIN', basePath: '/admin' },
  { name: 'TERMINAL', basePath: '/terminal' },
  { name: 'STORE', basePath: '/store' },
  { name: 'BUSINESS', basePath: '/business' },
  { name: 'USERS', basePath: '/users' },
  { name: 'COMMON', basePath: '/common' },
  { name: 'CONFIG', basePath: '/config' },
  { name: 'AMEX', basePath: '/amex' }
];

// Common JSDoc templates
const templates = {
  securitySchemes: `/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */`,

  // Template for different HTTP methods
  post: (path, tag, summary, description, security = true, hasBody = true) => `
  /**
   * @swagger
   * ${path}:
   *   post:
   *     tags: [${tag}]
   *     summary: ${summary}
   *     description: ${description}${security ? `
   *     security:
   *       - BearerAuth: []` : ''}${hasBody ? `
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object` : ''}
   *     responses:
   *       200:
   *         description: ${summary} successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'${security ? `
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'` : ''}
   */`,

  get: (path, tag, summary, description, security = true, hasParams = false) => `
  /**
   * @swagger
   * ${path}:
   *   get:
   *     tags: [${tag}]
   *     summary: ${summary}
   *     description: ${description}${security ? `
   *     security:
   *       - BearerAuth: []` : ''}${hasParams ? `
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'` : ''}
   *     responses:
   *       200:
   *         description: ${summary} successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'${security ? `
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'` : ''}
   */`,

  put: (path, tag, summary, description, security = true) => `
  /**
   * @swagger
   * ${path}:
   *   put:
   *     tags: [${tag}]
   *     summary: ${summary}
   *     description: ${description}${security ? `
   *     security:
   *       - BearerAuth: []` : ''}
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: ${summary} successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'${security ? `
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'` : ''}
   */`,

  delete: (path, tag, summary, description, security = true) => `
  /**
   * @swagger
   * ${path}:
   *   delete:
   *     tags: [${tag}]
   *     summary: ${summary}
   *     description: ${description}${security ? `
   *     security:
   *       - BearerAuth: []` : ''}
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: ${summary} successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'${security ? `
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'` : ''}
   */`
};

/**
 * Parse route line to extract method, path, and middleware
 */
function parseRouteLine(line) {
  const routeRegex = /router\.(get|post|put|patch|delete)\(['"`]([^'"`]+)['"`](?:,\s*([^,]+))?,\s*tryCatch\(([^)]+)\)/;
  const match = line.match(routeRegex);
  
  if (match) {
    return {
      method: match[1],
      path: match[2],
      middleware: match[3] || '',
      controller: match[4]
    };
  }
  return null;
}

/**
 * Generate JSDoc comment for a route
 */
function generateJSDoc(route, serviceName, basePath) {
  const { method, path, middleware } = route;
  const fullPath = `${basePath}/v1${path}`;
  
  // Determine security based on middleware
  const hasAuth = middleware.includes('IS_ADMIN') || middleware.includes('HAS_API_KEY');
  
  // Generate tag and summary based on path
  const pathParts = path.split('/').filter(p => p && !p.startsWith(':'));
  const tag = pathParts.length > 0 ? 
    pathParts[0].split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ') + ' Management' :
    serviceName + ' Operations';
  
  const summary = generateSummary(method, path);
  const description = generateDescription(method, path, serviceName);
  
  // Use appropriate template
  switch (method) {
    case 'post':
      return templates.post(fullPath, tag, summary, description, hasAuth, true);
    case 'get':
      const hasListParams = path.includes('get-all') || path.includes('list');
      return templates.get(fullPath, tag, summary, description, hasAuth, hasListParams);
    case 'put':
    case 'patch':
      return templates.put(fullPath, tag, summary, description, hasAuth);
    case 'delete':
      return templates.delete(fullPath, tag, summary, description, hasAuth);
    default:
      return templates.get(fullPath, tag, summary, description, hasAuth, false);
  }
}

/**
 * Generate summary from method and path
 */
function generateSummary(method, path) {
  const action = method === 'get' ? 'Get' : 
                method === 'post' ? 'Create' :
                method === 'put' || method === 'patch' ? 'Update' :
                method === 'delete' ? 'Delete' : 'Process';
  
  const resource = path.split('/').filter(p => p && !p.startsWith(':')).join(' ').replace(/-/g, ' ');
  return `${action} ${resource}`;
}

/**
 * Generate description from method, path and service
 */
function generateDescription(method, path, serviceName) {
  const resource = path.split('/').filter(p => p && !p.startsWith(':')).join(' ').replace(/-/g, ' ');
  const action = method === 'get' ? 'retrieve' : 
                method === 'post' ? 'create' :
                method === 'put' || method === 'patch' ? 'update' :
                method === 'delete' ? 'delete' : 'process';
  
  return `${action.charAt(0).toUpperCase() + action.slice(1)} ${resource} in the ${serviceName.toLowerCase()} service`;
}

/**
 * Process a single service's routes file
 */
function processServiceRoutes(serviceName) {
  const routesPath = path.join(__dirname, '..', serviceName, 'routes.js');
  
  if (!fs.existsSync(routesPath)) {
    console.log(`Routes file not found for ${serviceName}: ${routesPath}`);
    return false;
  }

  console.log(`Processing ${serviceName} routes...`);
  
  const content = fs.readFileSync(routesPath, 'utf8');
  const lines = content.split('\n');
  
  // Count existing JSDoc comments vs total routes
  const existingJSDocCount = (content.match(/@swagger/g) || []).length - 1; // -1 for security schemes
  const totalRoutes = (content.match(/router\.(get|post|put|patch|delete)\(/g) || []).length;

  if (existingJSDocCount >= totalRoutes && existingJSDocCount > 0) {
    console.log(`${serviceName} already has JSDoc comments (${existingJSDocCount}/${totalRoutes} routes)`);
    return true;
  }

  console.log(`Adding JSDoc to ${serviceName} (${existingJSDocCount}/${totalRoutes} routes documented)`);

  // If partially documented, we need to be more careful
  const hasPartialDocs = existingJSDocCount > 0;
  
  const newLines = [];
  let addedSecuritySchemes = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Add security schemes after imports
    if (!addedSecuritySchemes && line.includes('router.group')) {
      newLines.push('');
      newLines.push(templates.securitySchemes);
      newLines.push('');
      addedSecuritySchemes = true;
    }
    
    newLines.push(line);
    
    // Check if this line contains a route definition
    const route = parseRouteLine(line);
    if (route) {
      // Check if this route already has JSDoc (look at previous lines)
      const hasExistingJSDoc = newLines.slice(-10).some(prevLine =>
        prevLine.includes('@swagger') && prevLine.includes(route.path)
      );

      if (!hasExistingJSDoc) {
        const basePath = services.find(s => s.name === serviceName)?.basePath || `/${serviceName.toLowerCase()}`;
        const jsDoc = generateJSDoc(route, serviceName, basePath);

        // Insert JSDoc before the route line
        const jsDocLines = jsDoc.split('\n');
        newLines.splice(newLines.length - 1, 0, ...jsDocLines);
      }
    }
  }
  
  // Write back to file
  fs.writeFileSync(routesPath, newLines.join('\n'));
  console.log(`Added JSDoc comments to ${serviceName} routes`);
  return true;
}

/**
 * Process all services
 */
function processAllServices() {
  console.log('Adding JSDoc comments to all service routes...\n');
  
  let processed = 0;
  let total = services.length;
  
  services.forEach(service => {
    if (processServiceRoutes(service.name)) {
      processed++;
    }
  });
  
  console.log(`\nProcessed ${processed}/${total} services successfully!`);
  console.log('\nNext steps:');
  console.log('1. Run: npm run generate-openapi');
  console.log('2. Check generated specs in generated-specs/ folder');
  console.log('3. View documentation in Swagger Editor');
}

// Run the script
if (require.main === module) {
  processAllServices();
}

module.exports = {
  processAllServices,
  processServiceRoutes
};
