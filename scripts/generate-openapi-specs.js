#!/usr/bin/env node

/**
 * @fileoverview Generate static OpenAPI JSON files for all microservices
 * This script creates OpenAPI 3.1 specification files that can be viewed in any OpenAPI editor
 */

const fs = require('fs');
const path = require('path');
const swaggerJsdoc = require('swagger-jsdoc');

// Service configurations
const services = [
  { name: 'ADMIN', port: 4000, description: 'Admin management and authentication service' },
  { name: 'TERMINAL', port: 4001, description: 'Terminal management and operations service' },
  { name: 'STORE', port: 4002, description: 'Store management and configuration service' },
  { name: 'BUSINESS', port: 4003, description: 'Business logic and operations service' },
  { name: 'USERS', port: 4004, description: 'User management and authentication service' },
  { name: 'COMMON', port: 4005, description: 'Common utilities and shared functionality service' },
  { name: 'CONFIG', port: 4006, description: 'Configuration management service' },
  { name: 'AMEX', port: 4008, description: 'American Express integration service' }
];

// Import base configuration and schemas
const { createBaseConfig } = require('../docs/openapi-base');

/**
 * Generate OpenAPI specification for a service
 * @param {Object} serviceConfig - Service configuration
 * @returns {Object} OpenAPI specification
 */
function generateServiceSpec(serviceConfig) {
  const { name, port, description } = serviceConfig;
  
  try {
    // Load service-specific schemas
    const schemasPath = path.join(__dirname, '..', name, 'docs', 'schemas.js');
    let serviceSchemas = {};
    
    if (fs.existsSync(schemasPath)) {
      serviceSchemas = require(schemasPath);
    }

    // Create base configuration
    const baseConfig = createBaseConfig({
      serviceName: name,
      serviceDescription: description,
      version: '1.0.0',
      port: port,
      basePath: `/${name.toLowerCase()}`,
      additionalSchemas: serviceSchemas
    });

    // Define API file paths to scan for JSDoc comments
    const servicePath = path.join(__dirname, '..', name);
    const apiPaths = [
      path.join(servicePath, 'routes.js'),
      path.join(servicePath, 'controllers', '*.js'),
      path.join(servicePath, 'docs', 'schemas.js')
    ];

    // Filter existing files
    const existingPaths = apiPaths.filter(apiPath => {
      if (apiPath.includes('*')) {
        const dir = path.dirname(apiPath);
        return fs.existsSync(dir);
      }
      return fs.existsSync(apiPath);
    });

    // Generate OpenAPI specification using swagger-jsdoc
    const options = {
      definition: baseConfig,
      apis: existingPaths
    };

    const specs = swaggerJsdoc(options);
    
    // Add service-specific information
    specs.info.title = `${name} Service API`;
    specs.info.description = `${description}\n\nGenerated on: ${new Date().toISOString()}`;
    
    return specs;
    
  } catch (error) {
    console.error(`❌ Error generating spec for ${name}:`, error.message);
    return null;
  }
}

/**
 * Generate OpenAPI specs for all services
 */
function generateAllSpecs() {
  console.log('Generating OpenAPI 3.1 specifications for all services...\n');

  // Create output directory
  const outputDir = path.join(__dirname, '..', 'generated-specs');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`Created output directory: ${outputDir}\n`);
  }

  const results = [];

  services.forEach(service => {
    console.log(`Generating spec for ${service.name}...`);
    
    const spec = generateServiceSpec(service);
    
    if (spec) {
      // Write JSON file
      const filename = `${service.name.toLowerCase()}-openapi.json`;
      const filepath = path.join(outputDir, filename);
      
      fs.writeFileSync(filepath, JSON.stringify(spec, null, 2));
      
      console.log(`Generated: ${filename}`);
      console.log(`   Location: ${filepath}`);
      console.log(`   Endpoints: ${Object.keys(spec.paths || {}).length}`);
      console.log(`   Schemas: ${Object.keys(spec.components?.schemas || {}).length}`);
      
      results.push({
        service: service.name,
        file: filename,
        path: filepath,
        endpoints: Object.keys(spec.paths || {}).length,
        schemas: Object.keys(spec.components?.schemas || {}).length
      });
    } else {
      console.log(`Failed to generate spec for ${service.name}`);
    }

    console.log('');
  });

  // Generate summary
  console.log('Generation Summary:');
  console.log('='.repeat(50));

  results.forEach(result => {
    console.log(`${result.service.padEnd(10)} | ${result.endpoints.toString().padEnd(3)} endpoints | ${result.schemas.toString().padEnd(3)} schemas | ${result.file}`);
  });

  console.log('='.repeat(50));
  console.log(`\nGenerated ${results.length} OpenAPI specifications!`);
  console.log(`All files saved in: ${outputDir}`);
  
  // Generate index file
  generateIndexFile(outputDir, results);
  
  console.log('\nHow to view:');
  console.log('1. Open any JSON file in VS Code with OpenAPI extension');
  console.log('2. Use online viewers like https://editor.swagger.io/');
  console.log('3. Use Postman to import the JSON files');
  console.log('4. Use Insomnia to import the JSON files');
}

/**
 * Generate an index HTML file to view all specs
 */
function generateIndexFile(outputDir, results) {
  const indexHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSP Backend - OpenAPI Specifications</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        .service { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
        .service h3 { margin-top: 0; color: #34495e; }
        .stats { display: flex; gap: 20px; margin: 10px 0; }
        .stat { background: #3498db; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; }
        .links { margin-top: 15px; }
        .links a { display: inline-block; margin-right: 10px; padding: 8px 15px; background: #2ecc71; color: white; text-decoration: none; border-radius: 3px; font-size: 14px; }
        .links a:hover { background: #27ae60; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #7f8c8d; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PSP Backend - OpenAPI 3.1 Specifications</h1>
        <p>Generated on: <strong>${new Date().toISOString()}</strong></p>
        
        ${results.map(result => `
        <div class="service">
            <h3>📋 ${result.service} Service</h3>
            <div class="stats">
                <span class="stat">${result.endpoints} Endpoints</span>
                <span class="stat">${result.schemas} Schemas</span>
                <span class="stat">OpenAPI 3.1</span>
            </div>
            <div class="links">
                <a href="${result.file}" target="_blank">📄 View JSON</a>
                <a href="https://editor.swagger.io/" target="_blank">🌐 Open in Swagger Editor</a>
            </div>
        </div>
        `).join('')}
        
        <div class="footer">
            <p>💡 <strong>How to use these files:</strong></p>
            <p>• Open JSON files in VS Code with OpenAPI extension</p>
            <p>• Import into Postman or Insomnia for API testing</p>
            <p>• Use online editors like Swagger Editor or Redoc</p>
            <p>• Generate client SDKs using OpenAPI Generator</p>
        </div>
    </div>
</body>
</html>`;

  const indexPath = path.join(outputDir, 'index.html');
  fs.writeFileSync(indexPath, indexHtml);
  console.log(`Generated index file: ${indexPath}`);
}

// Run the script
if (require.main === module) {
  generateAllSpecs();
}

module.exports = {
  generateAllSpecs,
  generateServiceSpec,
  services
};
