/**
 * @fileoverview OpenAPI schemas specific to BUSINESS service
 */

module.exports = {
  Business: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      name: {
        type: 'string',
        example: 'Sample Business',
        description: 'Business name'
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['name']
  },

  CreateBusinessRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'New Business',
        description: 'Business name'
      },
      description: {
        type: 'string',
        example: 'Business description',
        description: 'Business description'
      }
    },
    required: ['name']
  },

  UpdateBusinessRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Updated Business',
        description: 'Business name'
      },
      description: {
        type: 'string',
        example: 'Updated description',
        description: 'Business description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  BusinessListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Business'
            }
          }
        }
      }
    ]
  }
};