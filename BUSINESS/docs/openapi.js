/**
 * @fileoverview OpenAPI configuration for BUSINESS service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const businessSchemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for BUSINESS service
 * @param {Object} app - Express app instance
 */
function setupBusinessDocs(app) {
  const config = {
    serviceName: 'BUSINESS',
    serviceDescription: `
      Business logic and operations service

      Key features:
      - Entity management
      - Authentication and authorization
      - Data validation and processing
    `,
    version: '1.0.0',
    port: 4003,
    basePath: '/business',
    additionalSchemas: businessSchemas,
    docsPath: '/business/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  setupBusinessDocs
};