const { mongoose } = require('mongoose');
const {
  BUSINESS,
  STORE,
  TERMINAL,
  USER,
  USER_GROUP,
  TERMINAL_GROUP,
  MERCHANTDETAILS,
  CONFIGURATION,
  ISO_BUSINESS,
  RESTRICTED_PINS,
} = require('../models');

const {
  filterQuery,
  pagination,
  genrateRandomBusinessId,
  sendEmail,
  sendEmailLocally,
  generate6digitOtp,
  sendSocketNotification,
  randomPassword,
  updateMongoCollection,
  updateMongoCollectionBulk,
  createGiftCard,
  updateGiftCard,
} = require('../utils/helper');
const { default: axios } = require('axios');
const {
  base_url,
  merchant_tool_url,
  merchant_tool_api_key,
  gift_card_db,
  gift_card_url,
  gift_card_api_url,
} = require('../configs');
const { formatISO } = require('date-fns');
const creatingMerchant = require('../utils/templates/creating-merchant');

exports.createBusiness = async (req, res) => {
  const { title, owner, ...data } = req.body;
  const business = await BUSINESS.findOne({ title: title, is_deleted: false });
  // TODO: Need to remove index title_1 from business collection for the above to work properly
  const {
    is_amex,
    submitter_id,
    marketing_indicator,
    japan_credit_bureau_indicator,
    ownership_type_indicator,
    seller,
    significant_owners,
    authorized_signer,
    se_status_code_change_date,
    se_detail_status_code,
    ...rest
  } = data;
  let merchant_details_id = null;
  if (is_amex) {
    let {
      data: { _id },
    } = await axios.post(`${base_url}/amex/v1/merchant`, {
      is_amex,
      submitter_id,
      marketing_indicator,
      japan_credit_bureau_indicator,
      ownership_type_indicator,
      seller,
      significant_owners,
      authorized_signer,
      se_status_code_change_date,
      se_detail_status_code,
    });
    merchant_details_id = _id;
  }

  if (business) {
    throw new Error('Business Already Exists:409');
  }
  const ownerEmail = await BUSINESS.findOne({ 'owner.email': owner.email });

  if (ownerEmail) {
    throw new Error('Owner Email Already Taken:409');
  }

  const randomBid = await genrateRandomBusinessId(8);

  const response = await BUSINESS.create({
    title,
    owner,
    bid: randomBid,
    is_amex,
    merchant_details_id,
    ...rest,
  });

  if (merchant_tool_url) {
    const password = randomPassword();
    axios
      .post(
        `${merchant_tool_url}/api/add-admin`,
        {
          title,
          email: owner.email,
          business_id: response._id,
          mid: response.mid,
          password: password,
          should_reset_password: true,
          province: rest.province,
        },
        {
          headers: {
            Authorization: 'Bearer ' + merchant_tool_api_key,
          },
        },
      )
      .then(() => console.log('admin added'))
      .catch(e => console.log(e));
    const filename = 'Merchant Portal Guide.pdf';
    const path = require('path');
    const fs = require('fs');
    const filePath = fs.readFileSync(path.join(__dirname, `../utils/templates/${filename}`));
    const base64Pdf = Buffer.from(filePath).toString('base64');
    sendEmail({
      template: creatingMerchant(owner.first_name, owner.email, password, merchant_tool_url),
      toList: [owner.email],
      attachment: [
        {
          content: base64Pdf,
          filename: 'Merchant Portal Guide.pdf',
          type: 'application/pdf',
          disposition: 'attachment',
        },
      ],
      subject: 'Your Merchant Portal Access Is Ready',
    });

    response.linked_to_merchant_tool = true;
  }

  await response.save();

  if (gift_card_api_url && response._id && response.bid) {
    await createGiftCard({
      _id: response._id,
      title: title,
      business_no: response.bid,
      business_id: response._id,
      status: 'Active',
    });
    // await updateMongoCollection(gift_card_url, gift_card_db, 'business', {
    //   _id: response._id,
    //   title: title,
    //   business_no: response.bid,
    //   business_id: response._id,
    //   status: 'Active',
    // });
  }

  // sendEmailLocally({
  //   template: `Your Business has been created successfully. You can login at  <a href="${'http://localhost:3001/'}">here</a> with your email <${
  //     owner.email
  //   }> and otp, and you are required to set a new password upon first login.`,
  //   toList: [owner.email],
  //   subject: 'Business Created Successfully',
  // });
  return res.status(200).send({
    code: 200,
    message: 'Business Created Successfully!',
    success: true,
    business: response,
  });
};

exports.getAllBusiness = async (req, res) => {
  const { page, itemsPerPage, searchText, status, startDate, endDate, getDeleted } = filterQuery(req);

  const query = {
    $and: [],
    $or: [],
  };

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [
      { title: regExp },
      { address: regExp },
      { 'owner.first_name': regExp },
      { 'owner.last_name': regExp },
      { mid: regExp },
    ];
  }

  if (status && status !== '') {
    query.$and.push({
      status: { $eq: status },
    });
  }

  if (startDate && endDate) {
    let start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    let end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    query.$and.push({ created_at: { $gte: start, $lt: end } });
  }

  if (getDeleted && getDeleted !== '') {
    query.$and.push({ is_deleted: JSON.parse(getDeleted) });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }

  if (!query.$or.length > 0) {
    delete query.$or;
  }

  const totalItems = await BUSINESS.countDocuments(query);

  let businesses = await BUSINESS.find(query)
    .sort([['title', 1]])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .lean();

  const records = pagination(businesses, page, totalItems, itemsPerPage);

  return res.status(200).send({
    ...records,
    code: 200,
    success: true,
    message: 'All Businesses Fetched Successfully',
  });
};

exports.editBusiness = async (req, res) => {
  const { id } = req.params;
  let { owner, ...data } = req.body;
  const {
    is_amex,
    submitter_id,
    marketing_indicator,
    japan_credit_bureau_indicator,
    ownership_type_indicator,
    seller,
    significant_owners,
    authorized_signer,
    se_status_code_change_date,
    se_detail_status_code,
    record_number,
    ...payload
  } = data;
  // let merchant_details_id = '';
  let merchant_details_id = null;
  if (is_amex) {
    let {
      data: { _id },
    } = await axios.post(`${base_url}/amex/v1/merchant`, {
      record_number,
      is_amex,
      submitter_id,
      marketing_indicator,
      japan_credit_bureau_indicator,
      ownership_type_indicator,
      seller,
      significant_owners,
      authorized_signer,
      se_status_code_change_date,
      se_detail_status_code,
    });
    merchant_details_id = _id;
  }
  const counter = await BUSINESS.findById(id).lean();

  if (!counter) {
    throw new Error('Business Does Not Exists:404');
  }

  let business = {
    is_amex,
    merchant_details_id,
  };

  Object.keys(payload).forEach(element => {
    if (element === 'merchant_details_id') {
      business[element] = merchant_details_id;
    } else {
      business[element] = payload[element];
    }
  });

  const businessExists = await BUSINESS.findOne({ title: business.title, _id: { $ne: id } });

  if (businessExists) {
    throw new Error('Business Already Exists:409');
  }

  const updatedDetails = { ...counter, ...business, owner: { ...owner } };
  await BUSINESS.findByIdAndUpdate(id, { $set: { ...updatedDetails } });

  if (gift_card_api_url) {
    await updateGiftCard({
      _id: id,
      title: updatedDetails.title,
      status: updatedDetails.status,
    });

    // await updateMongoCollection(gift_card_url, gift_card_db, 'business', {
    //   _id: id,
    //   title: updatedDetails.title,
    //   status: updatedDetails.status,
    // });
  }

  let stores = await STORE.find({ business_id: id, is_deleted: { $ne: true } }).select('_id');
  stores = stores.forEach(store => {
    sendSocketNotification({
      store_id: store._id,
      event: 'gift_card_updated',
      data: {
        store_id: store._id,
        is_giftcard: updatedDetails.is_giftcard,
        is_report: updatedDetails.is_report,
      },
    });
  });

  if (merchant_tool_url && updatedDetails.linked_to_merchant_tool) {
    axios
      .post(
        `${merchant_tool_url}/api/update-admin-tms`,
        {
          mid: updatedDetails.mid,
          business_id: id,
          title: updatedDetails.title,
          province: updatedDetails.province,
        },
        {
          headers: {
            Authorization: 'Bearer ' + merchant_tool_api_key,
          },
        },
      )
      .then(() => console.log('admin added'))
      .catch(e => console.log(e));
  }
  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Business Updated Successfully',
  });
};

exports.deleteBusiness = async (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;

  const business = await BUSINESS.findById(id);

  if (!business) {
    throw new Error('Business Does Not Exists Or Already Deleted:404');
  }

  await BUSINESS.findByIdAndUpdate(id, {
    is_deleted: true,
    status: 'Deactivated',
    deactivation_reason: reason,
  });

  if (gift_card_api_url) {
    await updateGiftCard({
      _id: id,
      status: 'Deactivated',
    });
    // await updateMongoCollection(gift_card_url, gift_card_db, 'business', {
    //   _id: id,
    //   status: 'Deactivated',
    // });
  }

  await STORE.updateMany(
    { business_id: id },
    {
      $set: {
        is_deleted: true,
        status: 'Deactivated',
        deactivation_reason: reason,
      },
    },
  );
  await USER.updateMany({ business_id: id }, { $set: { is_deleted: true, status: 'Deactivated' }, user_id: [] });
  await TERMINAL.updateMany(
    { business_id: id },
    {
      $set: { is_deleted: true, status: 'Deactivated', api_key: '' },
      $unset: { group_id: 1 },
    },
  );
  await USER_GROUP.deleteMany({ business_id: id });
  await TERMINAL_GROUP.deleteMany({ business_id: id });

  await ISO_BUSINESS.updateMany(
    { businesses: id },
    {
      $pull: {
        businesses: id,
      },
    },
  );

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Business Deactivated Successfully!',
  });
};

exports.getBusinessDetails = async (req, res) => {
  const { id } = req.params;

  const business = await BUSINESS.aggregate([
    { $match: { _id: mongoose.Types.ObjectId(id) } },
    {
      $lookup: {
        from: 'stores',
        localField: '_id',
        foreignField: 'business_id',
        as: 'stores',
      },
    },
  ]);

  if (!business) {
    throw new Error('Business Does Not Exists:404');
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Business Details Fetched Successfully',
    business: business[0],
  });
};

exports.changeBusinessStatus = async (req, res) => {
  const { id } = req.params;

  const { status, stores, terminals, users } = req.body;

  const business = await BUSINESS.findById(id);

  if (!business) {
    throw new Error('Business Does Not Exists:404');
  }

  await BUSINESS.findByIdAndUpdate(id, { status });

  if (status !== 'Active') {
    const query = { business_id: id, is_deleted: false };
    await STORE.updateMany(query, { status });
    await TERMINAL.updateMany(query, { status });
    await USER.updateMany(query, { status });
  }

  if (status === 'Active' && stores?.length > 0) {
    if (stores?.includes('all')) {
      await STORE.updateMany({ business_id: id, is_deleted: false }, { status });
    } else {
      await STORE.updateMany({ _id: { $in: stores }, business_id: id }, { status });
    }
  }

  if (status === 'Active' && terminals?.length > 0) {
    if (terminals?.includes('all')) {
      const activeTerminalsQuery = { business_id: id, is_deleted: false, api_key: { $exists: true, $ne: '' } };
      const unactiveTerminalsQuery = { business_id: id, is_deleted: false, api_key: { $exists: false } };
      if (!stores?.includes('all')) {
        activeTerminalsQuery.store_id = { $in: stores };
        unactiveTerminalsQuery.store_id = { $in: stores };
      }
      await TERMINAL.updateMany(activeTerminalsQuery, { status });
      await TERMINAL.updateMany(unactiveTerminalsQuery, { status: 'PendingActivation' });
    } else {
      await TERMINAL.updateMany(
        {
          _id: { $in: terminals },
          business_id: id,
          api_key: { $exists: true, $ne: '' },
        },
        { status },
      );
      await TERMINAL.updateMany(
        {
          _id: { $in: terminals },
          business_id: id,
          api_key: { $exists: false },
        },
        { status: 'PendingActivation' },
      );
    }
  }

  if (status === 'Active' && users?.length > 0) {
    if (users?.includes('all')) {
      const storesQuery = { business_id: id, is_deleted: false };
      if (!stores?.includes('all')) {
        storesQuery.store_id = { $in: stores };
      }
      await USER.updateMany(storesQuery, { status });
    } else {
      await USER.updateMany({ _id: { $in: users }, business_id: id }, { status });
    }
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Business Status Changed Successfully',
  });
};

exports.getBusinessForQr = async (req, res) => {
  const status = 'Active';
  const { page, itemsPerPage, searchText, businessId } = filterQuery(req);

  const query = {
    $and: [{ is_deleted: false }],
    $or: [],
  };

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [{ title: regExp }, { 'owner.first_name': regExp }, { 'owner.last_name': regExp }];
  }

  if (status && status !== '') {
    query.$and.push({
      status: { $eq: status },
    });
  }
  if (businessId && businessId !== '') {
    query.$and.push({
      bid: { $eq: businessId },
    });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }

  if (!query.$or.length > 0) {
    delete query.$or;
  }

  const totalItems = await BUSINESS.countDocuments(query);

  const businesses = await BUSINESS.find(query)
    .sort([['created_at', -1]])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .select(['title', 'bid'])
    .lean();

  const records = pagination(businesses, page, totalItems, itemsPerPage);

  return res.status(200).send({
    ...records,
    code: 200,
    success: true,
    message: 'Business with there id and names',
  });
};

exports.copyBusinessToMerchantTool = async (req, res) => {
  const business = await BUSINESS.find({
    $or: [{ linked_to_merchant_tool: false }, { linked_to_merchant_tool: { $exists: false } }],
  });
  for (const element of business) {
    try {
      await axios.post(
        `${merchant_tool_url}/api/add-admin`,
        {
          title: element.title,
          email: element.owner.email,
          password: '<EMAIL>',
          business_id: element._id,
          mid: element.mid,
        },
        {
          headers: {
            Authorization: 'Bearer ' + merchant_tool_api_key,
          },
        },
      );
      element.linked_to_merchant_tool = true;
      await element.save();
    } catch (error) {
      console.error(`Failed to copy business with ID ${element._id}:`, error.message);
    }
  }

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Business Copied to Merchant Tool Successfully!',
  });
};

exports.sendMailMerchantTool = async (req, res) => {
  try {
    const { businessId } = req.body;
    const business = await BUSINESS.findById(businessId);
    if (!business) {
      throw new Error('Business Does Not Exists:404');
    }
    if (merchant_tool_url) {
      const password = randomPassword();
      axios
        .post(
          `${merchant_tool_url}/api/add-admin`,
          {
            title: business.title,
            email: business.owner.email,
            business_id: business._id,
            mid: business.mid,
            password: password,
            should_reset_password: true,
          },
          {
            headers: {
              Authorization: 'Bearer ' + merchant_tool_api_key,
            },
          },
        )
        .then(() => console.log('admin added'))
        .catch(e => console.log(e));

      sendEmail({
        template: creatingMerchant(business.owner.first_name, business.owner.email, password, merchant_tool_url),
        toList: [business.owner.email],
        subject: 'Your Merchant Portal Access Is Ready',
      });

      business.linked_to_merchant_tool = true;
    }
    await business.save();
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'Email Sent Successfully!',
    });
  } catch (err) {
    return res.status(500).send({
      code: 500,
      success: false,
      message: err.message,
    });
  }
};

exports.getBusinessTerminals = async (req, res) => {
  try {
    const { id } = req.params;
    let { page = 1, itemsPerPage = 10, searchText = '', status = '', getAll = 'false' } = filterQuery(req);
    page = parseInt(page);
    itemsPerPage = parseInt(itemsPerPage);
    const query = {
      $and: [{ business_id: id, is_deleted: false }],
      $or: [],
    };
    if (status && status !== 'undefined') {
      query.$and.push({ status });
    }

    if (searchText && searchText !== '') {
      const regExp = new RegExp(searchText, 'i');

      query.$or = [{ serial_number: regExp }, { tid: regExp }, { title: regExp }];
    }

    if (!query.$and.length > 0) {
      delete query.$and;
    }

    if (!query.$or.length > 0) {
      delete query.$or;
    }

    const totalItems = await TERMINAL.countDocuments(query);
    if (getAll === 'true') {
      itemsPerPage = totalItems;
    }
    const terminals = await TERMINAL.find(query)
      .sort([['created_at', -1]])
      .skip((page - 1) * itemsPerPage)
      .limit(itemsPerPage)
      .lean();

    const records = pagination(terminals, page, totalItems, itemsPerPage);

    return res.status(200).send({
      ...records,
      code: 200,
      success: true,
      message: 'Terminals',
    });
  } catch (ex) {
    return res.status(500).send({
      code: 500,
      success: false,
      message: ex.message,
    });
  }
};

exports.generateTerminalOtp = async (req, res) => {
  const { id } = req.params;

  const terminal = await TERMINAL.findById(id)
    .populate({
      model: STORE,
      path: 'store_id',
      select: { status: 1 },
    })
    .populate({ path: 'business_id', model: BUSINESS, select: 'mid' });

  if (!terminal) {
    return res.status(400).send({
      code: 400,
      success: false,
      message: 'Not found!',
    });
  }

  if (terminal?.store_id?.status !== 'Active') {
    throw new Error("You can't do that because store is not Active:400");
  }

  let otp = generate6digitOtp();

  const created_time = formatISO(new Date());

  await TERMINAL.findByIdAndUpdate(terminal._id, {
    status: 'PendingActivation',
    api_key: '',
    activation_code: {
      otp,
      created_time,
    },
  });
  sendSocketNotification({
    serial_number: terminal?.serial_number,
    data: {
      store_id: terminal?.store_id?._id,
      serial_number: terminal?.serial_number,
      status: 'PendingActivation',
      mid: terminal?.business_id?.mid,
    },
    event: 'device_deleted',
  });
  sendSocketNotification({
    serial_number: `status_${terminal?.serial_number}`,
    event: `status_${terminal?.serial_number}`,
    data: {
      store_id: terminal?.store_id?._id,
      serial_number: terminal?.serial_number,
      status: 'PendingActivation',
      mid: terminal?.business_id?.mid,
      online: false,
      shouldRefetchTerminals: true,
      timestamp: new Date().toISOString(),
    },
  });
  return res.status(200).send({
    code: 200,
    success: true,
    message: `API key generated`,
    data: { otp },
  });
};

exports.getTerminalConfigurtion = async (req, res) => {
  const { id } = req.params;

  const terminlConfiguration = await CONFIGURATION.findById(id);

  return res.status(200).send({
    success: true,
    message: 'Terminal Configuration',
    data: terminlConfiguration,
  });
};

exports.updateMongoCollectionBulk = async (req, res) => {
  try {
    const businesses = await BUSINESS.find({}).select(['_id', 'title', 'bid', 'status']);

    let data = businesses.map(business => ({
      updateOne: {
        filter: { _id: business._id },
        update: {
          $set: {
            // _id: business._id,
            title: business.title,
            business_no: business.bid,
            status: business.status,
          },
        },
        upsert: true,
      },
    }));

    await updateMongoCollectionBulk(gift_card_url, gift_card_db, 'business', data);
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'Businesses Updated in Mongo Collection Successfully!',
    });
  } catch (err) {
    return res.status(500).send({
      code: 500,
      success: false,
      message: err.message,
    });
  }
};

exports.getQrDetails = async (req, res) => {
  const { serial_number } = req.query;
  const terminal = await TERMINAL.findOne(
    { serial_number, status: 'Active' },
    {
      tid: 1,
      business_id: 1,
      title: 1,
      device_id: 1,
    },
  )
    .populate({
      path: 'business_id',
      model: BUSINESS,
    })
    .populate({
      path: 'store_id',
      model: STORE,
    })
    .lean();
  if (!terminal) {
    return res.status(404).send({
      code: 404,
      success: false,
      message: 'Terminal not found!',
    });
  }
  return res.status(200).send({
    tid: terminal.tid,
    title: terminal.title,
    device_id: terminal.device_id,
    mid: terminal.business_id.mid,
    extra: terminal,
  });
};
exports.uploadMerchants = async (req, res) => {
  try {
    const businesses = req.body;
    let response = [];
    //if any business already exists then throw error before adding any business from csv

    for (let i = 0; i < businesses.length; i++) {
      const business = businesses[i];
      const businessExists = await BUSINESS.findOne({ title: business.title });
      if (businessExists) {
        response.push({
          title: business.title,
          message: 'Business Already Exists',
          success: false,
        });
      }
      const Owner = await BUSINESS.findOne({ 'owner.email': business?.owner?.email });
      if (Owner) {
        response.push({
          title: business.title,
          message: 'Owner Email Already Taken',
          success: false,
        });
      }
      const Mid = await BUSINESS.findOne({ mid: business.mid });
      if (Mid) {
        response.push({
          title: business.title,
          message: 'MID Already Taken',
          success: false,
        });
      }
    }
    if (response.length > 0) {
      return res.status(400).send({
        code: 400,
        success: true,
        message: 'Some Of Business Already Exists, Please sure the MID and business title , Owner Email are unique',
        data: response,
      });
    }

    for (let i = 0; i < businesses.length; i++) {
      const business = businesses[i];
      const randomBid = await genrateRandomBusinessId(8);
      const response = await BUSINESS.create({
        title: business.title,
        owner: business.owner,
        bid: randomBid,
        mid: business.mid,
        status: 'Active',
        merchant_details_id: null,
        is_amex: false,
      });
      await response.save();
    }
    return res.status(200).send({
      code: 200,
      success: true,
      message: 'Businesses Added Successfully',
    });
  } catch (ex) {
    return res.status(500).send({
      code: 500,
      success: false,
      message: ex.message,
    });
  }
};

exports.getAllBusinesses = async (req, res) => {
  try {
    const businesses = await BUSINESS.find({}).lean();
    return res.status(200).send(businesses);
  } catch (ex) {
    return res.status(500).send({
      code: 500,
      success: false,
      message: ex.message,
    });
  }
};

exports.getIso = async (req, res) => {
  try {
    let { searchText = '', page = 1, pageSize = 10 } = req.query;
    const query = {};
    page = parseInt(page);
    pageSize = parseInt(pageSize);
    if (searchText) {
      query.title = { $regex: searchText, $options: 'i' };
    }
    const totalItems = await ISO_BUSINESS.countDocuments(query);
    const iso = await ISO_BUSINESS.find(query)
      .populate({
        path: 'businesses',
        model: BUSINESS,
        select: ['title', '_id', 'mid', 'status'],
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean();
    return res.status(200).json({ ...pagination(iso, page, totalItems, pageSize) });
  } catch (ex) {
    return res.status(500).json({
      message: ex.message,
    });
  }
};
exports.addBusinessIso = async (req, res) => {
  try {
    const { title, businesses = [] } = req.body;
    const found = await ISO_BUSINESS.findOne({ title });
    if (found) {
      return res.status(400).json({
        message: 'ISO Already Exists',
      });
    }
    const iso = await ISO_BUSINESS.create({
      title,
      businesses,
    });

    await ISO_BUSINESS.updateMany(
      {
        businesses: {
          $in: businesses,
        },
      },
      {
        $pull: {
          businesses: {
            $in: businesses,
          },
        },
      },
    );

    return res.status(200).json({
      message: 'ISO Added Successfully',
      data: iso,
    });
  } catch (ex) {
    return res.status(500).json({
      message: ex.message,
    });
  }
};
exports.deleteBusinessIso = async (req, res) => {
  try {
    const { id } = req.params;
    await ISO_BUSINESS.findByIdAndDelete(id);

    return res.status(200).json({
      message: 'ISO Deleted Successfully',
    });
  } catch (ex) {
    return res.status(500).json({
      message: ex.message,
    });
  }
};
exports.updateBusinessIso = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, businesses = [] } = req.body;
    const found = await ISO_BUSINESS.findOne({ title });
    if (found && found._id.toString() != id) {
      return res.status(400).json({
        message: 'ISO Already Exists',
      });
    }
    await ISO_BUSINESS.findByIdAndUpdate(id, {
      title,
      businesses,
    });
    await ISO_BUSINESS.updateMany(
      {
        businesses: {
          $in: businesses,
        },
        _id: {
          $ne: id,
        },
      },
      {
        $pull: {
          businesses: {
            $in: businesses,
          },
        },
      },
    );

    return res.status(200).json({
      message: 'ISO Updated Successfully',
    });
  } catch (ex) {
    return res.status(500).json({
      message: ex.message,
    });
  }
};

exports.getIsoOptions = async (req, res) => {
  try {
    const iso = await ISO_BUSINESS.find({});
    return res.status(200).json(iso);
  } catch (ex) {
    return res.status(500).json({
      message: ex.message,
    });
  }
};
