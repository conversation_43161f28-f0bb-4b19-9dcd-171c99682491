const nodemailer = require('nodemailer');

const {
  sendgrid_api_key,
  sendgrid_user_name,
  sendgrid_port,
  sendgrid_host,
  sendgrid_email_from,
  mail_host,
  mail_port,
  mail_username,
  mail_password,
  mail_from_address,
  mail_from_name,
  base_url,
  access_key,
  gift_card_api_url,
  gift_card_api_key,
} = require('../configs');
const axios = require('axios');
const { BUSINESS } = require('../models');
const sgMail = require('@sendgrid/mail');
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

module.exports = {
  pagination: (items = [], page = 1, totalItems = 0, itemsPerPage = 5) => {
    return {
      currentPage: page,
      hasNextPage: itemsPerPage * page < totalItems,
      hasPreviousPage: page > 1,
      nextPage: page + 1,
      previousPage: page - 1,
      lastPage: Math.ceil(totalItems / itemsPerPage),
      totalItems: totalItems,
      items: items,
    };
  },
  filterQuery: req => ({
    ...req.query,
    page: req.query.page ? Number(req.query.page) : 1,
    itemsPerPage: req.query.itemsPerPage
      ? Number(req.query.itemsPerPage)
      : req.query.perPage
        ? Number(req.query.perPage)
        : 10,
    searchText:
      req.query.searchText !== 'null' && req.query.searchText !== 'undefined' && req.query.searchText
        ? req.query.searchText
        : '',
    startDate:
      req.query.startDate !== 'null' && req.query.startDate !== 'undefined' && req.query.startDate
        ? req.query.startDate
        : '',
    endDate:
      req.query.endDate !== 'null' && req.query.endDate !== 'undefined' && req.query.endDate ? req.query.endDate : '',
    storeId:
      req.query.storeId !== 'null' && req.query.storeId !== 'undefined' && req.query.storeId ? req.query.storeId : '',
  }),
  readAllSchemaFiles: async () => {
    const fs = require('fs');
    const path = require('path');
    const createCsvWriter = require('csv-writer').createObjectCsvWriter;

    const directoryPath = path.join(__dirname, '../confirmed_models');
    const csvPath = path.join(__dirname, '../utils/csv');

    const files = await new Promise((resolve, reject) => {
      fs.readdir(directoryPath, (err, files) => {
        if (err) {
          reject(err);
        } else {
          resolve(files);
        }
      });
    });

    files.forEach(async file => {
      let csvFileName = file.replace('.js', '.csv');
      const filePath = path.join(directoryPath, file);

      // Require each file
      let model = require(filePath);
      if (typeof model === 'function') {
        const newObject = Object.entries(model.schema.paths).reduce((acc, [key, payload]) => {
          acc.push({
            params: key,
            requred: payload?.isRequired ? 'Y' : 'O',
            unique: payload?.options?.unique ? 'Y' : 'N',
            data_type: payload?.instance,
            enum_values: payload?.enumValues?.length ? payload?.enumValues : '',
            default_values: payload?.options?.default ? payload?.options?.default : '',
          });
          return acc;
        }, []);

        let csvWritePath = path.join(csvPath, csvFileName);

        const csvWriter = createCsvWriter({
          path: csvWritePath,
          header: Object.keys(newObject[0]).map(key => ({
            id: key,
            title: key.toUpperCase(),
          })),
        });
        await csvWriter.writeRecords(newObject);
      }
    });
  },
  genRandom: length => {
    let zero = '';
    for (let index = 1; index < length; index++) {
      zero += '0';
    }

    let firstVal = 1 + zero;
    let secondVal = 9 + zero;

    return Math.floor(Number(firstVal) + Math.random() * Number(secondVal));
  },

  genrateRandomBusinessId: async length => {
    const number = module.exports.genRandom(length);
    const user = await BUSINESS.findOne({ bid: number });

    if (user) {
      return module.exports.genrateRandomBusinessId(length);
    }
    return number;
  },
  sendEmail: async ({ template, toList, subject, attachment = [] }) => {
    try {
      const msg = {
        to: toList,
        from: sendgrid_email_from,
        subject: subject,
        html: template,
        attachments: attachment,
        tls: {
          ciphers: 'SSLv3',
          rejectUnauthorized: false,
        },
        host: sendgrid_host,
        port: sendgrid_port,
        auth: {
          user: sendgrid_user_name,
          pass: sendgrid_api_key,
        },
      };

      await sgMail.send(msg);
      return { code: 200, message: 'email sent', success: true };
    } catch (err) {
      return { code: err.code, message: err.message, success: false };
    }
  },
  sendEmailLocally: ({ template, toList, subject, attachment = null }) => {
    try {
      let transporter = nodemailer.createTransport({
        host: mail_host,
        port: mail_port,
        secure: false,
        auth: {
          user: mail_username,
          pass: mail_password,
        },
        tls: {
          rejectUnauthorized: false,
        },
      });
      transporter.sendMail({
        from: `${mail_from_name} <${mail_from_address}>`,
        to: toList,
        replyTo: null,
        subject,
        html: template,
        attachments: attachment,
      });
    } catch (error) {
      throw new Error(`${error.message}:code`);
    }
  },
  generate6digitOtp: () => {
    let otp = Math.floor(Math.random() * 1000000);
    let zeroCount = otp.toString().split('0').length - 1;
    if (otp < 100000 || zeroCount > 1) {
      return module.exports.generate6digitOtp();
    }
    return otp;
  },
  sendSocketNotification: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };
      await axios.post(`${base_url}/common/send-socket-notification`, payload, {
        headers,
      });
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },
  randomPassword: (length = 8) => {
    const types = [
      'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      'abcdefghijklmnopqrstuvwxyz',
      '0123456789',
      '!@#$%^&*()_+~`|}{[]:;?><,./-=',
    ];
    let password = types.map(type => type[Math.floor(Math.random() * type.length)]).join('');
    const allChars = types.join('');
    return (
      password +
      Array.from({ length: length - 4 }, () => allChars[Math.floor(Math.random() * allChars.length)]).join('')
    );
  },
  updateMongoCollection: async (mongo_url, db_name, collection_name, data) => {
    const { MongoClient } = require('mongodb');
    const client = new MongoClient(mongo_url, { useUnifiedTopology: true });
    try {
      await client.connect();
      const db = client.db(db_name);
      const collection = db.collection(collection_name);
      await collection.updateOne({ _id: data._id }, { $set: data }, { upsert: true });
    } catch (error) {
      console.error('Error updating mongo collection:', error);
    } finally {
      await client.close();
    }
  },
  updateMongoCollectionBulk: async (mongo_url, db_name, collection_name, data) => {
    const { MongoClient } = require('mongodb');
    const client = new MongoClient(mongo_url, { useUnifiedTopology: true });
    try {
      await client.connect();
      const db = client.db(db_name);
      const collection = db.collection(collection_name);
      await collection.bulkWrite(data);
    } catch (error) {
      console.error('Error updating mongo collection:', error);
    } finally {
      await client.close();
    }
  },
  createGiftCard: async ({ _id, title, business_no, business_id, status }) => {
    try {
      const headers = {
        Authorization: `Bearer ${gift_card_api_key}`,
      };

      const payload = {
        title,
        business_no,
        business_id,
        status,
        _id,
      };

      await axios.post(`${gift_card_api_url}/api/create-business`, payload, {
        headers,
      });
    } catch (error) {
      console.error('Error calling gift card service:', error);
    }
  },
  updateGiftCard: async ({ _id, title, business_no, business_id, status }) => {
    try {
      const headers = {
        Authorization: `Bearer ${gift_card_api_key}`,
      };

      const payload = {
        title,
        business_no,
        business_id,
        status,
        _id,
      };

      await axios.post(`${gift_card_api_url}/api/update-business`, payload, {
        headers,
      });

      return { code: 200, message: 'Gift card updated', success: true };
    } catch (error) {
      return { code: error.code, message: error.message, success: false };
    }
  },
  randomPassword: (length = 8) => {
    const types = [
      'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      'abcdefghijklmnopqrstuvwxyz',
      '0123456789',
      '!@#$%^&*()_+~|}{[]:;?><,./-=',
    ];
    let password = types.map(type => type[Math.floor(Math.random() * type.length)]).join('');
    const allChars = types.join('');
    password += Array.from({ length: length - 4 }, () => allChars[Math.floor(Math.random() * allChars.length)]).join(
      '',
    );
    return password.replace(/\s/g, '').slice(0, length);
  },
};
