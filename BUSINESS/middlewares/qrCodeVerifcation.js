const { reward_api_key } = require('../configs');

const API_KEY = reward_api_key;

module.exports = async (req, res, next) => {
  const authHeader = req.headers['x-api-key'];

  if (!authHeader) {
    return res.status(401).send({
      code: 401,
      success: false,
      message: 'Authetication required!',
    });
  }

  if (authHeader && authHeader === API_KEY) {
    next();
  } else {
    return res.status(401).json({
      code: 401,
      success: false,
      message: 'Authetication Failed!',
    });
  }
};
