const express = require('express');
require('express-group-routes');
const router = express.Router();

const { IS_ADMIN, QR_API_VERIFY, MERCHANT_TOOL_API_VERIFY } = require('./middlewares');
const tryCatch = require('./utils/tryCatch');
const { businessController } = require('./controllers');


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */

router.group('/v1', router => {

  /**
   * @swagger
   * /business/v1/create-business:
   *   post:
   *     tags: [Create Business Management]
   *     summary: Create create business
   *     description: Create create business in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/create-business:
   *   post:
   *     tags: [Create Business Management]
   *     summary: Create create business
   *     description: Create create business in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-business', IS_ADMIN, tryCatch(businessController.createBusiness));

  /**
   * @swagger
   * /business/v1/get-all-business:
   *   get:
   *     tags: [Get All Business Management]
   *     summary: Get get all business
   *     description: Retrieve get all business in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/get-all-business:
   *   get:
   *     tags: [Get All Business Management]
   *     summary: Get get all business
   *     description: Retrieve get all business in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-all-business', IS_ADMIN, tryCatch(businessController.getAllBusiness));

  /**
   * @swagger
   * /business/v1/update-business/:id:
   *   put:
   *     tags: [Update Business Management]
   *     summary: Update update business
   *     description: Update update business in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/update-business/:id:
   *   put:
   *     tags: [Update Business Management]
   *     summary: Update update business
   *     description: Update update business in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-business/:id', IS_ADMIN, tryCatch(businessController.editBusiness));

  /**
   * @swagger
   * /business/v1/delete-business/:id:
   *   post:
   *     tags: [Delete Business Management]
   *     summary: Create delete business
   *     description: Create delete business in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create delete business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/delete-business/:id:
   *   post:
   *     tags: [Delete Business Management]
   *     summary: Create delete business
   *     description: Create delete business in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create delete business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/delete-business/:id', IS_ADMIN, tryCatch(businessController.deleteBusiness));

  /**
   * @swagger
   * /business/v1/get-business-details/:id:
   *   get:
   *     tags: [Get Business Details Management]
   *     summary: Get get business details
   *     description: Retrieve get business details in the business service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get business details successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/get-business-details/:id:
   *   get:
   *     tags: [Get Business Details Management]
   *     summary: Get get business details
   *     description: Retrieve get business details in the business service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get business details successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-business-details/:id', IS_ADMIN, tryCatch(businessController.getBusinessDetails));

  /**
   * @swagger
   * /business/v1/change-business-status/:id:
   *   put:
   *     tags: [Change Business Status Management]
   *     summary: Update change business status
   *     description: Update change business status in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update change business status successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/change-business-status/:id:
   *   put:
   *     tags: [Change Business Status Management]
   *     summary: Update change business status
   *     description: Update change business status in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update change business status successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/change-business-status/:id', IS_ADMIN, tryCatch(businessController.changeBusinessStatus));


  /**
   * @swagger
   * /business/v1/qr-business:
   *   get:
   *     tags: [Qr Business Management]
   *     summary: Get qr business
   *     description: Retrieve qr business in the business service
   *     responses:
   *       200:
   *         description: Get qr business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /business/v1/qr-business:
   *   get:
   *     tags: [Qr Business Management]
   *     summary: Get qr business
   *     description: Retrieve qr business in the business service
   *     responses:
   *       200:
   *         description: Get qr business successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
  router.get('/qr-business', QR_API_VERIFY, tryCatch(businessController.getBusinessForQr));

  /**
   * @swagger
   * /business/v1/qr-details:
   *   get:
   *     tags: [Qr Details Management]
   *     summary: Get qr details
   *     description: Retrieve qr details in the business service
   *     responses:
   *       200:
   *         description: Get qr details successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /business/v1/qr-details:
   *   get:
   *     tags: [Qr Details Management]
   *     summary: Get qr details
   *     description: Retrieve qr details in the business service
   *     responses:
   *       200:
   *         description: Get qr details successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
  router.get('/qr-details', QR_API_VERIFY, tryCatch(businessController.getQrDetails));


  /**
   * @swagger
   * /business/v1/copy-business-to-merchant-tool:
   *   get:
   *     tags: [Copy Business To Merchant Tool Management]
   *     summary: Get copy business to merchant tool
   *     description: Retrieve copy business to merchant tool in the business service
   *     responses:
   *       200:
   *         description: Get copy business to merchant tool successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /business/v1/copy-business-to-merchant-tool:
   *   get:
   *     tags: [Copy Business To Merchant Tool Management]
   *     summary: Get copy business to merchant tool
   *     description: Retrieve copy business to merchant tool in the business service
   *     responses:
   *       200:
   *         description: Get copy business to merchant tool successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
  // router.get('/copy-business-to-merchant-tool', tryCatch(businessController.copyBusinessToMerchantTool));

  /**
   * @swagger
   * /business/v1/send-mail-to-merchant-tool:
   *   post:
   *     tags: [Send Mail To Merchant Tool Management]
   *     summary: Create send mail to merchant tool
   *     description: Create send mail to merchant tool in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send mail to merchant tool successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/send-mail-to-merchant-tool:
   *   post:
   *     tags: [Send Mail To Merchant Tool Management]
   *     summary: Create send mail to merchant tool
   *     description: Create send mail to merchant tool in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create send mail to merchant tool successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/send-mail-to-merchant-tool', IS_ADMIN, tryCatch(businessController.sendMailMerchantTool));

  /**
   * @swagger
   * /business/v1/upload-merchants:
   *   post:
   *     tags: [Upload Merchants Management]
   *     summary: Create upload merchants
   *     description: Create upload merchants in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create upload merchants successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/upload-merchants:
   *   post:
   *     tags: [Upload Merchants Management]
   *     summary: Create upload merchants
   *     description: Create upload merchants in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create upload merchants successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/upload-merchants', [IS_ADMIN], tryCatch(businessController.uploadMerchants));


  /**
   * @swagger
   * /business/v1/business-terminals/:id:
   *   post:
   *     tags: [Business Terminals Management]
   *     summary: Create business terminals
   *     description: Create business terminals in the business service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create business terminals successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /business/v1/business-terminals/:id:
   *   post:
   *     tags: [Business Terminals Management]
   *     summary: Create business terminals
   *     description: Create business terminals in the business service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create business terminals successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/business-terminals/:id', [MERCHANT_TOOL_API_VERIFY], tryCatch(businessController.getBusinessTerminals));

  /**
   * @swagger
   * /business/v1/generate-otp/:id:
   *   post:
   *     tags: [Generate Otp Management]
   *     summary: Create generate otp
   *     description: Create generate otp in the business service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create generate otp successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */

  /**
   * @swagger
   * /business/v1/generate-otp/:id:
   *   post:
   *     tags: [Generate Otp Management]
   *     summary: Create generate otp
   *     description: Create generate otp in the business service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create generate otp successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   */
  router.post('/generate-otp/:id', [MERCHANT_TOOL_API_VERIFY], tryCatch(businessController.generateTerminalOtp));

  /**
   * @swagger
   * /business/v1/get-all-businesses:
   *   get:
   *     tags: [Get All Businesses Management]
   *     summary: Get get all businesses
   *     description: Retrieve get all businesses in the business service
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all businesses successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /business/v1/get-all-businesses:
   *   get:
   *     tags: [Get All Businesses Management]
   *     summary: Get get all businesses
   *     description: Retrieve get all businesses in the business service
   *     parameters:
   *       - $ref: '#/components/parameters/PageQuery'
   *       - $ref: '#/components/parameters/ItemsPerPageQuery'
   *       - $ref: '#/components/parameters/SearchQuery'
   *     responses:
   *       200:
   *         description: Get get all businesses successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
  router.get('/get-all-businesses', [MERCHANT_TOOL_API_VERIFY], tryCatch(businessController.getAllBusinesses));
  router.get(
    '/terminal-configuration/:id',
    [MERCHANT_TOOL_API_VERIFY],
    tryCatch(businessController.getTerminalConfigurtion),
  );

  router.post(
    '/update-bulk-for-gift-cards',
    [MERCHANT_TOOL_API_VERIFY],
    tryCatch(businessController.updateMongoCollectionBulk),
  );


  /**
   * @swagger
   * /business/v1/get-iso:
   *   get:
   *     tags: [Get Iso Management]
   *     summary: Get get iso
   *     description: Retrieve get iso in the business service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get iso successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/get-iso:
   *   get:
   *     tags: [Get Iso Management]
   *     summary: Get get iso
   *     description: Retrieve get iso in the business service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get iso successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-iso', IS_ADMIN, tryCatch(businessController.getIso));

  /**
   * @swagger
   * /business/v1/add-business-iso:
   *   post:
   *     tags: [Add Business Iso Management]
   *     summary: Create add business iso
   *     description: Create add business iso in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create add business iso successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/add-business-iso:
   *   post:
   *     tags: [Add Business Iso Management]
   *     summary: Create add business iso
   *     description: Create add business iso in the business service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create add business iso successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/add-business-iso', IS_ADMIN, tryCatch(businessController.addBusinessIso));

  /**
   * @swagger
   * /business/v1/delete-business-iso/:id:
   *   delete:
   *     tags: [Delete Business Iso Management]
   *     summary: Delete delete business iso
   *     description: Delete delete business iso in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete business iso successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/delete-business-iso/:id:
   *   delete:
   *     tags: [Delete Business Iso Management]
   *     summary: Delete delete business iso
   *     description: Delete delete business iso in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete business iso successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-business-iso/:id', IS_ADMIN, tryCatch(businessController.deleteBusinessIso));

  /**
   * @swagger
   * /business/v1/update-business-iso/:id:
   *   put:
   *     tags: [Update Business Iso Management]
   *     summary: Update update business iso
   *     description: Update update business iso in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update business iso successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/update-business-iso/:id:
   *   put:
   *     tags: [Update Business Iso Management]
   *     summary: Update update business iso
   *     description: Update update business iso in the business service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update update business iso successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/update-business-iso/:id', IS_ADMIN, tryCatch(businessController.updateBusinessIso));


  /**
   * @swagger
   * /business/v1/iso-options:
   *   get:
   *     tags: [Iso Options Management]
   *     summary: Get iso options
   *     description: Retrieve iso options in the business service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get iso options successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /business/v1/iso-options:
   *   get:
   *     tags: [Iso Options Management]
   *     summary: Get iso options
   *     description: Retrieve iso options in the business service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get iso options successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/iso-options', IS_ADMIN, tryCatch(businessController.getIsoOptions));
});

module.exports = router;
