const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const deviceLogs = new Schema(
  {
    body: { type: Object },
    params: { type: Object },
    query: { type: Object },
    url: { type: String },
    method: { type: String },
    terminal: { type: String },
    store_id: { type: Schema.Types.ObjectId, ref: 'store', required: true },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

deviceLogs.plugin(uniqueValidator);

const DEVICE_LOGS = model('device_logs', deviceLogs);
module.exports = DEVICE_LOGS;
