const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const roleSchema = new Schema(
  {
    type: {
      type: String,
      default: 'admin',
      uppercase: true,
    },
    description: {
      type: String,
      default: 'Role for a super user',
    },
    permissions: { type: [Schema.Types.ObjectId], ref: 'permissions' },
  },

  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

roleSchema.plugin(uniqueValidator);

const ROLE = model('role', roleSchema);

module.exports = ROLE;
