const axios = require('axios');
const redis = require('redis');
const {
  access_key,
  redis_url,
  base_url,
  sendgrid_api_key,
  sendgrid_user_name,
  sendgrid_port,
  sendgrid_host,
  sendgrid_email_from,
  mail_host,
  mail_port,
  mail_username,
  mail_password,
  mail_from_address,
  mail_from_name,
} = require('../configs');
module.exports = {
  isEmpty: payload =>
    payload && Object.keys(payload).length === 0 && Object.getPrototypeOf(payload) === Object.prototype,
  pagination: (items = [], page = 1, totalItems = 0, itemsPerPage = 5) => {
    return {
      currentPage: page,
      hasNextPage: itemsPerPage * page < totalItems,
      hasPreviousPage: page > 1,
      nextPage: page + 1,
      previousPage: page - 1,
      lastPage: Math.ceil(totalItems / itemsPerPage),
      totalItems: totalItems,
      items: items,
    };
  },
  filterQuery: req => ({
    ...req.query,
    page: req.query.page ? Number(req.query.page) : 1,
    itemsPerPage: req.query.itemsPerPage
      ? Number(req.query.itemsPerPage)
      : req.query.perPage
        ? Number(req.query.perPage)
        : 10,
    searchText:
      req.query.searchText !== 'null' && req.query.searchText !== 'undefined' && req.query.searchText
        ? req.query.searchText
        : '',
    startDate:
      req.query.startDate !== 'null' && req.query.startDate !== 'undefined' && req.query.startDate
        ? req.query.startDate
        : '',
    endDate:
      req.query.endDate !== 'null' && req.query.endDate !== 'undefined' && req.query.endDate ? req.query.endDate : '',
    storeId:
      req.query.storeId !== 'null' && req.query.storeId !== 'undefined' && req.query.storeId ? req.query.storeId : '',
  }),
  readAllSchemaFiles: async () => {
    const fs = require('fs');
    const path = require('path');
    const createCsvWriter = require('csv-writer').createObjectCsvWriter;

    const directoryPath = path.join(__dirname, '../confirmed_models');
    const csvPath = path.join(__dirname, '../utils/csv');

    const files = await new Promise((resolve, reject) => {
      fs.readdir(directoryPath, (err, files) => {
        if (err) {
          reject(err);
        } else {
          resolve(files);
        }
      });
    });

    files.forEach(async file => {
      let csvFileName = file.replace('.js', '.csv');
      const filePath = path.join(directoryPath, file);

      // Require each file
      let model = require(filePath);
      if (typeof model === 'function') {
        const newObject = Object.entries(model.schema.paths).reduce((acc, [key, payload]) => {
          acc.push({
            params: key,
            requred: payload?.isRequired ? 'Y' : 'O',
            unique: payload?.options?.unique ? 'Y' : 'N',
            data_type: payload?.instance,
            enum_values: payload?.enumValues?.length ? payload?.enumValues : '',
            default_values: payload?.options?.default ? payload?.options?.default : '',
          });
          return acc;
        }, []);

        let csvWritePath = path.join(csvPath, csvFileName);

        const csvWriter = createCsvWriter({
          path: csvWritePath,
          header: Object.keys(newObject[0]).map(key => ({
            id: key,
            title: key.toUpperCase(),
          })),
        });
        await csvWriter.writeRecords(newObject);
      }
    });
  },
  sendSocketNotification: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };
      const response = await axios.post(`${base_url}/common/send-socket-notification`, payload, {
        headers,
      });
      console.log(response.code);
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },

  sendDeviceData: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };
      const response = await axios.post(`${base_url}/common/send-device-data`, payload, {
        headers,
      });
      console.log(response.code);
      return response;
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },

  pushDataToQueue: async payload => {
    try {
      const headers = {
        Authorization: `Bearer ${access_key}`,
      };

      const response = await axios.post(`${base_url}/common/push-data-queue`, payload, {
        headers,
      });

      return response;
    } catch (error) {
      console.error('Error calling common service:', error);
    }
  },
  mergeTwoObjects: (obj1, obj2) => {
    const merged = { ...obj1 };

    for (let key in obj2) {
      if (obj2.hasOwnProperty(key)) {
        if (merged.hasOwnProperty(key) && typeof merged[key] === 'object' && typeof obj2[key] === 'object') {
          merged[key] = module.exports.mergeTwoObjects(merged[key], obj2[key]);
        } else {
          merged[key] = obj2[key];
        }
      }
    }

    return merged;
  },
  createRedisClient: () => {
    const client = redis.createClient({ url: redis_url });
    client.connect();
    return client;
  },
  sendEmail: async ({ template, toList, subject, attachment = null }) => {
    try {
      const msg = {
        to: toList,
        from: sendgrid_email_from,
        subject: subject,
        html: template,
        tls: {
          ciphers: 'SSLv3',
          rejectUnauthorized: false,
        },
        host: sendgrid_host,
        port: sendgrid_port,
        auth: {
          user: sendgrid_user_name,
          pass: sendgrid_api_key,
        },
      };

      await sgMail.send(msg);
      return { code: 200, message: 'Email sent!', success: true };
    } catch (err) {
      return { code: err.code, message: err.message, success: false };
    }
  },
  sendEmailLocally: ({ template, toList, subject, attachment = null }) => {
    try {
      let transporter = nodemailer.createTransport({
        host: mail_host,
        port: mail_port,
        secure: false,
        auth: {
          user: mail_username,
          pass: mail_password,
        },
        tls: {
          rejectUnauthorized: false,
        },
      });
      transporter.sendMail({
        from: `${mail_from_name} <${mail_from_address}>`,
        to: toList,
        replyTo: null,
        subject,
        html: template,
        attachments: attachment,
      });
    } catch (error) {
      throw new Error('Somehing Went Wrong While Sending Email:400');
    }
  },
};
