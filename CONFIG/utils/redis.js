const CryptoJS = require('crypto-js');
const { createRedisClient } = require('./helper');

const client = createRedisClient();

client.on('error', err => console.log('Redis Client Error', err));

const redisHelper = {
  set: async (key, data, time = 5) => {
    try {
      let write = CryptoJS.SHA256(JSON.stringify(key)).toString();
      await client.setEx(write, time, JSON.stringify(data));
    } catch (err) {
      console.log('%credis.js line:24 "Error Setting Up"', 'color: #007acc;', err?.message);
      return null;
    }
  },
  get: async key => {
    try {
      let read = CryptoJS.SHA256(JSON.stringify(key)).toString();
      let result = await client.get(read, err => {
        if (err) {
          throw new Error(err);
        }
      });
      return result;
    } catch (err) {
      console.log('%credis.js line:39 "Error Getting Value"', 'color: #007acc;', err?.message);
      return null;
    }
  },
  remove: async key => {
    try {
      let read = CryptoJS.SHA256(JSON.stringify(key)).toString();
      await client.del(read);
    } catch (err) {
      console.log('%credis.js line:49 "Error Deleteing value"', 'color: #007acc;', err?.message);
    }
  },
  ping: async () => await client.ping(),
};

module.exports = redisHelper;
