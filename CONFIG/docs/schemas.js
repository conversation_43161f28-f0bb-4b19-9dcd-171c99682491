/**
 * @fileoverview OpenAPI schemas specific to CONFIG service
 */

module.exports = {
  Config: {
    type: 'object',
    properties: {
      _id: {
        $ref: '#/components/schemas/ObjectId'
      },
      name: {
        type: 'string',
        example: 'Sample Config',
        description: 'Config name'
      },
      status: {
        $ref: '#/components/schemas/Status'
      },
      created_at: {
        $ref: '#/components/schemas/Timestamp'
      },
      updated_at: {
        $ref: '#/components/schemas/Timestamp'
      }
    },
    required: ['name']
  },

  CreateConfigRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'New Config',
        description: 'Config name'
      },
      description: {
        type: 'string',
        example: 'Config description',
        description: 'Config description'
      }
    },
    required: ['name']
  },

  UpdateConfigRequest: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 2,
        example: 'Updated Config',
        description: 'Config name'
      },
      description: {
        type: 'string',
        example: 'Updated description',
        description: 'Config description'
      },
      status: {
        $ref: '#/components/schemas/Status'
      }
    }
  },

  ConfigListResponse: {
    allOf: [
      {
        $ref: '#/components/schemas/PaginatedResponse'
      },
      {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Config'
            }
          }
        }
      }
    ]
  }
};