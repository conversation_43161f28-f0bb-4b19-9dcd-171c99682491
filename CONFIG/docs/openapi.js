/**
 * @fileoverview OpenAPI configuration for CONFIG service
 */

const { setupSwagger } = require('../../docs/swagger-setup');
const configSchemas = require('./schemas');

/**
 * Sets up OpenAPI documentation for CONFIG service
 * @param {Object} app - Express app instance
 */
function setupConfigDocs(app) {
  const config = {
    serviceName: 'CONFIG',
    serviceDescription: `
      Configuration management service

      Key features:
      - Entity management
      - Authentication and authorization
      - Data validation and processing
    `,
    version: '1.0.0',
    port: 4006,
    basePath: '/config',
    additionalSchemas: configSchemas,
    docsPath: '/config/api-docs',
    routeFiles: [
      './routes.js',
      './controllers/*.js'
    ]
  };

  return setupSwagger(app, config);
}

module.exports = {
  setupConfigDocs
};