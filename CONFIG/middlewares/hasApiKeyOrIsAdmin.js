const jwt = require('jsonwebtoken');
const { ADMIN, ADMIN_JWT, TERMINAL, CONFIGURATION } = require('../models');
const { secret, access_key, base_url } = require('../configs');
const { default: axios } = require('axios');

module.exports = async (req, res, next) => {
  // Check for admin JWT
  const authHeader = req.headers.authorization;
  if (authHeader) {
    const token = authHeader.split(' ')[1];
    if (token) {
      try {
        const decodedToken = jwt.verify(token, secret);
        const admin = await ADMIN.findById(decodedToken.id);
        if (admin) {
          const isValid = await ADMIN_JWT.findOne({ token });
          if (isValid) {
            req.admin = admin;
            return next();
          }
        }
      } catch (err) {
        // Ignore and continue to API key check
      }
    }
  }

  // Check for API key
  const apiKeyHeader = req.headers['x-api-key'];
  if (!apiKeyHeader) {
    return res.status(401).send({
      code: 401,
      success: false,
      message: 'x-api-key Header Missing or invalid Admin token!',
    });
  }

  const terminal = await TERMINAL.findOne({ api_key: apiKeyHeader });
  if (!terminal) {
    return res.status(401).send({
      code: 401,
      success: false,
      message: 'Device not found',
    });
  }
  if (terminal.status !== 'Active') {
    return res.status(403).send({
      code: 403,
      success: false,
      message: 'Device is not active',
    });
  }

  if (apiKeyHeader && apiKeyHeader === terminal.api_key) {
    let old_config = null;
    if (req.url.includes('/update-device-configuration')) {
      old_config = await CONFIGURATION.findById(terminal.configuration);
    }
    if (old_config) {
      req.body.old_config = old_config.configuration;
    }

    axios
      .post(
        `${base_url}/common/device-log`,
        {
          body: req.body,
          params: req.params,
          query: req.query,
          url: req.url,
          method: req.method,
          terminal: terminal.serial_number,
          store_id: terminal.store_id,
        },
        {
          headers: {
            authorization: `Bearer ${access_key}`,
          },
        },
      )
      .then(() => {
        req.terminal = terminal;
        next();
      })
      .catch(ex => {
        console.log({ ERROR: ex.message });
        req.terminal = terminal;
        next();
      });
  } else {
    return res.status(401).json({
      code: 401,
      success: false,
      message: 'Please provide correct API key or valid admin token',
    });
  }
};
