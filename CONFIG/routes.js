const express = require('express');
require('express-group-routes');
const router = express.Router();

const { IS_ADMIN, HAS_API_KEY, HAS_API_KEY_OR_IS_ADMIN } = require('./middlewares');
const tryCatch = require('./utils/tryCatch');
const { configurationController } = require('./controllers');


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */


/**
 * @swagger
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: x-api-key
 *     AccessKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 */

router.group('/v1', router => {

  /**
   * @swagger
   * /config/v1/update-config:
   *   post:
   *     tags: [Update Config Management]
   *     summary: Create update config
   *     description: Create update config in the config service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create update config successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /config/v1/update-config:
   *   post:
   *     tags: [Update Config Management]
   *     summary: Create update config
   *     description: Create update config in the config service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create update config successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/update-config', IS_ADMIN, tryCatch(configurationController.updateConfig));

  /**
   * @swagger
   * /config/v1/get-config/:id:
   *   get:
   *     tags: [Get Config Management]
   *     summary: Get get config
   *     description: Retrieve get config in the config service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get config successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /config/v1/get-config/:id:
   *   get:
   *     tags: [Get Config Management]
   *     summary: Get get config
   *     description: Retrieve get config in the config service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get config successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-config/:id', IS_ADMIN, tryCatch(configurationController.getConfig));


  /**
   * @swagger
   * /config/v1/update-device-configuration:
   *   post:
   *     tags: [Update Device Configuration Management]
   *     summary: Create update device configuration
   *     description: Create update device configuration in the config service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create update device configuration successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /config/v1/update-device-configuration:
   *   post:
   *     tags: [Update Device Configuration Management]
   *     summary: Create update device configuration
   *     description: Create update device configuration in the config service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create update device configuration successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/update-device-configuration', HAS_API_KEY, tryCatch(configurationController.updateConfigFromDevice));

  /**
   * @swagger
   * /config/v1/get-config:
   *   get:
   *     tags: [Get Config Management]
   *     summary: Get get config
   *     description: Retrieve get config in the config service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get config successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /config/v1/get-config:
   *   get:
   *     tags: [Get Config Management]
   *     summary: Get get config
   *     description: Retrieve get config in the config service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get config successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-config', HAS_API_KEY, tryCatch(configurationController.getConfigFromDevice));

  // configuration template APIs

  /**
   * @swagger
   * /config/v1/create-config-template:
   *   post:
   *     tags: [Create Config Template Management]
   *     summary: Create create config template
   *     description: Create create config template in the config service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create config template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /config/v1/create-config-template:
   *   post:
   *     tags: [Create Config Template Management]
   *     summary: Create create config template
   *     description: Create create config template in the config service
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Create create config template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.post('/create-config-template', IS_ADMIN, tryCatch(configurationController.createConfigTemplate));

  /**
   * @swagger
   * /config/v1/get-config-templates:
   *   get:
   *     tags: [Get Config Templates Management]
   *     summary: Get get config templates
   *     description: Retrieve get config templates in the config service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get config templates successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /config/v1/get-config-templates:
   *   get:
   *     tags: [Get Config Templates Management]
   *     summary: Get get config templates
   *     description: Retrieve get config templates in the config service
   *     security:
   *       - BearerAuth: []
   *     responses:
   *       200:
   *         description: Get get config templates successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.get('/get-config-templates', IS_ADMIN, tryCatch(configurationController.getConfigTemplates));

  /**
   * @swagger
   * /config/v1/delete-config-template/:id:
   *   delete:
   *     tags: [Delete Config Template Management]
   *     summary: Delete delete config template
   *     description: Delete delete config template in the config service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete config template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /config/v1/delete-config-template/:id:
   *   delete:
   *     tags: [Delete Config Template Management]
   *     summary: Delete delete config template
   *     description: Delete delete config template in the config service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     responses:
   *       200:
   *         description: Delete delete config template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.delete('/delete-config-template/:id', IS_ADMIN, tryCatch(configurationController.deleteConfigTemplate));

  /**
   * @swagger
   * /config/v1/edit-config-template/:id:
   *   put:
   *     tags: [Edit Config Template Management]
   *     summary: Update edit config template
   *     description: Update edit config template in the config service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update edit config template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */

  /**
   * @swagger
   * /config/v1/edit-config-template/:id:
   *   put:
   *     tags: [Edit Config Template Management]
   *     summary: Update edit config template
   *     description: Update edit config template in the config service
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/ObjectIdPath'
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Update edit config template successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  router.put('/edit-config-template/:id', IS_ADMIN, tryCatch(configurationController.editConfigTemplate));
  router.get(
    '/resync-config/:serial_number',
    HAS_API_KEY_OR_IS_ADMIN,
    tryCatch(configurationController.resyncConfiguration),
  );


  /**
   * @swagger
   * /config/v1/redis-ping:
   *   get:
   *     tags: [Redis Ping Management]
   *     summary: Get redis ping
   *     description: Retrieve redis ping in the config service
   *     responses:
   *       200:
   *         description: Get redis ping successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */

  /**
   * @swagger
   * /config/v1/redis-ping:
   *   get:
   *     tags: [Redis Ping Management]
   *     summary: Get redis ping
   *     description: Retrieve redis ping in the config service
   *     responses:
   *       200:
   *         description: Get redis ping successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SuccessResponse'
   */
  // router.get('/redis-ping', tryCatch(configurationController.checkRedisConnection));
});

module.exports = router;
