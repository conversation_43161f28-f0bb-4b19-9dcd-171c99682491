const { env } = require('../configs');
const { CONFIGURATION, CONFIGURATION_TEMPLATE, TERMINAL, BUSINESS } = require('../models');
const {
  isEmpty,
  filterQuery,
  pagination,
  sendDeviceData,
  pushDataToQueue,
  mergeTwoObjects,
  sendEmailLocally,
  sendEmail,
} = require('../utils/helper');

const redis = require('../utils/redis');
const terminalUpdate = require('../utils/templates/terminal-update');

exports.updateConfig = async (req, res) => {
  const { id, configuration, deviceGroup, touchedfields } = req.body;

  if (deviceGroup) {
    throw new Error("Can't update because device is in group:400");
  }

  const terminal = await TERMINAL.findById(id).populate({
    path: 'business_id',
    select: 'owner',
    model: BUSINESS,
  });

  if (terminal.group_id) {
    throw new Error("Can't update because device is in group:403");
  }

  const config = await CONFIGURATION.findById(terminal.configuration);

  if (!config) {
    throw new Error('Configuration Not found!:404');
  }
  await redis.remove(terminal.configuration);
  const isPayloadEmpty = isEmpty(configuration);

  if (!isPayloadEmpty) {
    const updated = await CONFIGURATION.findByIdAndUpdate(terminal.configuration, {
      $set: { configuration },
    });

    if (updated) {
      pushDataToQueue({ serial_number: terminal.serial_number, configuration: updated });
      try {
        if (env === 'development' || env === 'local') {
          await sendEmailLocally({
            template: terminalUpdate(
              `${terminal?.business_id?.owner?.first_name} ${terminal?.business_id?.owner?.last_name}`,
              Object.keys(touchedfields),
            ),
            toList: [to],
            subject: 'Report Email',
          });
        } else {
          await sendEmail({
            template: terminalUpdate(
              `${terminal?.business_id?.owner?.first_name} ${terminal?.business_id?.owner?.last_name}`,
              Object.keys(touchedfields),
            ),
            toList: [to],
            subject: 'Report Email',
          });
        }
      } catch (ex) {
        console.log(ex.message);
      }

      return res.status(200).send({
        code: 200,
        success: true,
        message: 'Configuration Updated Successfully!',
      });
    } else {
      throw new Error('Error in Updating!:304');
    }
  }
  throw new Error('Body is empty:400');
};

exports.getConfig = async (req, res) => {
  const { id } = req.params;
  const terminal_config = await CONFIGURATION.findById(id);

  if (!terminal_config) {
    throw new Error('Configuration is not found!:304');
  }

  return res.status(200).send({
    code: 200,
    success: true,
    data: terminal_config,
  });
};
exports.getConfigFromDevice = async (req, res) => {
  const { configuration } = req.terminal;

  const result = await redis.get(configuration);

  if (result) {
    return res.status(200).send({
      code: 200,
      success: true,
      data: JSON.parse(result),
    });
  } else {
    const terminal_config = await CONFIGURATION.findById(configuration);

    if (!terminal_config) {
      throw new Error('Configuration is not found!:404');
    }

    await redis.set(configuration, terminal_config, 300);

    return res.status(200).send({
      code: 200,
      success: true,
      data: terminal_config,
    });
  }
};

exports.createConfigTemplate = async (req, res) => {
  let { title, config, logo } = req.body;

  title = title.trim();

  const template = await CONFIGURATION_TEMPLATE.findOne({ title });

  if (template) {
    throw new Error('Configuration template with this title already exists:409');
  }
  logo && (config.printer.receipt_header_lines.line_1 = logo);
  await CONFIGURATION_TEMPLATE.create({ title, configuration: config });

  return res.status(200).send({
    success: true,
    message: 'Configuration Template Created Successfully',
  });
};

exports.getConfigTemplates = async (req, res) => {
  const { page, itemsPerPage, searchText, startDate, endDate } = filterQuery(req);

  const query = {
    $and: [],
    $or: [],
  };

  if (searchText && searchText !== '') {
    const regExp = new RegExp(searchText, 'i');

    query.$or = [{ title: regExp }];
  }

  if (startDate && endDate) {
    let start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    let end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    query.$and.push({ created_at: { $gte: start, $lt: end } });
  }

  if (!query.$and.length > 0) {
    delete query.$and;
  }

  if (!query.$or.length > 0) {
    delete query.$or;
  }

  const totalItems = await CONFIGURATION_TEMPLATE.countDocuments(query);

  const config_templates = await CONFIGURATION_TEMPLATE.find(query)
    .sort([['created_at', -1]])
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .lean();

  const records = pagination(config_templates, page, totalItems, itemsPerPage);

  return res.status(200).send({
    ...records,
    code: 200,
    success: true,
    message: 'All Config Templates Fetched Successfully',
  });
};

exports.deleteConfigTemplate = async (req, res) => {
  const { id } = req.params;

  const template = await CONFIGURATION_TEMPLATE.findById(id);

  if (!template) {
    throw new Error('Config Template Does Not Exists Or Already Deleted:404');
  }

  await CONFIGURATION_TEMPLATE.findByIdAndDelete(id);

  return res.status(200).send({
    success: true,
    message: 'Config Template Deleted Successfully',
  });
};

exports.editConfigTemplate = async (req, res) => {
  const { id } = req.params;
  let { configuration, title } = req.body;
  title = title.trim();
  const template = await CONFIGURATION_TEMPLATE.findById(id);

  if (!template) {
    throw new Error("Configuration Template doesn't Exists!:404");
  }

  await CONFIGURATION_TEMPLATE.findByIdAndUpdate(id, {
    configuration,
    title,
  }).catch(error => {
    if (error) {
      throw new Error(
        error?.codeName === 'DuplicateKey' ? 'Configuration Template Already Exists with this name' : error?.message,
      );
    }
  });

  return res.status(200).send({
    success: true,
    message: 'Config Template Updated Successfully',
  });
};

exports.updateConfigFromDevice = async (req, res) => {
  const { configuration } = req.body;

  if (!configuration) {
    throw new Error('Invalid Data!:400');
  }

  const terminal = await CONFIGURATION.findById(req.terminal.configuration);

  if (!terminal) {
    throw new Error('Unable To find Configuration!:404');
  }

  await redis.remove(req.terminal.configuration);

  const mergedConfiguration = mergeTwoObjects(terminal.configuration, configuration);

  await CONFIGURATION.findByIdAndUpdate(req.terminal.configuration, {
    $set: { configuration: mergedConfiguration },
  });
  return res.status(200).send({
    success: true,
    message: 'Configuration Updated Successfully!',
  });
};

exports.checkRedisConnection = async (req, res) => {
  const result = await redis.ping();

  if (!result) {
    throw new Error('Not Conneted!:500');
  }
  return res.status(200).send({
    success: true,
    message: result,
  });
};

exports.resyncConfiguration = async (req, res) => {
  const { serial_number } = req.params;

  if (!serial_number) {
    throw new Error('Serial Number is required!:400');
  }

  sendDeviceData({ serial_number, event: 'server_config_update' });

  return res.status(200).send({
    code: 200,
    success: true,
    message: 'Configuration Resynced Successfully!',
  });
};
