const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const businessSchema = new Schema(
  {
    title: {
      type: String,
      unique: true,
      required: [true, 'title is required'],
    },
    address: {
      type: String,
    },
    province: {
      type: String,
      default: 'ON',
    },
    owner: {
      first_name: String,
      last_name: String,
      email: String,
      phone_number: String,
    },
    status: {
      type: String,
      default: 'Active',
    },
    is_deleted: {
      type: Boolean,
      default: false,
    },
    deactivation_reason: {
      type: String,
      default: '',
    },
    bid: {
      type: String,
      required: [true, 'Business id is required'],
      unique: true,
    },
    mid: {
      type: String,
      required: [true, 'Merchant id is required'],
      unique: true,
    },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

businessSchema.plugin(uniqueValidator);

const BUSINESS = model('business', businessSchema);

module.exports = BUSINESS;
