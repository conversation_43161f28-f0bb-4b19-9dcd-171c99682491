const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const adminSchema = new Schema(
  {
    email: {
      type: String,
      unique: true,
      required: [true, 'email is required'],
    },
    password: {
      type: String,
      required: [true, 'password is ready'],
    },
    permissions: { type: Array, default: [] },
    token: { type: String, default: '' },
    roles: [{ type: Schema?.Types?.ObjectId, ref: 'role' }],
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

adminSchema.plugin(uniqueValidator);

const ADMIN = model('admin', adminSchema);
module.exports = ADMIN;
