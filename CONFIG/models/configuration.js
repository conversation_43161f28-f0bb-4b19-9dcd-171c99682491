const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const uniqueValidator = require('mongoose-unique-validator');

const configurationSchema = new Schema(
  {
    configuration: {},
    terminal_id: { type: Schema.Types.ObjectId, ref: 'terminal' },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  },
);

configurationSchema.plugin(uniqueValidator);

const CONFIGURATION = model('configuration', configurationSchema);

module.exports = CONFIGURATION;
