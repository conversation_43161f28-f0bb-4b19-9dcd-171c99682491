const mongoose = require('mongoose');
const uniqueValidator = require('mongoose-unique-validator');
const { Schema, model } = mongoose;

const receiptsData = new Schema(
  {
    data: Object,
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

receiptsData.plugin(uniqueValidator);

const RECEIPTS_DATA = model('receipts_data', receiptsData);
module.exports = RECEIPTS_DATA;
