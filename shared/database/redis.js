const CryptoJS = require('crypto-js');
const dbManager = require('./connection');

function hashKey(key) {
  return CryptoJS.SHA256(JSON.stringify(key)).toString();
}

const redisHelper = {
  set: async (key, data, ttl = 3600) => {
    try {
      const client = dbManager.redisClient;
      if (!client) return null;
      const hashedKey = hashKey(key);
      await client.setEx(hashedKey, ttl, JSON.stringify(data));
    } catch (err) {
      console.log('[redis.js] Error Setting Up:', err?.message);
      return null;
    }
  },
  get: async (key) => {
    try {
      const client = dbManager.redisClient;
      if (!client) return null;
      const hashedKey = hashKey(key);
      const result = await client.get(hashedKey);
      return result ? JSON.parse(result) : null;
    } catch (err) {
      console.log('[redis.js] Error Getting Value:', err?.message);
      return null;
    }
  },
  remove: async (key) => {
    try {
      const client = dbManager.redisClient;
      if (!client) return null;
      const hashedKey = hashKey(key);
      await client.del(hashedKey);
    } catch (err) {
      console.log('[redis.js] Error Deleting Value:', err?.message);
    }
  },
};

module.exports = redisHelper; 