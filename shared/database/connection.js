/**
 * Optimized MongoDB Connection Manager
 * Shared across all 8 services with connection pooling and monitoring
 */

const mongoose = require('mongoose');
const { createClient } = require('redis');

class DatabaseManager {
  constructor() {
    this.mongoConnection = null;
    this.redisClient = null;
    this.connectionOptions = {
      // Connection Pool Settings
      maxPoolSize: 50, // Maximum number of connections
      minPoolSize: 5,  // Minimum number of connections
      maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
      serverSelectionTimeoutMS: 5000, // How long to try selecting a server
      socketTimeoutMS: 45000, // How long a send or receive on a socket can take
      
      // Replica Set Settings
      readPreference: 'secondaryPreferred',
      readConcern: { level: 'majority' },
      writeConcern: { w: 'majority', j: true },
      
      // Performance Settings
      bufferMaxEntries: 0, // Disable mongoose buffering
      bufferCommands: false, // Disable mongoose buffering
      useNewUrlParser: true,
      useUnifiedTopology: true,
      
      // Monitoring
      monitorCommands: true,
    };
  }

  /**
   * Initialize MongoDB connection with optimized settings
   */
  async connectMongoDB(mongoUri, serviceName) {
    try {
      // Set mongoose global settings
      mongoose.set('strictQuery', false);
      // Log slow queries (over 200ms)
      mongoose.set('debug', function (collectionName, method, query, doc, options) {
        const start = Date.now();
        const callback = options && typeof options.callback === 'function' ? options.callback : null;
        if (callback) {
          options.callback = function () {
            const duration = Date.now() - start;
            if (duration > 200) {
              console.log(`[SLOW QUERY >200ms] ${collectionName}.${method}`, { query, doc, duration });
            }
            callback.apply(this, arguments);
          };
        }
        // For promises
        const origThen = Promise.prototype.then;
        Promise.prototype.then = function (onFulfilled, onRejected) {
          const duration = Date.now() - start;
          if (duration > 200) {
            console.log(`[SLOW QUERY >200ms] ${collectionName}.${method}`, { query, doc, duration });
          }
          return origThen.call(this, onFulfilled, onRejected);
        };
      });

      // Connect with optimized options
      this.mongoConnection = await mongoose.connect(mongoUri, this.connectionOptions);

      // Connection event handlers
      mongoose.connection.on('connected', () => {
        console.log(`✅ [${serviceName}] MongoDB connected successfully`);
      });

      mongoose.connection.on('error', (err) => {
        console.error(`❌ [${serviceName}] MongoDB connection error:`, err);
      });

      mongoose.connection.on('disconnected', () => {
        console.log(`⚠️ [${serviceName}] MongoDB disconnected`);
      });

      // Graceful shutdown
      process.on('SIGINT', async () => {
        await this.closeConnections();
        process.exit(0);
      });

      return this.mongoConnection;
    } catch (error) {
      console.error(`❌ [${serviceName}] Failed to connect to MongoDB:`, error);
      throw error;
    }
  }

  /**
   * Initialize Redis connection for caching
   */
  async connectRedis(redisUrl = 'redis://localhost:6379') {
    try {
      this.redisClient = createClient({
        url: redisUrl,
        socket: {
          connectTimeout: 5000,
          lazyConnect: true,
        },
        retry_strategy: (options) => {
          if (options.error && options.error.code === 'ECONNREFUSED') {
            return new Error('Redis server connection refused');
          }
          if (options.total_retry_time > 1000 * 60 * 60) {
            return new Error('Redis retry time exhausted');
          }
          if (options.attempt > 10) {
            return undefined;
          }
          return Math.min(options.attempt * 100, 3000);
        }
      });

      await this.redisClient.connect();
      
      this.redisClient.on('error', (err) => {
        console.error('Redis Client Error:', err);
      });

      this.redisClient.on('connect', () => {
        console.log('✅ Redis connected successfully');
      });

      return this.redisClient;
    } catch (error) {
      console.error('❌ Failed to connect to Redis:', error);
      // Don't throw error - Redis is optional for caching
      return null;
    }
  }

  /**
   * Get cached data or execute query
   */
  async getCachedOrQuery(cacheKey, queryFn, ttl = 300) {
    if (!this.redisClient) {
      return await queryFn();
    }

    try {
      const cached = await this.redisClient.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      const result = await queryFn();
      await this.redisClient.setEx(cacheKey, ttl, JSON.stringify(result));
      return result;
    } catch (error) {
      console.error('Cache error, falling back to direct query:', error);
      return await queryFn();
    }
  }

  /**
   * Invalidate cache by pattern
   */
  async invalidateCache(pattern) {
    if (!this.redisClient) return;

    try {
      const keys = await this.redisClient.keys(pattern);
      if (keys.length > 0) {
        await this.redisClient.del(keys);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  /**
   * Get connection health status
   */
  getHealthStatus() {
    return {
      mongodb: {
        status: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
        readyState: mongoose.connection.readyState,
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        name: mongoose.connection.name
      },
      redis: {
        status: this.redisClient?.isReady ? 'connected' : 'disconnected',
        isReady: this.redisClient?.isReady || false
      }
    };
  }

  /**
   * Close all connections gracefully
   */
  async closeConnections() {
    try {
      if (this.mongoConnection) {
        await mongoose.connection.close();
        console.log('✅ MongoDB connection closed');
      }
      
      if (this.redisClient) {
        await this.redisClient.quit();
        console.log('✅ Redis connection closed');
      }
    } catch (error) {
      console.error('Error closing connections:', error);
    }
  }
}

module.exports = new DatabaseManager();
