/**
 * Database Indexing Strategy for All Services
 * Optimized indexes for better query performance
 */

const mongoose = require('mongoose');

class IndexManager {
  constructor() {
    this.indexes = new Map();
  }

  /**
   * Define indexes for all collections across services
   */
  defineIndexes() {
    // Admin Collection Indexes
    this.indexes.set('admins', [
      { fields: { email: 1 }, options: { unique: true, background: true } },
      { fields: { created_at: -1 }, options: { background: true } },
      { fields: { roles: 1 }, options: { background: true } },
      { fields: { email: 'text' }, options: { background: true } }
    ]);

    // Business Collection Indexes
    this.indexes.set('businesses', [
      { fields: { bid: 1 }, options: { unique: true, background: true } },
      { fields: { mid: 1 }, options: { unique: true, sparse: true, background: true } },
      { fields: { status: 1, is_deleted: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } },
      { fields: { title: 'text', 'owner.first_name': 'text', 'owner.last_name': 'text' }, options: { background: true } },
      { fields: { 'owner.email': 1 }, options: { background: true } }
    ]);

    // Store Collection Indexes
    this.indexes.set('stores', [
      { fields: { business_id: 1, is_deleted: 1 }, options: { background: true } },
      { fields: { status: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } },
      { fields: { title: 'text', 'address.formatted_address': 'text' }, options: { background: true } }
    ]);

    // Terminal Collection Indexes
    this.indexes.set('terminals', [
      { fields: { device_id: 1 }, options: { unique: true, background: true } },
      { fields: { tid: 1 }, options: { unique: true, background: true } },
      { fields: { api_key: 1 }, options: { unique: true, sparse: true, background: true } },
      { fields: { store_id: 1, business_id: 1 }, options: { background: true } },
      { fields: { status: 1, is_deleted: 1 }, options: { background: true } },
      { fields: { mac_address: 1, serial_number: 1 }, options: { background: true } },
      { fields: { created_at: -1, updated_at: -1 }, options: { background: true } },
      { fields: { title: 'text', device_id: 'text', serial_number: 'text' }, options: { background: true } }
    ]);

    // User Collection Indexes
    this.indexes.set('users', [
      { fields: { clerk_id: 1 }, options: { unique: true, background: true } },
      { fields: { email: 1 }, options: { sparse: true, background: true } },
      { fields: { store_id: 1, business_id: 1 }, options: { background: true } },
      { fields: { type: 1, status: 1, is_deleted: 1 }, options: { background: true } },
      { fields: { group_id: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } },
      { fields: { first_name: 'text', last_name: 'text', email: 'text' }, options: { background: true } }
    ]);

    // User Groups Collection Indexes
    this.indexes.set('user_groups', [
      { fields: { store_id: 1, business_id: 1 }, options: { background: true } },
      { fields: { status: 1, is_deleted: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } },
      { fields: { name: 'text' }, options: { background: true } }
    ]);

    // Terminal Groups Collection Indexes
    this.indexes.set('terminal_groups', [
      { fields: { store_id: 1, business_id: 1 }, options: { background: true } },
      { fields: { user_group_id: 1 }, options: { background: true } },
      { fields: { status: 1, is_deleted: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } },
      { fields: { name: 'text' }, options: { background: true } }
    ]);

    // Configuration Collection Indexes
    this.indexes.set('configurations', [
      { fields: { store_id: 1, business_id: 1 }, options: { background: true } },
      { fields: { is_deleted: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } }
    ]);

    // Transactions Collection Indexes (for reporting)
    this.indexes.set('transactions', [
      { fields: { terminal_id: 1, created_at: -1 }, options: { background: true } },
      { fields: { store_id: 1, business_id: 1 }, options: { background: true } },
      { fields: { transaction_type: 1, status: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } },
      { fields: { clerk_id: 1, server_id: 1 }, options: { background: true } },
      { fields: { card_brand: 1 }, options: { background: true } }
    ]);

    // Device Logs Collection Indexes
    this.indexes.set('device_logs', [
      { fields: { terminal_id: 1, created_at: -1 }, options: { background: true } },
      { fields: { log_type: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { expireAfterSeconds: 2592000, background: true } } // 30 days TTL
    ]);

    // Admin JWT Collection Indexes
    this.indexes.set('admin_jwts', [
      { fields: { admin_id: 1 }, options: { background: true } },
      { fields: { token: 1 }, options: { unique: true, background: true } },
      { fields: { exp: 1 }, options: { expireAfterSeconds: 0, background: true } } // TTL based on exp field
    ]);

    // Roles and Permissions Indexes
    this.indexes.set('roles', [
      { fields: { type: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } }
    ]);

    this.indexes.set('permissions', [
      { fields: { can: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } }
    ]);

    // Certificates Collection Indexes
    this.indexes.set('certificates', [
      { fields: { terminal_id: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } }
    ]);

    // Message Queue Collection Indexes
    this.indexes.set('message_queues', [
      { fields: { terminal_id: 1, processed: 1 }, options: { background: true } },
      { fields: { created_at: -1 }, options: { background: true } },
      { fields: { created_at: 1 }, options: { expireAfterSeconds: 86400, background: true } } // 24 hours TTL
    ]);
  }

  /**
   * Create all indexes for a specific database
   */
  async createIndexes(serviceName) {
    try {
      console.log(`🔧 Creating indexes for ${serviceName} service...`);
      
      const db = mongoose.connection.db;
      const collections = await db.listCollections().toArray();
      const existingCollections = collections.map(col => col.name);

      for (const [collectionName, indexes] of this.indexes) {
        if (!existingCollections.includes(collectionName)) {
          console.log(`⚠️ Collection ${collectionName} doesn't exist, skipping...`);
          continue;
        }

        const collection = db.collection(collectionName);
        
        for (const indexDef of indexes) {
          try {
            await collection.createIndex(indexDef.fields, indexDef.options);
            console.log(`✅ Created index on ${collectionName}:`, Object.keys(indexDef.fields).join(', '));
          } catch (error) {
            if (error.code === 85) { // Index already exists
              console.log(`ℹ️ Index already exists on ${collectionName}:`, Object.keys(indexDef.fields).join(', '));
            } else {
              console.error(`❌ Failed to create index on ${collectionName}:`, error.message);
            }
          }
        }
      }

      console.log(`✅ Index creation completed for ${serviceName} service`);
    } catch (error) {
      console.error(`❌ Error creating indexes for ${serviceName}:`, error);
      throw error;
    }
  }

  /**
   * Get index statistics for monitoring
   */
  async getIndexStats() {
    try {
      const db = mongoose.connection.db;
      const stats = {};

      for (const collectionName of this.indexes.keys()) {
        try {
          const collection = db.collection(collectionName);
          const indexStats = await collection.indexStats().toArray();
          stats[collectionName] = indexStats;
        } catch (error) {
          stats[collectionName] = { error: error.message };
        }
      }

      return stats;
    } catch (error) {
      console.error('Error getting index stats:', error);
      return { error: error.message };
    }
  }

  /**
   * Analyze slow queries and suggest optimizations
   */
  async analyzeSlowQueries() {
    try {
      const db = mongoose.connection.db;
      
      // Enable profiling for slow operations (>100ms)
      await db.admin().command({ profile: 2, slowms: 100 });
      
      // Get profiling data
      const profilingData = await db.collection('system.profile')
        .find({})
        .sort({ ts: -1 })
        .limit(50)
        .toArray();

      return profilingData.map(op => ({
        namespace: op.ns,
        operation: op.op,
        duration: op.millis,
        timestamp: op.ts,
        command: op.command
      }));
    } catch (error) {
      console.error('Error analyzing slow queries:', error);
      return [];
    }
  }
}

module.exports = new IndexManager();
