/**
 * Query Optimization Utilities
 * Optimized query patterns and pagination for all services
 */

const mongoose = require('mongoose');

class QueryOptimizer {
  constructor() {
    this.defaultPageSize = 20;
    this.maxPageSize = 100;
  }

  /**
   * Optimized pagination using cursor-based approach for large datasets
   */
  async paginateWithCursor(Model, query = {}, options = {}) {
    const {
      cursor,
      limit = this.defaultPageSize,
      sortField = '_id',
      sortOrder = 1,
      populate = [],
      select = '',
      lean = true
    } = options;

    const actualLimit = Math.min(limit, this.maxPageSize);
    
    // Build cursor query
    if (cursor) {
      const cursorQuery = sortOrder === 1 
        ? { [sortField]: { $gt: cursor } }
        : { [sortField]: { $lt: cursor } };
      
      query = { $and: [query, cursorQuery] };
    }

    let queryBuilder = Model.find(query)
      .sort({ [sortField]: sortOrder })
      .limit(actualLimit + 1); // Get one extra to check if there's a next page

    if (select) queryBuilder = queryBuilder.select(select);
    if (lean) queryBuilder = queryBuilder.lean();
    
    // Apply population
    populate.forEach(pop => {
      queryBuilder = queryBuilder.populate(pop);
    });

    const results = await queryBuilder.exec();
    const hasNextPage = results.length > actualLimit;
    
    if (hasNextPage) {
      results.pop(); // Remove the extra item
    }

    const nextCursor = hasNextPage && results.length > 0 
      ? results[results.length - 1][sortField] 
      : null;

    return {
      data: results,
      hasNextPage,
      nextCursor,
      count: results.length
    };
  }

  /**
   * Optimized offset-based pagination with count optimization
   */
  async paginateWithOffset(Model, query = {}, options = {}) {
    const {
      page = 1,
      limit = this.defaultPageSize,
      sort = { _id: -1 },
      populate = [],
      select = '',
      lean = true,
      useEstimatedCount = false
    } = options;

    const actualLimit = Math.min(limit, this.maxPageSize);
    const skip = (page - 1) * actualLimit;

    // Parallel execution of count and data queries
    const [totalCount, results] = await Promise.all([
      useEstimatedCount 
        ? Model.estimatedDocumentCount()
        : Model.countDocuments(query),
      this.buildQuery(Model, query, {
        skip,
        limit: actualLimit,
        sort,
        populate,
        select,
        lean
      })
    ]);

    const totalPages = Math.ceil(totalCount / actualLimit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      data: results,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        nextPage: hasNextPage ? page + 1 : null,
        prevPage: hasPrevPage ? page - 1 : null,
        limit: actualLimit
      }
    };
  }

  /**
   * Build optimized query with all options
   */
  async buildQuery(Model, query, options = {}) {
    const {
      skip = 0,
      limit = this.defaultPageSize,
      sort = { _id: -1 },
      populate = [],
      select = '',
      lean = true
    } = options;

    let queryBuilder = Model.find(query);

    if (sort) queryBuilder = queryBuilder.sort(sort);
    if (skip > 0) queryBuilder = queryBuilder.skip(skip);
    if (limit > 0) queryBuilder = queryBuilder.limit(limit);
    if (select) queryBuilder = queryBuilder.select(select);
    if (lean) queryBuilder = queryBuilder.lean();

    // Apply population efficiently
    populate.forEach(pop => {
      if (typeof pop === 'string') {
        queryBuilder = queryBuilder.populate(pop);
      } else {
        queryBuilder = queryBuilder.populate(pop);
      }
    });

    return await queryBuilder.exec();
  }

  /**
   * Optimized search query builder with text indexes
   */
  buildSearchQuery(searchText, searchFields = []) {
    if (!searchText || searchText.trim() === '') {
      return {};
    }

    const trimmedSearch = searchText.trim();
    
    // If search fields support text index, use text search
    const textSearchQuery = {
      $text: { $search: trimmedSearch }
    };

    // Fallback to regex search for specific fields
    const regexSearchQuery = {
      $or: searchFields.map(field => ({
        [field]: { $regex: trimmedSearch, $options: 'i' }
      }))
    };

    // Try text search first, fallback to regex
    return {
      $or: [textSearchQuery, regexSearchQuery]
    };
  }

  /**
   * Optimized date range query
   */
  buildDateRangeQuery(startDate, endDate, dateField = 'created_at') {
    if (!startDate && !endDate) {
      return {};
    }

    const query = {};
    
    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query[dateField] = { $gte: start };
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query[dateField] = { ...query[dateField], $lte: end };
    }

    return query;
  }

  /**
   * Optimized aggregation pipeline for complex queries
   */
  async aggregateWithOptimization(Model, pipeline, options = {}) {
    const {
      allowDiskUse = true,
      maxTimeMS = 30000,
      hint = null
    } = options;

    const aggregationOptions = {
      allowDiskUse,
      maxTimeMS
    };

    if (hint) {
      aggregationOptions.hint = hint;
    }

    // Add $facet for pagination in aggregation
    if (options.paginate) {
      const { page = 1, limit = this.defaultPageSize } = options.paginate;
      const skip = (page - 1) * limit;

      pipeline.push({
        $facet: {
          data: [
            { $skip: skip },
            { $limit: limit }
          ],
          count: [
            { $count: 'total' }
          ]
        }
      });
    }

    return await Model.aggregate(pipeline, aggregationOptions);
  }

  /**
   * Bulk operations optimizer
   */
  async bulkWrite(Model, operations, options = {}) {
    const {
      ordered = false,
      batchSize = 1000
    } = options;

    if (operations.length === 0) {
      return { acknowledged: true, insertedCount: 0, modifiedCount: 0 };
    }

    // Process in batches to avoid memory issues
    const batches = [];
    for (let i = 0; i < operations.length; i += batchSize) {
      batches.push(operations.slice(i, i + batchSize));
    }

    let totalResult = {
      acknowledged: true,
      insertedCount: 0,
      modifiedCount: 0,
      deletedCount: 0,
      upsertedCount: 0
    };

    for (const batch of batches) {
      try {
        const result = await Model.bulkWrite(batch, { ordered });
        
        totalResult.insertedCount += result.insertedCount || 0;
        totalResult.modifiedCount += result.modifiedCount || 0;
        totalResult.deletedCount += result.deletedCount || 0;
        totalResult.upsertedCount += result.upsertedCount || 0;
      } catch (error) {
        console.error('Bulk write batch error:', error);
        if (ordered) throw error;
      }
    }

    return totalResult;
  }

  /**
   * Query performance analyzer
   */
  async explainQuery(Model, query, options = {}) {
    try {
      const explanation = await Model.find(query).explain('executionStats');
      
      return {
        executionTimeMillis: explanation.executionStats.executionTimeMillis,
        totalDocsExamined: explanation.executionStats.totalDocsExamined,
        totalDocsReturned: explanation.executionStats.totalDocsReturned,
        indexesUsed: explanation.executionStats.executionStages?.indexName || 'COLLSCAN',
        isOptimal: explanation.executionStats.totalDocsExamined === explanation.executionStats.totalDocsReturned
      };
    } catch (error) {
      console.error('Query explanation error:', error);
      return null;
    }
  }

  /**
   * Memory-efficient streaming for large datasets
   */
  createStream(Model, query = {}, options = {}) {
    const {
      batchSize = 100,
      sort = { _id: 1 },
      select = '',
      populate = []
    } = options;

    let queryBuilder = Model.find(query)
      .sort(sort)
      .batchSize(batchSize);

    if (select) queryBuilder = queryBuilder.select(select);
    
    populate.forEach(pop => {
      queryBuilder = queryBuilder.populate(pop);
    });

    return queryBuilder.cursor();
  }
}

module.exports = new QueryOptimizer();
