/**
 * Performance Monitoring Middleware
 * Tracks request performance, database queries, and system metrics
 */

const os = require('os');
const process = require('process');

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      requests: new Map(),
      dbQueries: new Map(),
      errors: new Map(),
      systemMetrics: {}
    };
    
    // Start system metrics collection
    this.startSystemMetricsCollection();
  }

  /**
   * Request performance tracking middleware
   */
  trackRequest() {
    return (req, res, next) => {
      const startTime = Date.now();
      const startMemory = process.memoryUsage();
      
      // Generate unique request ID
      req.requestId = this.generateRequestId();
      
      // Track request start
      this.metrics.requests.set(req.requestId, {
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        startTime,
        startMemory
      });

      // Override res.end to capture response metrics
      const originalEnd = res.end;
      res.end = (...args) => {
        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        const duration = endTime - startTime;
        
        // Update request metrics
        const requestMetric = this.metrics.requests.get(req.requestId);
        if (requestMetric) {
          requestMetric.endTime = endTime;
          requestMetric.duration = duration;
          requestMetric.statusCode = res.statusCode;
          requestMetric.memoryDelta = {
            rss: endMemory.rss - startMemory.rss,
            heapUsed: endMemory.heapUsed - startMemory.heapUsed,
            heapTotal: endMemory.heapTotal - startMemory.heapTotal
          };
          
          // Log slow requests
          if (duration > 1000) { // > 1 second
            console.warn(`🐌 Slow request detected: ${req.method} ${req.url} - ${duration}ms`);
          }
          
          // Log high memory usage
          if (requestMetric.memoryDelta.heapUsed > 50 * 1024 * 1024) { // > 50MB
            console.warn(`🧠 High memory usage: ${req.method} ${req.url} - ${Math.round(requestMetric.memoryDelta.heapUsed / 1024 / 1024)}MB`);
          }
        }
        
        originalEnd.apply(res, args);
      };

      next();
    };
  }

  /**
   * Database query performance tracking
   */
  trackDbQuery(operation, collection, query, duration) {
    const key = `${operation}:${collection}`;
    
    if (!this.metrics.dbQueries.has(key)) {
      this.metrics.dbQueries.set(key, {
        operation,
        collection,
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: Infinity,
        slowQueries: []
      });
    }
    
    const metric = this.metrics.dbQueries.get(key);
    metric.count++;
    metric.totalDuration += duration;
    metric.avgDuration = metric.totalDuration / metric.count;
    metric.maxDuration = Math.max(metric.maxDuration, duration);
    metric.minDuration = Math.min(metric.minDuration, duration);
    
    // Track slow queries
    if (duration > 100) { // > 100ms
      metric.slowQueries.push({
        query: JSON.stringify(query).substring(0, 200), // Truncate long queries
        duration,
        timestamp: new Date().toISOString()
      });
      
      // Keep only last 10 slow queries
      if (metric.slowQueries.length > 10) {
        metric.slowQueries.shift();
      }
    }
  }

  /**
   * Error tracking
   */
  trackError(error, context = {}) {
    const errorKey = error.name || 'UnknownError';
    
    if (!this.metrics.errors.has(errorKey)) {
      this.metrics.errors.set(errorKey, {
        name: errorKey,
        count: 0,
        lastOccurrence: null,
        contexts: []
      });
    }
    
    const errorMetric = this.metrics.errors.get(errorKey);
    errorMetric.count++;
    errorMetric.lastOccurrence = new Date().toISOString();
    errorMetric.contexts.push({
      message: error.message,
      stack: error.stack?.substring(0, 500), // Truncate stack trace
      context,
      timestamp: new Date().toISOString()
    });
    
    // Keep only last 5 error contexts
    if (errorMetric.contexts.length > 5) {
      errorMetric.contexts.shift();
    }
  }

  /**
   * System metrics collection
   */
  startSystemMetricsCollection() {
    setInterval(() => {
      this.metrics.systemMetrics = {
        timestamp: new Date().toISOString(),
        memory: process.memoryUsage(),
        cpu: {
          usage: process.cpuUsage(),
          loadAverage: os.loadavg()
        },
        system: {
          platform: os.platform(),
          arch: os.arch(),
          uptime: os.uptime(),
          freeMemory: os.freemem(),
          totalMemory: os.totalmem()
        },
        process: {
          pid: process.pid,
          uptime: process.uptime(),
          version: process.version,
          versions: process.versions
        }
      };
    }, 30000); // Update every 30 seconds
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    
    // Filter recent requests (last hour)
    const recentRequests = Array.from(this.metrics.requests.values())
      .filter(req => req.startTime > oneHourAgo);
    
    // Calculate request statistics
    const requestStats = this.calculateRequestStats(recentRequests);
    
    // Get database query statistics
    const dbStats = Array.from(this.metrics.dbQueries.entries()).map(([key, value]) => ({
      operation: key,
      ...value
    }));
    
    // Get error statistics
    const errorStats = Array.from(this.metrics.errors.entries()).map(([key, value]) => ({
      error: key,
      ...value
    }));
    
    return {
      timestamp: new Date().toISOString(),
      requests: requestStats,
      database: dbStats,
      errors: errorStats,
      system: this.metrics.systemMetrics
    };
  }

  /**
   * Calculate request statistics
   */
  calculateRequestStats(requests) {
    if (requests.length === 0) {
      return {
        total: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: 0,
        statusCodes: {},
        slowRequests: []
      };
    }
    
    const durations = requests.map(req => req.duration).filter(d => d !== undefined);
    const statusCodes = {};
    const slowRequests = [];
    
    requests.forEach(req => {
      // Count status codes
      const status = req.statusCode || 'unknown';
      statusCodes[status] = (statusCodes[status] || 0) + 1;
      
      // Track slow requests
      if (req.duration > 1000) {
        slowRequests.push({
          method: req.method,
          url: req.url,
          duration: req.duration,
          statusCode: req.statusCode,
          timestamp: new Date(req.startTime).toISOString()
        });
      }
    });
    
    return {
      total: requests.length,
      avgDuration: durations.length > 0 ? Math.round(durations.reduce((a, b) => a + b, 0) / durations.length) : 0,
      maxDuration: durations.length > 0 ? Math.max(...durations) : 0,
      minDuration: durations.length > 0 ? Math.min(...durations) : 0,
      statusCodes,
      slowRequests: slowRequests.slice(-10) // Last 10 slow requests
    };
  }

  /**
   * Generate unique request ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clear old metrics to prevent memory leaks
   */
  cleanup() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    
    // Clean up old requests
    for (const [id, request] of this.metrics.requests.entries()) {
      if (request.startTime < oneHourAgo) {
        this.metrics.requests.delete(id);
      }
    }
  }

  /**
   * Start periodic cleanup
   */
  startCleanup() {
    setInterval(() => {
      this.cleanup();
    }, 10 * 60 * 1000); // Cleanup every 10 minutes
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();
performanceMonitor.startCleanup();

module.exports = performanceMonitor;
