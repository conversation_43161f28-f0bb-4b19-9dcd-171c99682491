/**
 * Optimized Controller Base Class
 * Provides caching, query optimization, and performance monitoring
 */

const DatabaseManager = require('../database/connection');
const QueryOptimizer = require('../database/queryOptimizer');

class OptimizedController {
  constructor(Model, modelName) {
    this.Model = Model;
    this.modelName = modelName;
    this.cachePrefix = modelName.toLowerCase();
  }

  /**
   * Optimized get all with caching and cursor pagination
   */
  async getAll(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        cursor,
        searchText,
        startDate,
        endDate,
        useCursor = false,
        ...filters
      } = req.query;

      // Build cache key
      const cacheKey = `${this.cachePrefix}:list:${JSON.stringify(req.query)}`;

      // Build query
      let query = this.buildFilterQuery(filters);
      
      // Add search query
      if (searchText) {
        const searchQuery = QueryOptimizer.buildSearchQuery(searchText, this.getSearchFields());
        query = { $and: [query, searchQuery] };
      }

      // Add date range query
      if (startDate || endDate) {
        const dateQuery = QueryOptimizer.buildDateRangeQuery(startDate, endDate);
        query = { $and: [query, dateQuery] };
      }

      // Clean up empty $and arrays
      if (query.$and && query.$and.length === 0) {
        delete query.$and;
      }

      // Execute query with caching
      const result = await DatabaseManager.getCachedOrQuery(
        cacheKey,
        async () => {
          if (useCursor === 'true') {
            return await QueryOptimizer.paginateWithCursor(this.Model, query, {
              cursor,
              limit: parseInt(limit),
              populate: this.getPopulateFields(),
              select: this.getSelectFields(),
              sortField: this.getSortField(),
              sortOrder: this.getSortOrder()
            });
          } else {
            return await QueryOptimizer.paginateWithOffset(this.Model, query, {
              page: parseInt(page),
              limit: parseInt(limit),
              populate: this.getPopulateFields(),
              select: this.getSelectFields(),
              sort: this.getSortOptions()
            });
          }
        },
        300 // 5 minutes cache
      );

      res.status(200).json({
        code: 200,
        success: true,
        message: `${this.modelName} fetched successfully`,
        ...result
      });
    } catch (error) {
      console.error(`Error in ${this.modelName} getAll:`, error);
      res.status(500).json({
        code: 500,
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Optimized get by ID with caching
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      const cacheKey = `${this.cachePrefix}:${id}`;

      const result = await DatabaseManager.getCachedOrQuery(
        cacheKey,
        async () => {
          let query = this.Model.findById(id);
          
          const populateFields = this.getPopulateFields();
          populateFields.forEach(field => {
            query = query.populate(field);
          });

          const selectFields = this.getSelectFields();
          if (selectFields) {
            query = query.select(selectFields);
          }

          return await query.lean().exec();
        },
        600 // 10 minutes cache
      );

      if (!result) {
        return res.status(404).json({
          code: 404,
          success: false,
          message: `${this.modelName} not found`
        });
      }

      res.status(200).json({
        code: 200,
        success: true,
        message: `${this.modelName} fetched successfully`,
        data: result
      });
    } catch (error) {
      console.error(`Error in ${this.modelName} getById:`, error);
      res.status(500).json({
        code: 500,
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Optimized create with cache invalidation
   */
  async create(req, res) {
    try {
      const data = req.body;
      
      // Validate data
      const validationError = this.validateCreateData(data);
      if (validationError) {
        return res.status(400).json({
          code: 400,
          success: false,
          message: validationError
        });
      }

      const result = await this.Model.create(data);

      // Invalidate related caches
      await this.invalidateCache(['list', result._id.toString()]);

      res.status(201).json({
        code: 201,
        success: true,
        message: `${this.modelName} created successfully`,
        data: result
      });
    } catch (error) {
      console.error(`Error in ${this.modelName} create:`, error);
      
      if (error.code === 11000) {
        return res.status(409).json({
          code: 409,
          success: false,
          message: 'Duplicate entry detected'
        });
      }

      res.status(500).json({
        code: 500,
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Optimized update with cache invalidation
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      // Validate data
      const validationError = this.validateUpdateData(data);
      if (validationError) {
        return res.status(400).json({
          code: 400,
          success: false,
          message: validationError
        });
      }

      const result = await this.Model.findByIdAndUpdate(
        id,
        { $set: data },
        { new: true, runValidators: true }
      ).lean();

      if (!result) {
        return res.status(404).json({
          code: 404,
          success: false,
          message: `${this.modelName} not found`
        });
      }

      // Invalidate related caches
      await this.invalidateCache(['list', id]);

      res.status(200).json({
        code: 200,
        success: true,
        message: `${this.modelName} updated successfully`,
        data: result
      });
    } catch (error) {
      console.error(`Error in ${this.modelName} update:`, error);
      res.status(500).json({
        code: 500,
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Optimized delete with cache invalidation
   */
  async delete(req, res) {
    try {
      const { id } = req.params;

      const result = await this.Model.findByIdAndDelete(id);

      if (!result) {
        return res.status(404).json({
          code: 404,
          success: false,
          message: `${this.modelName} not found`
        });
      }

      // Invalidate related caches
      await this.invalidateCache(['list', id]);

      res.status(200).json({
        code: 200,
        success: true,
        message: `${this.modelName} deleted successfully`
      });
    } catch (error) {
      console.error(`Error in ${this.modelName} delete:`, error);
      res.status(500).json({
        code: 500,
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // Override these methods in child classes
  buildFilterQuery(filters) { return {}; }
  getSearchFields() { return []; }
  getPopulateFields() { return []; }
  getSelectFields() { return ''; }
  getSortField() { return '_id'; }
  getSortOrder() { return -1; }
  getSortOptions() { return { _id: -1 }; }
  validateCreateData(data) { return null; }
  validateUpdateData(data) { return null; }

  /**
   * Invalidate cache patterns
   */
  async invalidateCache(patterns) {
    for (const pattern of patterns) {
      await DatabaseManager.invalidateCache(`${this.cachePrefix}:${pattern}*`);
    }
  }
}

module.exports = OptimizedController;
