version: '3'

services:
  redis:
    image: redis
    ports:
      - '6379:6379'

  mongo:
    image: mongo
    ports:
      - '27017:27017'

  admin:
    build:
      context: ./ADMIN
      dockerfile: Dockerfile
    ports:
      - '4000:4000'
    depends_on:
      - mongo
    environment:
      - MONGO_URI=mongodb://mongo:27017/psp-pos

  terminal:
    build:
      context: ./TERMINAL
      dockerfile: Dockerfile
    ports:
      - '4001:4001'
    depends_on:
      - mongo
    environment:
      - MONGO_URI=mongodb://mongo:27017/psp-pos

  store:
    build:
      context: ./STORE
      dockerfile: Dockerfile
    ports:
      - '4002:4002'
    depends_on:
      - mongo
    environment:
      - MONGO_URI=mongodb://mongo:27017/psp-pos

  business:
    build:
      context: ./BUSINESS
      dockerfile: Dockerfile
    ports:
      - '4003:4003'
    depends_on:
      - mongo
    environment:
      - MONGO_URI=mongodb://mongo:27017/psp-pos

  users:
    build:
      context: ./USERS
      dockerfile: Dockerfile
    ports:
      - '4004:4004'
    depends_on:
      - mongo
    environment:
      - MONGO_URI=mongodb://mongo:27017/psp-pos

  common:
    build:
      context: ./COMMON
      dockerfile: Dockerfile
    ports:
      - '4005:4005'
    depends_on:
      - mongo
    environment:
      - MONGO_URI=mongodb://mongo:27017/psp-pos

  config:
    build:
      context: ./CONFIG
      dockerfile: Dockerfile
    ports:
      - '4006:4006'
    depends_on:
      - mongo
    environment:
      - MONGO_URI=mongodb://mongo:27017/psp-pos

  amex:
    build:
      context: ./AMEX
      dockerfile: Dockerfile
    ports:
      - '4008:4008'
    depends_on:
      - mongo
    environment:
      - MONGO_URI=mongodb://mongo:27017/psp-pos
